#!/usr/bin/env python3
"""
验证规则生成器 - 自动生成和更新验证规则

该工具用于自动生成 pyAHC 特定的验证规则，支持：
- 组件验证规则生成
- 依赖关系规则生成
- 参数外部化配置生成
- 与 pySWAP-main 兼容的规则结构
"""

import yaml
from typing import Dict as _Dict, Any as _Any, List as _List
from pathlib import Path


class ValidationRuleGenerator:
    """验证规则生成器"""

    def __init__(self):
        self.rules = {}

    def add_component_rule(self, component_name: str, switch_param: str,
                          switch_value: _Any, required_params: _List[str],
                          validation_expressions: _List[str]):
        """添加组件验证规则"""
        if component_name not in self.rules:
            self.rules[component_name] = {}

        if switch_param not in self.rules[component_name]:
            self.rules[component_name][switch_param] = {}

        self.rules[component_name][switch_param][switch_value] = {
            'required': required_params,
            'validation': validation_expressions
        }

    def add_dependency_rule(self, component_name: str, depends_on_component: str,
                           conditions: _List[str]):
        """添加依赖关系规则"""
        if 'ComponentDependencies' not in self.rules:
            self.rules['ComponentDependencies'] = {}

        if component_name not in self.rules['ComponentDependencies']:
            self.rules['ComponentDependencies'][component_name] = {'depends_on': []}

        self.rules['ComponentDependencies'][component_name]['depends_on'].append({
            'component': depends_on_component,
            'conditions': conditions
        })

    def add_parameter_externalization(self, component_name: str, param_name: str,
                                    default_value: _Any, value_range: _List[_Any],
                                    description: str):
        """添加参数外部化配置"""
        if 'ParameterExternalization' not in self.rules:
            self.rules['ParameterExternalization'] = {}

        if component_name not in self.rules['ParameterExternalization']:
            self.rules['ParameterExternalization'][component_name] = {
                'configurable_params': []
            }

        param_config = {
            'name': param_name,
            'default': default_value,
            'range': value_range,
            'description': description
        }

        self.rules['ParameterExternalization'][component_name]['configurable_params'].append(param_config)

    def generate_yaml(self, output_path: str):
        """生成 YAML 文件"""
        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.rules, f, default_flow_style=False, allow_unicode=True)

    def merge_with_existing(self, existing_yaml_path: str):
        """与现有 YAML 文件合并"""
        try:
            with open(existing_yaml_path, 'r', encoding='utf-8') as f:
                existing_rules = yaml.safe_load(f)
            
            # 合并规则，新规则优先
            for key, value in self.rules.items():
                if key in existing_rules:
                    if isinstance(value, dict) and isinstance(existing_rules[key], dict):
                        existing_rules[key].update(value)
                    else:
                        existing_rules[key] = value
                else:
                    existing_rules[key] = value
            
            self.rules = existing_rules
        except FileNotFoundError:
            print(f"警告: 文件 {existing_yaml_path} 不存在，将创建新文件")


def generate_ahc_validation_rules():
    """生成 AHC 特定的验证规则"""
    generator = ValidationRuleGenerator()

    # BottomBoundary 验证规则
    generator.add_component_rule(
        'BottomBoundary', 'swbotb', 1,
        ['gwlevel_data'],
        ['len(gwlevel_data) > 0', 'all(isinstance(item, tuple) and len(item) == 3 for item in gwlevel_data)']
    )

    generator.add_component_rule(
        'BottomBoundary', 'swbotb', 3,
        ['shape', 'hdrain', 'rimlay', 'aqave', 'aqamp', 'aqtamx', 'aqper'],
        ['shape > 0.0 and shape <= 10.0', 'hdrain < 0.0', 'rimlay > 0.0', 'aqper > 0.0']
    )

    generator.add_component_rule(
        'BottomBoundary', 'swbotb', 4,
        ['cofqha', 'cofqhb', 'cofqhc'],
        ['cofqha is not None', 'cofqhb is not None', 'cofqhc is not None']
    )

    # EpicCrop 验证规则
    generator.add_component_rule(
        'EpicCrop', 'swcf', 1,
        ['cftb'],
        ['cftb is not None']
    )

    generator.add_component_rule(
        'EpicCrop', 'swcf', 2,
        ['chtb', 'cfini'],
        ['chtb is not None', 'cfini >= 0.0 and cfini <= 500.0']
    )

    generator.add_component_rule(
        'EpicCrop', 'swcf', 3,
        ['cfini'],
        ['cfini >= 0.0 and cfini <= 500.0']
    )

    generator.add_component_rule(
        'EpicCrop', 'swrsc', 1,
        ['rsc', 'frgmax', 'vpdfr'],
        ['rsc > 0.0', 'frgmax >= 0.0 and frgmax <= 1.0', 'vpdfr > 0.0']
    )

    generator.add_component_rule(
        'EpicCrop', 'swrsc', 2,
        ['rsctb', 'frgmax', 'vpdfr'],
        ['rsctb is not None', 'frgmax >= 0.0 and frgmax <= 1.0', 'vpdfr > 0.0']
    )

    # CtrFile 验证规则
    generator.add_component_rule(
        'CtrFile', 'swdra', 1,
        ['drfil'],
        ['drfil is not None and len(drfil) > 0']
    )

    generator.add_component_rule(
        'CtrFile', 'swsolu', 1,
        ['swstyp', 'bbcfil'],
        ['swstyp in [0, 1]', 'bbcfil is not None and len(bbcfil) > 0']
    )

    # 依赖关系规则
    generator.add_dependency_rule(
        'EpicCrop', 'CtrFile',
        ['project is not None', 'ssrun is not None', 'esrun is not None']
    )

    generator.add_dependency_rule(
        'BottomBoundary', 'CtrFile',
        ['swsolu is not None', 'swhea is not None']
    )

    generator.add_dependency_rule(
        'ObservationPoints', 'CtrFile',
        ['project is not None']
    )

    # 参数外部化配置
    # BottomBoundary 参数
    boundary_params = [
        ('shape', 1.0, [0.1, 10.0], '形状因子'),
        ('hdrain', -120.0, [-1000.0, 0.0], '排水基础'),
        ('rimlay', 500.0, [1.0, 10000.0], '隔水层垂直阻力'),
        ('aqave', 0.0, [-1000.0, 1000.0], '平均水力水头差'),
        ('aqamp', 0.0, [0.0, 1000.0], '水力水头正弦波振幅'),
        ('aqper', 365.0, [1.0, 3650.0], '水力水头正弦波周期')
    ]

    for name, default, range_val, desc in boundary_params:
        generator.add_parameter_externalization('BottomBoundary', name, default, range_val, desc)

    # EpicCrop 参数
    crop_params = [
        ('tba', 9.0, [-10.0, 50.0], '基础温度'),
        ('topt', 25.0, [0.0, 50.0], '最适温度'),
        ('ruep', 47.0, [1.0, 100.0], '辐射利用效率'),
        ('chmax', 265.0, [10.0, 1000.0], '最大冠层高度')
    ]

    for name, default, range_val, desc in crop_params:
        generator.add_parameter_externalization('EpicCrop', name, default, range_val, desc)

    # ObservationPoints 参数
    obs_params = [
        ('repeat_count', 2, [1, 10], '重复次数'),
        ('min_depth_difference', 1, [1, 100], '最小深度差异'),
        ('max_depth', 1000, [100, 10000], '最大深度')
    ]

    for name, default, range_val, desc in obs_params:
        generator.add_parameter_externalization('ObservationPoints', name, default, range_val, desc)

    # 生成文件
    output_path = 'pyahc/core/validation_generated.yaml'
    generator.generate_yaml(output_path)
    print(f"✓ 验证规则生成完成: {output_path}")

    return generator


def update_existing_validation():
    """更新现有的 validation.yaml 文件"""
    generator = generate_ahc_validation_rules()
    
    # 与现有文件合并
    existing_path = 'pyahc/core/validation.yaml'
    generator.merge_with_existing(existing_path)

    # 写入更新后的文件
    generator.generate_yaml(existing_path)
    print(f"✓ 验证规则已更新: {existing_path}")


if __name__ == "__main__":
    print("=== pyAHC 验证规则生成器 ===\n")
    
    # 生成新的验证规则
    generate_ahc_validation_rules()
    
    print("\n验证规则生成完成！")

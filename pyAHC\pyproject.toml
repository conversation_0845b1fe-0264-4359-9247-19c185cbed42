[project]
name = "pyahc"
version = "0.1.0"
description = "Python wrapper for AHC model."
authors = [{name = "WSY", email = "<EMAIL>"}]
license = "MIT"
readme = "README.md"
repository = ""
documentation = ""
pyahc = "pyahc.core.cli.cli:app"
dynamic = ["version", "dependencies", "requires-python"]

[tool.poetry.scripts]
pyahc = "pyahc.core.cli.cli:app"

[tool.poetry.dependencies]
python = "^3.11"
pandas = "^2.2.2"
numpy = "^1.26.4"
knmi-py = "^0.1.10"
chardet = "^5.2.0"
h5py = "^3.11.0"
shapely = "^2.0.5"
pyproj = "^3.6.1"
pydantic = "^2.7.4"
pandera = "^0.19.3"
matplotlib = "^3.9.2"
typer = "^0.13.1"
seaborn = "^0.13.2"
pyyaml = "^6.0.2"


[tool.poetry.group.dev.dependencies]
jupyter = "^1.1.1"
pytest = "^8.2.2"
pytest-cov = "^5.0.0"
deptry = "^0.16.2"
mypy = "^1.11.0"
nbstripout = "^0.7.1"
pre-commit = "^3.8.0"
ruff = "^0.7.4"
types-pyyaml = "^6.0.12.20241230"
pandas-stubs = "^2.2.3.241126"
h5py-stubs = "^0.1.1"

[tool.poetry.group.docs.dependencies]
mkdocs-material = "^9.6.4"
mkdocs-include-markdown-plugin = "^6.2.0"
mkdocs-autorefs = "^1.0.1"
mkdocstrings-python = "^1.10.3"
mkdocs-jupyter = "^0.25.0"
mkdocs-awesome-pages-plugin = "^2.10.1"
mkdocs-exclude-search = "^0.6.6"

[tool.poetry]
packages = [
  {include = "pyahc"},
  {include = "py.typed"},
]


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.mypy]
files = ["pyahc"]
disallow_untyped_defs = "True"
disallow_any_unimported = "True"
no_implicit_optional = "True"
check_untyped_defs = "True"
warn_return_any = "True"
warn_unused_ignores = "True"
show_error_codes = "True"
ignore_missing_imports = "True"
disable_error_code = ["union-attr", "no-untyped-def", "return-value", "arg-type", "assignment", "call-arg"]

[tool.pytest.ini_options]
testpaths = ["tests"]

[tool.ruff]
target-version = "py311"
line-length = 88
fix = true
exclude = ["*.csv", "*.txt", "*.h5", "*.swp", "*.bbc", "*.crp", "*.dra", "*.irg"]

[tool.ruff.lint]
select = [
    # flake8-2020
    "YTT",
    # flake8-bandit
    "S",
    # flake8-bugbear
    "B",
    # flake8-builtins
    "A",
    # flake8-comprehensions
    "C4",
    # flake8-debugger
    "T10",
    # flake8-simplify
    "SIM",
    # isort
    "I",
    # mccabe
    "C90",
    # pycodestyle
    "E", "W",
    # pyflakes
    "F",
    # pygrep-hooks
    "PGH",
    # pyupgrade
    "UP",
    # ruff
    "RUF",
    # tryceratops
    "TRY",
]
ignore = [
    # LineTooLong
    "E501",
    # DoNotAssignLambda
    "E731",
]

[tool.ruff.lint.isort]
force-wrap-aliases = true
combine-as-imports = true


[tool.ruff.lint.per-file-ignores]
"tests/*" = ["S101"]

[tool.ruff.format]
quote-style = "double"
preview = true

[tool.coverage.report]
skip_empty = true

[tool.coverage.run]
branch = true
source = ["pyahc"]

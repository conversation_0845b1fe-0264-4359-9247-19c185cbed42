# mypy: disable-error-code="attr-defined"
# attr-defined 被禁用，因为在基类中实现一个 mixin 的部分功能更容易。
# 以后可以考虑修复
"""所有 pyAHC 模型继承的基模型

许多功能可以在基模型中抽象出来。这样，代码更 DRY (Don't Repeat Yourself)且更易于维护。
基模型用于强制输入数据的正确数据类型和结构。它们还提供
将数据转换为 AHC 模型所需格式的方法。

此处定义的类基于 Pydantic BaseModel 和 Pandera DataFrameModel。
两者都旨在确保输入数据的正确数据类型和结构，
因为成功的验证意味着 AHC 模型的顺利执行。
在 HPC 上作为提交作业运行时尤其重要。

类：
    BaseModel: pyAHC 模型的基类。继承自 Pydantic BaseModel。
    BaseTableModel: 验证 pandas DataFrame 的 pyAHC 模型的基类。继承自 Pandera DataFrameModel。
"""

from __future__ import annotations

from typing import Any, Dict, Optional
import logging

import pandas as pd
import pandera as pa
from pandera.typing import DataFrame
from pydantic import BaseModel, ConfigDict, field_validator, PrivateAttr

from pyahc.core.defaults import ADDITIONAL_SWITCHES

# 设置日志
logger = logging.getLogger(__name__)


class PyAHCBaseModel(BaseModel):
    """pyAHC 模型的增强基类。

    新增功能：
        - 验证状态控制机制
        - 严格类型检查配置
        - 外部配置支持
        - 增强的验证错误处理

    方法：
        __setattr__: 覆盖方法以静默忽略冻结字段的赋值。
        update: 使用字典中的新值更新模型。
        enable_validation: 启用验证模式。
        disable_validation: 禁用验证模式。
        get_validation_errors: 获取验证错误列表。
    """

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        validate_assignment=True,
        extra="ignore",
        populate_by_name=True,
        # 新增：严格类型检查
        strict=False,  # 默认为False以保持向后兼容
        # 新增：验证默认值
        validate_default=True,
        # 新增：使用枚举值
        use_enum_values=True,
        # 新增：序列化时排除未设置的字段
        exclude_unset=True,
    )

    # 新增：验证状态控制
    _validation: bool = PrivateAttr(default=False)
    _external_config: Dict[str, Any] = PrivateAttr(default_factory=dict)
    _validation_errors: list[str] = PrivateAttr(default_factory=list)
    _strict_mode: bool = PrivateAttr(default=False)

    def enable_validation(self) -> None:
        """启用验证模式。

        启用后，模型将执行完整的验证检查，包括条件参数验证、
        跨组件依赖验证等。适用于开发和调试阶段。
        """
        self._validation = True
        logger.debug(f"Validation enabled for {self.__class__.__name__}")

    def disable_validation(self) -> None:
        """禁用验证模式。

        禁用后，模型将跳过大部分验证检查，提高性能。
        适用于生产环境或性能敏感的场景。
        """
        self._validation = False
        logger.debug(f"Validation disabled for {self.__class__.__name__}")

    def enable_strict_mode(self) -> None:
        """启用严格模式。

        严格模式下，类型检查更加严格，不允许类型转换。
        """
        self._strict_mode = True
        logger.debug(f"Strict mode enabled for {self.__class__.__name__}")

    def disable_strict_mode(self) -> None:
        """禁用严格模式。"""
        self._strict_mode = False
        logger.debug(f"Strict mode disabled for {self.__class__.__name__}")

    def get_validation_errors(self) -> list[str]:
        """获取验证错误列表。

        返回:
            list[str]: 验证错误信息列表
        """
        return self._validation_errors.copy()

    def clear_validation_errors(self) -> None:
        """清除验证错误列表。"""
        self._validation_errors.clear()

    def add_validation_error(self, error: str) -> None:
        """添加验证错误。

        参数:
            error (str): 错误信息
        """
        self._validation_errors.append(error)
        logger.warning(f"Validation error added: {error}")

    def is_validation_enabled(self) -> bool:
        """检查是否启用了验证。

        返回:
            bool: 如果启用了验证则返回 True
        """
        return self._validation

    def set_external_config(self, config: Dict[str, Any]) -> None:
        """设置外部配置。

        参数:
            config (Dict[str, Any]): 外部配置字典
        """
        self._external_config.update(config)
        logger.debug(f"External config updated for {self.__class__.__name__}")

    def get_external_config(self) -> Dict[str, Any]:
        """获取外部配置。

        返回:
            Dict[str, Any]: 外部配置字典的副本
        """
        return self._external_config.copy()

    def __setattr__(self, name, value):
        """静默忽略冻结字段的赋值。

        此方法被覆盖以静默忽略冻结字段的赋值，以避免读取旧 swp 文件时出错。
        """

        if name in self.model_fields and self.model_fields[name].frozen:
            return
        super().__setattr__(name, value)

    def update(self, new: dict, inplace: bool = False, no_validate: bool = False):
        """使用新值更新模型。

        给定值的字典首先被过滤，只包含模型中存在的字段。
        然后使用新值更新模型。返回更新后的模型（新模型或更新后的自身）。

        参数：
            new (dict): 包含新值的字典。
            inplace (bool): 如果为 True，则就地更新模型。
        """

        # filtered = {k: v for k, v in new.items() if k in self.model_fields}

        # updated_model = self.model_validate(dict(self) | filtered)
        updated_model = self.model_validate(dict(self) | new)

        if not inplace:
            # 添加此项是为了用户从经典 ASCII 文件加载模型的情况。
            # 此时使用 .update() 方法，但并非所有属性都会立即可用。
            # 在模型运行时仍将执行完整验证。
            if no_validate:
                updated_model._validation = False
            else:
                updated_model._validation = True
            updated_model.validate_with_yaml() if hasattr(
                updated_model, "validate_with_yaml"
            ) else None
            return updated_model

        else:
            for field, value in updated_model:
                setattr(self, field, value)
            if no_validate:
                updated_model._validation = False
            else:
                updated_model._validation = True
            self.validate_with_yaml() if hasattr(
                updated_model, "validate_with_yaml"
            ) else None

            return self

    @field_validator("*", mode="before")
    @classmethod
    def convert_switches(cls, value: Any, info: Any) -> Any:
        """将开关值转换为整数。

        此方法对于确保从 ASCII 文件加载模型能够正常工作是必要的。
        可以改进以包含不以“sw”开头的字面量。
        """
        if (
            (info.field_name.startswith("sw") or info.field_name in ADDITIONAL_SWITCHES)
            and info.field_name != "ahc_version"
            and value
        ):
            try:
                return int(value)
            except ValueError:
                return value
        return value


class BaseTableModel(pa.DataFrameModel):
    """pandas DataFrame 的基模型。

    方法：
        create: 从字典创建经过验证的 DataFrame。
    """

    class Config:
        coerce = True

    @classmethod
    def create(cls, data: dict, columns: list | None = None) -> DataFrame:
        df = pd.DataFrame(data)
        if columns:
            df.columns = columns
        else:
            df.columns = df.columns.str.upper()
        validated_df = cls.validate(df)
        return validated_df

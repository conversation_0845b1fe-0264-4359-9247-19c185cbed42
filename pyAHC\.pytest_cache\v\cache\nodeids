["tests/test_state_extractor.py::TestStateExtractor::test_ascii_parsing_with_invalid_data", "tests/test_state_extractor.py::TestStateExtractor::test_extract_all_states_complete", "tests/test_state_extractor.py::TestStateExtractor::test_extract_all_states_with_missing_data", "tests/test_state_extractor.py::TestStateExtractor::test_extract_crop_biomass_from_csv", "tests/test_state_extractor.py::TestStateExtractor::test_extract_crop_lai_from_csv", "tests/test_state_extractor.py::TestStateExtractor::test_extract_from_csv_with_alternative_column_names", "tests/test_state_extractor.py::TestStateExtractor::test_extract_from_empty_result", "tests/test_state_extractor.py::TestStateExtractor::test_extract_groundwater_level_from_csv", "tests/test_state_extractor.py::TestStateExtractor::test_extract_root_depth_from_csv", "tests/test_state_extractor.py::TestStateExtractor::test_extract_soil_moisture_from_ascii", "tests/test_state_extractor.py::TestStateExtractor::test_extract_soil_moisture_from_csv", "tests/test_state_extractor.py::TestStateExtractor::test_parse_numeric_from_ascii", "tests/test_state_extractor.py::TestStateExtractor::test_parse_soil_moisture_from_sba", "tests/test_state_extractor.py::TestStateExtractor::test_validate_biomass_out_of_range", "tests/test_state_extractor.py::TestStateExtractor::test_validate_biomass_valid", "tests/test_state_extractor.py::TestStateExtractor::test_validate_extracted_states", "tests/test_state_extractor.py::TestStateExtractor::test_validate_lai_out_of_range", "tests/test_state_extractor.py::TestStateExtractor::test_validate_lai_valid", "tests/test_state_extractor.py::TestStateExtractor::test_validate_soil_moisture_out_of_range", "tests/test_state_extractor.py::TestStateExtractor::test_validate_soil_moisture_valid"]
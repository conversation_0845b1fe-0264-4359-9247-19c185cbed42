#!/usr/bin/env python3
"""地下水浓度数据 - 从标准案例hetao.BBC文件中提取

这个文件包含了从标准案例hetao.BBC文件中提取的完整地下水浓度数据。

数据格式: (day, month, value)
- day: 日期（天）
- month: 月份
- value: 地下水浓度值
"""

# 从标准案例hetao.BBC文件中提取的完整地下水浓度数据
GWC_DATA = [
    (2, 5, 2.02), (3, 5, 2.02), (4, 5, 2.02), (5, 5, 2.02),
    (6, 5, 2.02), (7, 5, 2.02), (8, 5, 2.02), (9, 5, 2.02),
    (10, 5, 2.02), (11, 5, 2.23), (12, 5, 2.44), (13, 5, 2.65),
    (14, 5, 2.57), (15, 5, 2.49), (16, 5, 2.41), (17, 5, 2.33),
    (18, 5, 2.25), (19, 5, 2.17), (20, 5, 2.09), (21, 5, 2.04),
    (22, 5, 2.05), (23, 5, 2.05), (24, 5, 2.05), (25, 5, 2.05),
    (26, 5, 2.06), (27, 5, 2.06), (28, 5, 2.06), (29, 5, 2.07),
    (30, 5, 2.07), (31, 5, 2.07), (1, 6, 2.10), (2, 6, 2.09),
    (3, 6, 2.07), (4, 6, 2.07), (5, 6, 2.06), (6, 6, 2.06),
    (7, 6, 2.05), (8, 6, 2.05), (9, 6, 2.04), (10, 6, 2.04),
    (11, 6, 2.04), (12, 6, 2.04), (13, 6, 2.04), (14, 6, 2.03),
    (15, 6, 2.03), (16, 6, 2.03), (17, 6, 2.03), (18, 6, 2.03),
    (19, 6, 2.02), (20, 6, 2.02), (21, 6, 2.02), (22, 6, 2.02),
    (23, 6, 2.02), (24, 6, 2.01), (25, 6, 2.01), (26, 6, 2.01),
    (27, 6, 2.01), (28, 6, 2.01), (29, 6, 2.00), (30, 6, 2.00),
    (1, 7, 2.00), (2, 7, 2.00), (3, 7, 1.99), (4, 7, 1.99),
    (5, 7, 1.99), (6, 7, 1.99), (7, 7, 1.99), (8, 7, 1.98),
    (9, 7, 1.98), (10, 7, 1.98), (11, 7, 1.97), (12, 7, 1.97),
    (13, 7, 1.97), (14, 7, 1.97), (15, 7, 1.97), (16, 7, 1.97),
    (17, 7, 1.97), (18, 7, 1.97), (19, 7, 1.97), (20, 7, 1.96),
    (21, 7, 1.96), (22, 7, 1.96), (23, 7, 1.96), (24, 7, 1.96),
    (25, 7, 1.96), (26, 7, 1.96), (27, 7, 1.96), (28, 7, 1.96),
    (29, 7, 1.96), (30, 7, 1.95), (31, 7, 1.95), (1, 8, 1.95),
    (2, 8, 1.95), (3, 8, 1.95), (4, 8, 1.95), (5, 8, 1.95),
    (6, 8, 1.94), (7, 8, 1.96), (8, 8, 1.98), (9, 8, 1.99),
    (10, 8, 2.01), (11, 8, 2.03), (12, 8, 2.04), (13, 8, 2.03),
    (14, 8, 2.02), (15, 8, 2.01), (16, 8, 2.00), (17, 8, 1.99),
    (18, 8, 1.98), (19, 8, 1.97), (20, 8, 1.96), (21, 8, 1.95),
    (22, 8, 1.94), (23, 8, 1.93), (24, 8, 1.93), (25, 8, 1.93),
    (26, 8, 1.94), (27, 8, 1.94), (28, 8, 1.95), (29, 8, 1.96),
    (30, 8, 1.96), (31, 8, 1.97), (1, 9, 1.97), (2, 9, 1.98),
    (3, 9, 1.98), (4, 9, 1.97), (5, 9, 1.95), (6, 9, 1.93),
    (7, 9, 1.91), (8, 9, 1.90), (9, 9, 1.88), (10, 9, 1.86),
    (11, 9, 1.84), (12, 9, 1.83), (13, 9, 1.81), (14, 9, 1.79),
    (15, 9, 1.77), (16, 9, 1.76), (17, 9, 1.74), (18, 9, 1.72),
    (19, 9, 1.70), (20, 9, 1.69), (21, 9, 1.68)
]

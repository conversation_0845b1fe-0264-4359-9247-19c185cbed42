"""pyAHC 的数据库集成。

此模块包含允许 pyAHC 与数据库交互的类和函数。
目前，支持 WOFOST 作物数据库（使用 WOFOSTCropDB 类）和 HDF5 文件格式（使用 HDF5 类）。

### HDF5
由于 HDF5 集成，用户将能够将其模型和结果保存到单个 .h5 文件中。
HDF5 数据库是二进制文件，可以结构化地存储大量数据。
这种格式在科学界广泛使用，并受许多编程语言和不同平台的支持。
其理念是

### WOFOST crop database
WOFOST 作物数据库是一个包含 WOFOST 模型作物参数的数据库，以 YAML 格式存储在文件中。
WOFOSTCropDB 类允许用户加载特定作物品种及其相应的参数，并用它们更新 pyAHC 模型。

Modules:
    hdf5: 用于与 HDF5 文件交互的类。
    cropdb: 封装 WOFOST (A. de Wit) 作物参数数据库的类。
"""

from pyahc.db.cropdb import CropVariety, WOFOSTCropDB
from pyahc.db.hdf5 import (
    ProjectHDF5Manager,
    StateExtractor,
    StateInjector,
    HDF5Error,
    ProjectNotFoundError,
    DataNotFoundError,
    InvalidDataFormatError
)

__all__ = [
    "WOFOSTCropDB",
    "CropVariety",
    "ProjectHDF5Manager",
    "StateExtractor",
    "StateInjector",
    "HDF5Error",
    "ProjectNotFoundError",
    "DataNotFoundError",
    "InvalidDataFormatError"
]

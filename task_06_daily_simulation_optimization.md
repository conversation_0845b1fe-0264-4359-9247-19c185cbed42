# 任务06：日循环模拟优化与性能提升

## 任务概述
优化日循环模拟的性能和稳定性，实现高效的状态传递、内存管理和错误恢复机制。确保系统能够稳定运行长期模拟（数月到数年）。

## 任务需求

### 核心目标
1. 优化日循环模拟的执行效率
2. 实现智能的内存管理和资源释放
3. 提供模拟中断和恢复功能
4. 实现并行处理能力（可选）
5. 优化HDF5数据读写性能

### 具体要求
- 减少内存占用和提高执行速度
- 支持大规模长期模拟（1000+天）
- 实现检查点和恢复机制
- 提供详细的性能监控和诊断
- 支持批量模拟和参数扫描

## 实现步骤

### 步骤1：实现高效的日循环模拟引擎

```python
"""优化的日循环模拟引擎"""
import gc
import time
import psutil
import threading
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from typing import Dict, Any, Optional, List, Callable
import numpy as np
from datetime import date, timedelta
import logging

from .manager import ProjectHDF5Manager
from .state_extractor import StateExtractor
from .state_injector import StateInjector
from .exceptions import HDF5Error

class OptimizedSimulationEngine:
    """优化的模拟引擎"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.performance_monitor = PerformanceMonitor()
        self.checkpoint_manager = CheckpointManager(config)
        
        # 性能配置
        self.memory_limit_mb = config.get('memory_limit_mb', 2048)
        self.gc_frequency = config.get('gc_frequency', 10)  # 每N天执行垃圾回收
        self.checkpoint_frequency = config.get('checkpoint_frequency', 50)  # 每N天保存检查点
        self.parallel_enabled = config.get('enable_parallel', False)
        self.max_workers = config.get('max_workers', 2)
    
    def run_optimized_simulation(self, manager: ProjectHDF5Manager,
                                project_name: str,
                                start_date: date,
                                end_date: date,
                                base_model: 'Model') -> None:
        """运行优化的模拟"""
        try:
            # 检查是否有检查点可以恢复
            resume_date = self.checkpoint_manager.find_resume_point(project_name, start_date, end_date)
            if resume_date:
                current_date = resume_date
                self.logger.info(f"Resuming simulation from {resume_date}")
            else:
                current_date = start_date
                self.logger.info(f"Starting new simulation from {start_date}")
            
            total_days = (end_date - start_date).days + 1
            completed_days = (current_date - start_date).days
            
            # 性能监控开始
            self.performance_monitor.start_monitoring()
            
            while current_date <= end_date:
                day_start_time = time.time()
                
                try:
                    # 执行单日模拟
                    self._run_single_day_optimized(
                        manager, project_name, current_date, base_model
                    )
                    
                    completed_days += 1
                    day_duration = time.time() - day_start_time
                    
                    # 更新性能监控
                    self.performance_monitor.record_day_performance(
                        current_date, day_duration, self._get_memory_usage()
                    )
                    
                    # 定期垃圾回收
                    if completed_days % self.gc_frequency == 0:
                        self._perform_garbage_collection()
                    
                    # 定期保存检查点
                    if completed_days % self.checkpoint_frequency == 0:
                        self.checkpoint_manager.save_checkpoint(
                            project_name, current_date, self.performance_monitor.get_stats()
                        )
                    
                    # 内存检查
                    if self._check_memory_limit():
                        self.logger.warning("Memory limit approaching, forcing garbage collection")
                        self._perform_garbage_collection()
                    
                    # 进度报告
                    if completed_days % 10 == 0:
                        self._report_progress(completed_days, total_days, day_duration)
                
                except Exception as e:
                    self.logger.error(f"Simulation failed on {current_date}: {e}")
                    
                    # 保存错误检查点
                    self.checkpoint_manager.save_error_checkpoint(
                        project_name, current_date, str(e)
                    )
                    
                    if self.config.get('stop_on_error', True):
                        raise
                    else:
                        self.logger.warning(f"Skipping {current_date} and continuing")
                
                current_date += timedelta(days=1)
            
            # 模拟完成
            self.performance_monitor.stop_monitoring()
            self._generate_performance_report(project_name)
            
        except Exception as e:
            self.logger.error(f"Optimized simulation failed: {e}")
            raise
    
    def _run_single_day_optimized(self, manager: ProjectHDF5Manager,
                                 project_name: str,
                                 current_date: date,
                                 base_model: 'Model') -> None:
        """优化的单日模拟"""
        # 使用缓存的状态加载
        previous_states = self._load_previous_states_cached(
            manager, project_name, current_date - timedelta(days=1)
        )
        
        # 高效的模型准备
        daily_model = self._prepare_daily_model_optimized(base_model, current_date, previous_states)
        
        # 运行模型
        result = daily_model.run()
        
        # 快速状态提取
        current_states = StateExtractor.extract_all_states(result)
        
        # 异步保存数据
        if self.parallel_enabled:
            self._save_data_async(manager, project_name, current_date, daily_model, result, current_states)
        else:
            self._save_data_sync(manager, project_name, current_date, daily_model, result, current_states)
        
        # 清理临时对象
        del daily_model, result
    
    def _prepare_daily_model_optimized(self, base_model: 'Model', 
                                     current_date: date,
                                     previous_states: Optional[Dict[str, Any]]) -> 'Model':
        """优化的模型准备"""
        # 使用浅拷贝+选择性深拷贝来减少内存使用
        daily_model = self._smart_model_copy(base_model)
        
        # 高效的状态注入
        if previous_states:
            daily_model = StateInjector.inject_all_states(daily_model, previous_states)
        
        # 更新日期
        self._update_model_date_fast(daily_model, current_date)
        
        return daily_model
    
    def _smart_model_copy(self, model: 'Model') -> 'Model':
        """智能模型拷贝，只深拷贝必要的部分"""
        # 这里实现选择性拷贝逻辑
        # 对于不会改变的组件使用浅拷贝，对于会改变的组件使用深拷贝
        return model.model_copy(deep=True)  # 简化实现，实际可以优化
```

### 步骤2：实现性能监控系统

```python
class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.start_time = None
        self.daily_stats = []
        self.memory_stats = []
        self.is_monitoring = False
        self.lock = threading.Lock()
    
    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        self.is_monitoring = True
        self.daily_stats = []
        self.memory_stats = []
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
    
    def record_day_performance(self, date: date, duration: float, memory_mb: float):
        """记录单日性能"""
        with self.lock:
            self.daily_stats.append({
                'date': date.isoformat(),
                'duration': duration,
                'memory_mb': memory_mb,
                'timestamp': time.time()
            })
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self.daily_stats:
            return {}
        
        durations = [stat['duration'] for stat in self.daily_stats]
        memories = [stat['memory_mb'] for stat in self.daily_stats]
        
        return {
            'total_days': len(self.daily_stats),
            'total_time': sum(durations),
            'avg_day_duration': np.mean(durations),
            'max_day_duration': np.max(durations),
            'min_day_duration': np.min(durations),
            'avg_memory_mb': np.mean(memories),
            'max_memory_mb': np.max(memories),
            'days_per_hour': 3600 / np.mean(durations) if durations else 0
        }
    
    def get_performance_trend(self) -> Dict[str, List]:
        """获取性能趋势"""
        return {
            'dates': [stat['date'] for stat in self.daily_stats],
            'durations': [stat['duration'] for stat in self.daily_stats],
            'memory_usage': [stat['memory_mb'] for stat in self.daily_stats]
        }

class CheckpointManager:
    """检查点管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.checkpoint_dir = Path(config.get('checkpoint_dir', './checkpoints'))
        self.checkpoint_dir.mkdir(exist_ok=True)
        self.logger = logging.getLogger(__name__)
    
    def save_checkpoint(self, project_name: str, current_date: date, stats: Dict[str, Any]):
        """保存检查点"""
        try:
            checkpoint_data = {
                'project_name': project_name,
                'current_date': current_date.isoformat(),
                'timestamp': time.time(),
                'performance_stats': stats,
                'config': self.config
            }
            
            checkpoint_file = self.checkpoint_dir / f"{project_name}_{current_date.strftime('%Y%m%d')}.json"
            
            import json
            with open(checkpoint_file, 'w') as f:
                json.dump(checkpoint_data, f, indent=2)
            
            self.logger.info(f"Saved checkpoint for {project_name} at {current_date}")
            
        except Exception as e:
            self.logger.error(f"Failed to save checkpoint: {e}")
    
    def find_resume_point(self, project_name: str, start_date: date, end_date: date) -> Optional[date]:
        """查找恢复点"""
        try:
            checkpoint_files = list(self.checkpoint_dir.glob(f"{project_name}_*.json"))
            
            if not checkpoint_files:
                return None
            
            # 找到最新的有效检查点
            latest_checkpoint = None
            latest_date = None
            
            for checkpoint_file in checkpoint_files:
                try:
                    import json
                    with open(checkpoint_file, 'r') as f:
                        checkpoint_data = json.load(f)
                    
                    checkpoint_date = date.fromisoformat(checkpoint_data['current_date'])
                    
                    if start_date <= checkpoint_date <= end_date:
                        if latest_date is None or checkpoint_date > latest_date:
                            latest_date = checkpoint_date
                            latest_checkpoint = checkpoint_data
                
                except Exception as e:
                    self.logger.warning(f"Failed to read checkpoint {checkpoint_file}: {e}")
            
            if latest_checkpoint:
                return latest_date + timedelta(days=1)  # 从下一天开始
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to find resume point: {e}")
            return None
    
    def save_error_checkpoint(self, project_name: str, error_date: date, error_msg: str):
        """保存错误检查点"""
        try:
            error_data = {
                'project_name': project_name,
                'error_date': error_date.isoformat(),
                'error_message': error_msg,
                'timestamp': time.time()
            }
            
            error_file = self.checkpoint_dir / f"{project_name}_error_{error_date.strftime('%Y%m%d')}.json"
            
            import json
            with open(error_file, 'w') as f:
                json.dump(error_data, f, indent=2)
            
            self.logger.info(f"Saved error checkpoint for {project_name} at {error_date}")
            
        except Exception as e:
            self.logger.error(f"Failed to save error checkpoint: {e}")
```

### 步骤3：实现内存优化和资源管理

```python
    def _get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0
    
    def _check_memory_limit(self) -> bool:
        """检查是否接近内存限制"""
        current_memory = self._get_memory_usage()
        return current_memory > self.memory_limit_mb * 0.8  # 80%阈值
    
    def _perform_garbage_collection(self):
        """执行垃圾回收"""
        before_memory = self._get_memory_usage()
        
        # 强制垃圾回收
        gc.collect()
        
        after_memory = self._get_memory_usage()
        freed_memory = before_memory - after_memory
        
        self.logger.debug(f"Garbage collection freed {freed_memory:.1f} MB")
    
    def _load_previous_states_cached(self, manager: ProjectHDF5Manager,
                                   project_name: str,
                                   previous_date: date) -> Optional[Dict[str, Any]]:
        """缓存的状态加载"""
        # 实现简单的LRU缓存
        cache_key = f"{project_name}_{previous_date.isoformat()}"
        
        if hasattr(self, '_state_cache') and cache_key in self._state_cache:
            return self._state_cache[cache_key]
        
        try:
            with manager:
                daily_data = manager.load_daily_data(project_name, previous_date)
                states = daily_data.get('states', {})
            
            # 缓存状态（限制缓存大小）
            if not hasattr(self, '_state_cache'):
                self._state_cache = {}
            
            if len(self._state_cache) > 10:  # 最多缓存10天的状态
                # 移除最旧的缓存项
                oldest_key = next(iter(self._state_cache))
                del self._state_cache[oldest_key]
            
            self._state_cache[cache_key] = states
            return states
            
        except Exception as e:
            self.logger.debug(f"Could not load previous states for {previous_date}: {e}")
            return None
    
    def _update_model_date_fast(self, model: 'Model', current_date: date):
        """快速更新模型日期"""
        try:
            # 直接设置属性，避免复杂的验证
            if hasattr(model, 'generalsettings'):
                model.generalsettings.tstart = current_date
                model.generalsettings.tend = current_date
        except Exception as e:
            self.logger.warning(f"Failed to update model date: {e}")
    
    def _save_data_async(self, manager: ProjectHDF5Manager,
                        project_name: str,
                        current_date: date,
                        model: 'Model',
                        result: 'Result',
                        states: Dict[str, Any]):
        """异步保存数据"""
        # 使用线程池异步保存
        if not hasattr(self, '_save_executor'):
            self._save_executor = ThreadPoolExecutor(max_workers=2)
        
        future = self._save_executor.submit(
            self._save_data_sync, manager, project_name, current_date, model, result, states
        )
        
        # 可以选择等待完成或继续执行
        # future.result()  # 等待完成
    
    def _save_data_sync(self, manager: ProjectHDF5Manager,
                       project_name: str,
                       current_date: date,
                       model: 'Model',
                       result: 'Result',
                       states: Dict[str, Any]):
        """同步保存数据"""
        try:
            parameters = self._extract_parameters_fast(model)
            
            with manager:
                manager.save_daily_data(
                    project_name, current_date, model, result, states, parameters
                )
        except Exception as e:
            self.logger.error(f"Failed to save data for {current_date}: {e}")
            raise
    
    def _extract_parameters_fast(self, model: 'Model') -> Dict[str, Any]:
        """快速参数提取"""
        # 简化的参数提取，只提取关键参数
        parameters = {}
        
        try:
            if hasattr(model, 'soilprofile') and model.soilprofile:
                if hasattr(model.soilprofile, 'hydraulic_conductivity'):
                    parameters['hydraulic_conductivity'] = model.soilprofile.hydraulic_conductivity
        except Exception:
            pass
        
        return parameters
    
    def _report_progress(self, completed_days: int, total_days: int, last_day_duration: float):
        """报告进度"""
        progress = (completed_days / total_days) * 100
        estimated_remaining = (total_days - completed_days) * last_day_duration
        
        self.logger.info(
            f"Progress: {completed_days}/{total_days} days ({progress:.1f}%) - "
            f"Last day: {last_day_duration:.2f}s - "
            f"Est. remaining: {estimated_remaining/60:.1f} min"
        )
    
    def _generate_performance_report(self, project_name: str):
        """生成性能报告"""
        stats = self.performance_monitor.get_stats()
        
        report = f"""
Performance Report for {project_name}:
=====================================
Total days simulated: {stats.get('total_days', 0)}
Total simulation time: {stats.get('total_time', 0):.1f} seconds
Average time per day: {stats.get('avg_day_duration', 0):.3f} seconds
Simulation rate: {stats.get('days_per_hour', 0):.1f} days/hour
Peak memory usage: {stats.get('max_memory_mb', 0):.1f} MB
Average memory usage: {stats.get('avg_memory_mb', 0):.1f} MB
"""
        
        self.logger.info(report)
        
        # 保存详细报告到文件
        report_file = Path(f"{project_name}_performance_report.txt")
        with open(report_file, 'w') as f:
            f.write(report)
            
            # 添加趋势数据
            trend = self.performance_monitor.get_performance_trend()
            f.write("\nDaily Performance Data:\n")
            f.write("Date,Duration(s),Memory(MB)\n")
            for i, date in enumerate(trend['dates']):
                f.write(f"{date},{trend['durations'][i]:.3f},{trend['memory_usage'][i]:.1f}\n")
```

### 步骤4：实现批量模拟和参数扫描

```python
class BatchSimulationManager:
    """批量模拟管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.parallel_enabled = config.get('enable_parallel_batch', False)
        self.max_workers = config.get('max_batch_workers', 4)
    
    def run_parameter_sweep(self, base_config: Dict[str, Any], 
                           parameter_ranges: Dict[str, List]) -> List[Dict[str, Any]]:
        """运行参数扫描"""
        # 生成参数组合
        parameter_combinations = self._generate_parameter_combinations(parameter_ranges)
        
        results = []
        
        if self.parallel_enabled:
            results = self._run_parallel_simulations(base_config, parameter_combinations)
        else:
            results = self._run_sequential_simulations(base_config, parameter_combinations)
        
        return results
    
    def _generate_parameter_combinations(self, parameter_ranges: Dict[str, List]) -> List[Dict[str, Any]]:
        """生成参数组合"""
        import itertools
        
        keys = list(parameter_ranges.keys())
        values = list(parameter_ranges.values())
        
        combinations = []
        for combination in itertools.product(*values):
            param_dict = dict(zip(keys, combination))
            combinations.append(param_dict)
        
        return combinations
    
    def _run_parallel_simulations(self, base_config: Dict[str, Any],
                                 parameter_combinations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """并行运行模拟"""
        with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []
            
            for i, params in enumerate(parameter_combinations):
                config = base_config.copy()
                config.update(params)
                config['project_name'] = f"{base_config['project_name']}_sweep_{i:03d}"
                
                future = executor.submit(self._run_single_simulation, config)
                futures.append((future, params))
            
            results = []
            for future, params in futures:
                try:
                    result = future.result()
                    result['parameters'] = params
                    results.append(result)
                except Exception as e:
                    self.logger.error(f"Simulation failed for parameters {params}: {e}")
                    results.append({'parameters': params, 'error': str(e)})
            
            return results
    
    def _run_sequential_simulations(self, base_config: Dict[str, Any],
                                   parameter_combinations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """顺序运行模拟"""
        results = []
        
        for i, params in enumerate(parameter_combinations):
            try:
                config = base_config.copy()
                config.update(params)
                config['project_name'] = f"{base_config['project_name']}_sweep_{i:03d}"
                
                result = self._run_single_simulation(config)
                result['parameters'] = params
                results.append(result)
                
            except Exception as e:
                self.logger.error(f"Simulation failed for parameters {params}: {e}")
                results.append({'parameters': params, 'error': str(e)})
        
        return results
    
    def _run_single_simulation(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """运行单个模拟"""
        from .workflow import run_project_simulation
        
        start_time = time.time()
        
        try:
            manager = run_project_simulation(config)
            
            # 提取关键结果
            with manager:
                project_group = manager._file[config['project_name']]
                if 'summary/statistics' in project_group:
                    stats_group = project_group['summary/statistics']
                    stats = dict(stats_group.attrs)
                else:
                    stats = {}
            
            duration = time.time() - start_time
            
            return {
                'project_name': config['project_name'],
                'duration': duration,
                'statistics': stats,
                'success': True
            }
            
        except Exception as e:
            duration = time.time() - start_time
            return {
                'project_name': config['project_name'],
                'duration': duration,
                'error': str(e),
                'success': False
            }
```

## 可交付成果

### 主要交付物
1. **优化的模拟引擎**：OptimizedSimulationEngine类
2. **性能监控系统**：PerformanceMonitor和CheckpointManager类
3. **批量模拟管理器**：BatchSimulationManager类
4. **性能测试套件**：performance_tests.py
5. **优化配置模板**：optimized_config.yaml

### 功能特性
- 高效的内存管理和垃圾回收
- 检查点和恢复机制
- 详细的性能监控和报告
- 批量模拟和参数扫描支持

## 验收标准

### 性能验收
1. **模拟速度**：相比基础版本提升50%以上
2. **内存使用**：长期模拟内存使用稳定，无内存泄漏
3. **大规模测试**：能够稳定运行1000天以上的模拟
4. **恢复功能**：检查点恢复成功率100%

### 功能验收
1. **性能监控**：提供详细的性能统计和趋势分析
2. **批量处理**：支持参数扫描和批量模拟
3. **错误处理**：优雅处理各种异常情况
4. **资源管理**：有效控制内存和CPU使用

### 质量验收
1. **代码覆盖率**：性能测试覆盖率达到80%以上
2. **文档完整性**：所有优化功能有详细说明
3. **配置灵活性**：支持多种性能调优配置

## 依赖关系
- **前置依赖**：任务01-05（完整的基础系统）
- **后续任务**：任务07（数据同化接口）、任务08（测试和验证）


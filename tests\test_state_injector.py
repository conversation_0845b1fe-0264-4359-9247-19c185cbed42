"""StateInjector单元测试"""
import pytest
import numpy as np
from unittest.mock import Mock, MagicMock, patch
import logging

from pyahc.db.hdf5.state_injector import StateInjector


class TestStateInjector:
    """StateInjector测试类"""
    
    def setup_method(self):
        """测试前的设置"""
        self.mock_model = self._create_mock_model()
        self.test_states = {
            'soil_moisture': np.array([0.3, 0.25, 0.2, 0.15]),
            'lai': 3.5,
            'biomass': 1500.0,
            'root_depth': 80.0,
            'groundwater_level': -150.0
        }
    
    def _create_mock_model(self):
        """创建模拟模型对象"""
        mock_model = Mock()
        
        # 模拟model_copy方法
        mock_model.model_copy = Mock(return_value=mock_model)
        
        # 模拟土壤含水量组件
        mock_soilmoisture = Mock()
        mock_soilmoisture.thetai = [0.35, 0.3, 0.25, 0.2]
        mock_soilmoisture.swinco = 1.0
        mock_model.soilmoisture = mock_soilmoisture
        
        # 模拟作物组件
        mock_crop = Mock()
        mock_crop.leaf_area_index = 2.0
        mock_crop.biomass = 1000.0
        mock_crop.rds = 60.0
        mock_model.crop = mock_crop
        
        # 模拟底边界组件
        mock_bottomboundary = Mock()
        mock_bottomboundary.gwlevel_data = [(1, 1, -100.0), (31, 12, -100.0)]
        mock_model.bottomboundary = mock_bottomboundary
        
        return mock_model
    
    def test_inject_all_states_success(self):
        """测试成功注入所有状态变量"""
        with patch.object(StateInjector, '_validate_input_states', return_value=self.test_states), \
             patch.object(StateInjector, 'inject_soil_moisture', return_value=self.mock_model), \
             patch.object(StateInjector, 'inject_crop_state', return_value=self.mock_model), \
             patch.object(StateInjector, 'inject_groundwater_level', return_value=self.mock_model), \
             patch.object(StateInjector, '_validate_injection_result', return_value=True):
            
            result = StateInjector.inject_all_states(self.mock_model, self.test_states)
            
            assert result is not None
            self.mock_model.model_copy.assert_called_once_with(deep=True)
    
    def test_inject_all_states_with_exception(self):
        """测试注入过程中发生异常的情况"""
        with patch.object(StateInjector, '_validate_input_states', side_effect=Exception("Test error")):
            result = StateInjector.inject_all_states(self.mock_model, self.test_states)
            
            # 应该返回原始模型
            assert result == self.mock_model
    
    def test_validate_input_states(self):
        """测试输入状态验证"""
        states = {
            'soil_moisture': [0.3, 0.25, 0.2],
            'lai': 3.5,
            'invalid_state': None,
            'biomass': 'invalid_value'
        }
        
        with patch.object(StateInjector, '_validate_state_value') as mock_validate:
            mock_validate.side_effect = lambda name, value: value if name != 'biomass' else None
            
            result = StateInjector._validate_input_states(states)
            
            assert 'soil_moisture' in result
            assert 'lai' in result
            assert 'invalid_state' not in result
            assert 'biomass' not in result
    
    def test_validate_state_value_soil_moisture(self):
        """测试土壤含水量值验证"""
        # 测试正常范围内的值
        moisture = np.array([0.3, 0.25, 0.2])
        result = StateInjector._validate_state_value('soil_moisture', moisture)
        assert isinstance(result, np.ndarray)
        assert result.dtype == np.float64
        
        # 测试超出范围的值
        moisture_out_of_range = np.array([1.5, -0.1, 0.3])
        result = StateInjector._validate_state_value('soil_moisture', moisture_out_of_range)
        assert np.all(result >= 0) and np.all(result <= 1)
        
        # 测试列表输入
        moisture_list = [0.3, 0.25, 0.2]
        result = StateInjector._validate_state_value('soil_moisture', moisture_list)
        assert isinstance(result, np.ndarray)
    
    def test_validate_state_value_crop_states(self):
        """测试作物状态值验证"""
        # 测试LAI
        lai_result = StateInjector._validate_state_value('lai', 3.5)
        assert lai_result == 3.5
        
        # 测试生物量
        biomass_result = StateInjector._validate_state_value('biomass', 1500)
        assert biomass_result == 1500.0
        
        # 测试根深
        root_depth_result = StateInjector._validate_state_value('root_depth', 80)
        assert root_depth_result == 80.0
        
        # 测试地下水位
        gw_result = StateInjector._validate_state_value('groundwater_level', -150)
        assert gw_result == -150.0
    
    def test_inject_soil_moisture_success(self):
        """测试成功注入土壤含水量"""
        moisture = np.array([0.3, 0.25, 0.2, 0.15])
        
        result = StateInjector.inject_soil_moisture(self.mock_model, moisture)
        
        assert result is not None
        assert self.mock_model.soilmoisture.thetai == moisture.tolist()
    
    def test_inject_soil_moisture_no_component(self):
        """测试模型没有土壤含水量组件的情况"""
        mock_model_no_soil = Mock()
        mock_model_no_soil.model_copy = Mock(return_value=mock_model_no_soil)
        mock_model_no_soil.soilmoisture = None
        
        moisture = np.array([0.3, 0.25, 0.2])
        result = StateInjector.inject_soil_moisture(mock_model_no_soil, moisture)
        
        assert result == mock_model_no_soil
    
    def test_inject_soil_moisture_layer_mismatch(self):
        """测试土壤层数不匹配的情况"""
        moisture = np.array([0.3, 0.25])  # 只有2层，但模型有4层
        
        with patch.object(StateInjector, '_adjust_array_size', return_value=[0.3, 0.25, 0.25, 0.25]):
            result = StateInjector.inject_soil_moisture(self.mock_model, moisture)
            
            assert result is not None
    
    def test_adjust_array_size(self):
        """测试数组大小调整"""
        # 测试扩展数组
        array = [0.3, 0.25]
        result = StateInjector._adjust_array_size(array, 4)
        assert len(result) == 4
        assert result == [0.3, 0.25, 0.25, 0.25]
        
        # 测试截断数组
        array = [0.3, 0.25, 0.2, 0.15, 0.1]
        result = StateInjector._adjust_array_size(array, 3)
        assert len(result) == 3
        assert result == [0.3, 0.25, 0.2]
        
        # 测试相同大小
        array = [0.3, 0.25, 0.2]
        result = StateInjector._adjust_array_size(array, 3)
        assert result == array
        
        # 测试空数组
        array = []
        result = StateInjector._adjust_array_size(array, 2)
        assert result == [0.3, 0.3]  # 使用默认值
    
    def test_inject_crop_state_success(self):
        """测试成功注入作物状态"""
        with patch.object(StateInjector, '_inject_lai', return_value=self.mock_model), \
             patch.object(StateInjector, '_inject_biomass', return_value=self.mock_model), \
             patch.object(StateInjector, '_inject_root_depth', return_value=self.mock_model):
            
            result = StateInjector.inject_crop_state(
                self.mock_model, lai=3.5, biomass=1500.0, root_depth=80.0
            )
            
            assert result is not None
    
    def test_inject_crop_state_no_component(self):
        """测试模型没有作物组件的情况"""
        mock_model_no_crop = Mock()
        mock_model_no_crop.model_copy = Mock(return_value=mock_model_no_crop)
        mock_model_no_crop.crop = None
        
        result = StateInjector.inject_crop_state(mock_model_no_crop, lai=3.5)
        
        assert result == mock_model_no_crop
    
    def test_inject_lai(self):
        """测试LAI注入"""
        result = StateInjector._inject_lai(self.mock_model, 4.0)
        
        assert result is not None
        assert self.mock_model.crop.leaf_area_index == 4.0
    
    def test_inject_biomass(self):
        """测试生物量注入"""
        result = StateInjector._inject_biomass(self.mock_model, 2000.0)
        
        assert result is not None
        assert self.mock_model.crop.biomass == 2000.0
    
    def test_inject_root_depth(self):
        """测试根深注入"""
        # 创建一个简单的crop对象
        class MockCrop:
            def __init__(self):
                self.rds = 60.0

        self.mock_model.crop = MockCrop()

        result = StateInjector._inject_root_depth(self.mock_model, 100.0)

        assert result is not None
        assert self.mock_model.crop.rds == 100.0
    
    def test_estimate_development_stage_from_lai(self):
        """测试从LAI估算发育阶段"""
        assert StateInjector._estimate_development_stage_from_lai(0.3) == 0.2
        assert StateInjector._estimate_development_stage_from_lai(1.5) == 0.5
        assert StateInjector._estimate_development_stage_from_lai(3.0) == 0.7
        assert StateInjector._estimate_development_stage_from_lai(5.0) == 0.9
        assert StateInjector._estimate_development_stage_from_lai(7.0) == 1.0
    
    def test_inject_groundwater_level_success(self):
        """测试成功注入地下水位"""
        result = StateInjector.inject_groundwater_level(self.mock_model, -200.0)
        
        assert result is not None
        # 检查地下水位数据是否更新
        assert self.mock_model.bottomboundary.gwlevel_data[0][2] == -200.0
    
    def test_inject_groundwater_level_no_component(self):
        """测试模型没有底边界组件的情况"""
        mock_model_no_boundary = Mock()
        mock_model_no_boundary.model_copy = Mock(return_value=mock_model_no_boundary)
        mock_model_no_boundary.bottomboundary = None
        
        result = StateInjector.inject_groundwater_level(mock_model_no_boundary, -200.0)
        
        assert result == mock_model_no_boundary
    
    def test_validate_injection_result(self):
        """测试注入结果验证"""
        injected_states = {
            'soil_moisture': np.array([0.3, 0.25, 0.2, 0.15]),
            'lai': 3.5,
            'groundwater_level': -150.0
        }
        
        with patch.object(StateInjector, '_verify_soil_moisture_injection', return_value=True), \
             patch.object(StateInjector, '_verify_crop_state_injection', return_value=True), \
             patch.object(StateInjector, '_verify_groundwater_injection', return_value=True):
            
            result = StateInjector._validate_injection_result(self.mock_model, injected_states)
            
            assert result is True
    
    def test_verify_soil_moisture_injection(self):
        """测试土壤含水量注入验证"""
        expected_moisture = np.array([0.3, 0.25, 0.2, 0.15])
        self.mock_model.soilmoisture.thetai = [0.3, 0.25, 0.2, 0.15]
        
        result = StateInjector._verify_soil_moisture_injection(self.mock_model, expected_moisture)
        
        assert result is True
    
    def test_verify_crop_state_injection(self):
        """测试作物状态注入验证"""
        # 创建一个简单的对象来模拟crop组件
        class MockCrop:
            def __init__(self):
                self.leaf_area_index = 3.5
                self.biomass = 1500.0
                self.rds = 80.0

        self.mock_model.crop = MockCrop()

        # 测试LAI验证
        result = StateInjector._verify_crop_state_injection(self.mock_model, 'lai', 3.5)
        assert result is True

        # 测试生物量验证
        result = StateInjector._verify_crop_state_injection(self.mock_model, 'biomass', 1500.0)
        assert result is True

        # 测试根深验证
        result = StateInjector._verify_crop_state_injection(self.mock_model, 'root_depth', 80.0)
        assert result is True
    
    def test_verify_groundwater_injection(self):
        """测试地下水位注入验证"""
        self.mock_model.bottomboundary.gwlevel_data = [(1, 1, -150.0), (31, 12, -150.0)]
        
        result = StateInjector._verify_groundwater_injection(self.mock_model, -150.0)
        
        assert result is True


if __name__ == '__main__':
    pytest.main([__file__])

#!/usr/bin/env python3
"""地下水位数据 - 从标准案例hetao.BBC文件中提取

这个文件包含了从标准案例hetao.BBC文件中提取的完整地下水位数据。
即使SWOPT1=0也要包含这些数据，因为它们是标准案例的一部分。

数据格式: (day, month, value)
- day: 日期（天）
- month: 月份
- value: 地下水位值（cm）
"""

# 从标准案例hetao.BBC文件中提取的完整地下水位数据
GWLEVEL_DATA = [
    (2, 5, -151), (3, 5, -151), (4, 5, -151), (5, 5, -151), (6, 5, -151),
    (7, 5, -151), (8, 5, -151), (9, 5, -151), (10, 5, -151), (11, 5, -119.3),
    (12, 5, -87.6), (13, 5, -56), (14, 5, -45), (15, 5, -41.5), (16, 5, -38),
    (17, 5, -34.5), (18, 5, -31), (19, 5, -34.67), (20, 5, -38.34), (21, 5, -42),
    (22, 5, -50.5), (23, 5, -59), (24, 5, -67.5), (25, 5, -76), (26, 5, -79.86),
    (27, 5, -83.72), (28, 5, -87.58), (29, 5, -91.44), (30, 5, -95.3), (31, 5, -99.16),
    (1, 6, -103), (2, 6, -114.5), (3, 6, -126), (4, 6, -129), (5, 6, -132),
    (6, 6, -135), (7, 6, -138), (8, 6, -141), (9, 6, -144), (10, 6, -147),
    (11, 6, -148), (12, 6, -149), (13, 6, -150), (14, 6, -151), (15, 6, -152),
    (16, 6, -153), (17, 6, -154), (18, 6, -154.6), (19, 6, -155.2), (20, 6, -155.8),
    (21, 6, -156.4), (22, 6, -157), (23, 6, -149.6), (24, 6, -142.2), (25, 6, -134.8),
    (26, 6, -127.4), (27, 6, -120), (28, 6, -112), (29, 6, -104), (30, 6, -86),
    (1, 7, -68), (2, 7, -72.5), (3, 7, -77), (4, 7, -87.43), (5, 7, -97.86),
    (6, 7, -108.29), (7, 7, -118.72), (8, 7, -129.15), (9, 7, -139.58), (10, 7, -150),
    (11, 7, -154), (12, 7, -158), (13, 7, -162), (14, 7, -166), (15, 7, -150.33),
    (16, 7, -134.66), (17, 7, -119), (18, 7, -76), (19, 7, -76.33), (20, 7, -76.66),
    (21, 7, -77), (22, 7, -84.25), (23, 7, -91.5), (24, 7, -98.75), (25, 7, -106),
    (26, 7, -113.25), (27, 7, -120.5), (28, 7, -127.75), (29, 7, -135), (30, 7, -142.25),
    (31, 7, -149.5), (1, 8, -156.75), (2, 8, -164), (3, 8, -171.25), (4, 8, -178.5),
    (5, 8, -185.75), (6, 8, -193), (7, 8, -152), (8, 8, -156), (9, 8, -136),
    (10, 8, -103), (11, 8, -108.79), (12, 8, -114.58), (13, 8, -120.37), (14, 8, -126.16),
    (15, 8, -131.95), (16, 8, -137.74), (17, 8, -143.53), (18, 8, -149.32), (19, 8, -155.11),
    (20, 8, -160.9), (21, 8, -166.69), (22, 8, -172.48), (23, 8, -178.27), (24, 8, -184),
    (25, 8, -186.5), (26, 8, -189), (27, 8, -191.5), (28, 8, -194), (29, 8, -196.5),
    (30, 8, -199), (31, 8, -201.5), (1, 9, -204), (2, 9, -206.5), (3, 9, -209),
    (4, 9, -210), (5, 9, -211), (6, 9, -212), (7, 9, -213), (8, 9, -213.43),
    (9, 9, -213.86), (10, 9, -214.29), (11, 9, -214.72), (12, 9, -215.15), (13, 9, -215.58),
    (14, 9, -216.01), (15, 9, -216.44), (16, 9, -216.87), (17, 9, -217.3), (18, 9, -217.73),
    (19, 9, -218.16), (20, 9, -218.59), (21, 9, -219)
]

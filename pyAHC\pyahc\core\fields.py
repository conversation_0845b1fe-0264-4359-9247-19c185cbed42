"""pyAHC 序列化和反序列化的自定义字段类型。

结合 Pydantic 使用自定义 Annotated 字段，可以将对象序列化和反序列化为 AHC 中使用的适当格式。
当需要实现新类型的字段时，这种方法特别有用，设计应遵循以下模式：

```Python
CustonType = Annotated[
    <原生 Python 类型>,
    pydantic.AfterValidator(custom_parsing_function),
    pydantic.PlainSerializer(custom_serializer, return_type=<原生 Python 类型>, when_used="json"),
    pydantic.Field(<自定义字段参数>)
    ]
```

其中：

- <原生 Python 类型>: 用户必须提供的字段的原生 Python 类型（例如，int）。
- pydantic.AfterValidator (可选):
    Pydantic 验证器在用户传递的字段值最初解析和验证（例如，对于必需字段）后运行。
    然后它将自定义解析函数作为参数，并返回与 pyAHC 序列化兼容的最终值。
    有关自定义验证函数，请参阅 `pyahc.core.parsers` 模块。
- pydantic.PlainSerializer:
    当调用 Model.model_dump(mode="json") 时，会调用序列化器。
    在 pyAHC 内部，这在 PyAHCBaseModel.model_string() 方法中完成。
    它将自定义序列化函数作为参数，并以与 AHC 兼容的字符串格式返回参数。
    有关自定义序列化函数，请参阅 `pyahc.core.serializers` 模块。
- pydantic.Field:
    在 Pydantic 中，Field 对象用于定义字段的元数据。在 pyAHC 中，
    context 属性用于将附加信息传递给序列化函数。

然后在模型中使用自定义类型：

```Python
class AHCSection(PyAHCBaseModel):
    field: CustomType
```

此模块中的字段：

    Table (DataFrame): 一个 DataFrame 对象，序列化为仅包含标题和数据的字符串。
    Arrays (DataFrame): 一个 DataFrame 对象，序列化为仅包含数据列（无标题）的字符串，
        但前面带有变量名（例如，FLUXTB = 0.0 0.0/n 1.0 1.0）
    CSVTable (DataFrame): 一个 DataFrame 对象，序列化为 CSV 格式的包含标题和数据的字符串，
        专门为 .met 文件格式定制。
    DayMonth (d): 一个日期对象，序列化为仅包含日期和月份的字符串（例如，'01 01'）。
    StringList (List[str]): 一个字符串列表，序列化为以逗号分隔并用引号括起来的字符串
        （例如，'string1, string2, string3'）。
    FloatList (List[float]): 一个浮点数列表，序列化为以空格分隔的字符串。
    DateList (List[d]): 一个日期对象列表，序列化为以换行符分隔的字符串。
    Switch (bool | int): 一个布尔值或整数，序列化为整数（0 或 1）。
    ObjectList (list): 一个对象列表，序列化为以换行符分隔的字符串。
"""

from datetime import date
from decimal import Decimal
from typing import Annotated, TypeVar, Union as _Union, List as _List, Tuple as _Tuple, Optional as _Optional, Literal as _Literal
from enum import Enum as _Enum
import datetime as _datetime

from pandas import DataFrame
from pydantic import AfterValidator, BeforeValidator, Field
from pydantic.functional_serializers import PlainSerializer

from pyahc.core.basemodel import PyAHCBaseModel
from pyahc.core.parsers import (
    parse_day_month,
    parse_decimal,
    parse_float_list,
    parse_int_list,
    parse_quoted_string,
    parse_string_list,
)
from pyahc.core.serializers import (
    serialize_arrays,
    serialize_csv_table,
    serialize_day_month,
    serialize_decimal,
    serialize_table,
)

__all__ = [
    "Table",
    "Arrays",
    "CSVTable",
    "DayMonth",
    "StringList",
    "FloatList",
    "IntList",
    "DateList",
    "String",
    "File",
    "Subsection",
    "Decimal2f",
    "Decimal3f",
    "Decimal4f",
    # Enhanced strong type fields
    "PositiveFloat",
    "UnitFloat",
    "DepthFloat",
    "ConcentrationFloat",
    "PositiveInt",
    "NonNegativeInt",
    "PercentageFloat",
    "TemperatureFloat",
    # Validation functions
    "validate_positive",
    "validate_unit_range",
    "validate_depth",
    "validate_concentration",
    "validate_percentage",
    "validate_temperature",
    # Enums
    "SwitchValue",
    "ValidationLevel",
    # Complex types
    "DepthTuple",
    "DateTuple",
    "CoordinateTuple",
    # Range-based fields
    "UnitRangeFloat",
    "YearRangeInt",
    "DVSRangeFloat",
    # Utility functions
    "create_range_field",
]

Table = Annotated[
    DataFrame,
    PlainSerializer(serialize_table, return_type=str, when_used="json"),
    Field(json_schema_extra={"is_annotated_exception_type": True}),
]
"""将带有标题的 pd.DataFrame 序列化为不带前导变量名的字符串。"""

Arrays = Annotated[
    DataFrame, PlainSerializer(serialize_arrays, return_type=str, when_used="json")
]
"""将不带标题的 pd.DataFrame 序列化为带前导变量名的字符串。"""

CSVTable = Annotated[
    DataFrame,
    PlainSerializer(
        lambda x: serialize_csv_table(x), return_type=str, when_used="json"
    ),
]
"""将 pd.DataFrame 序列化为 CSV 格式的字符串。"""

DayMonth = Annotated[
    date | str,
    AfterValidator(parse_day_month),
    PlainSerializer(serialize_day_month, return_type=str, when_used="json"),
]
"""将日期对象序列化为仅包含日期和月份的字符串。"""

StringList = Annotated[
    list[str] | str,
    AfterValidator(parse_string_list),
    PlainSerializer(lambda x: f"'{','.join(x)}'", return_type=str, when_used="json"),
]
"""将字符串列表序列化为以逗号分隔的字符串。"""

FloatList = Annotated[
    list[float] | str,
    AfterValidator(parse_float_list),
    PlainSerializer(
        lambda x: " ".join([f"{Decimal(f):.2f}" for f in x]),
        return_type=str,
        when_used="json",
    ),
]
"""将浮点数列表序列化为以空格分隔的字符串。"""

IntList = Annotated[
    list[int] | str,
    AfterValidator(parse_int_list),
    PlainSerializer(
        lambda x: " ".join([str(f) for f in x]), return_type=str, when_used="json"
    ),
]
"""将整数列表序列化为以空格分隔的字符串。"""

DateList = Annotated[
    list[date],
    PlainSerializer(
        lambda x: "\n" + "\n".join([d.strftime("%Y-%m-%d") for d in x]),
        return_type=str,
        when_used="json",
    ),
]
"""将日期对象列表序列化为以换行符分隔的字符串。"""

String = Annotated[
    str,
    PlainSerializer(lambda x: f"'{x}'", return_type=str),
    AfterValidator(parse_quoted_string),
]
"""将字符串序列化为带引号的字符串。"""

File = Annotated[
    PyAHCBaseModel,
    PlainSerializer(lambda x: x.model_string(), return_type=str, when_used="json"),
    Field(json_schema_extra={"is_annotated_exception_type": True}),
]
"""将 PyAHCBaseModel 序列化为字符串。"""

SubsectionTypeVar = TypeVar("SubsectionTypeVar", bound=PyAHCBaseModel)

Subsection = Annotated[
    SubsectionTypeVar,
    PlainSerializer(lambda x: x.model_string(), return_type=str),
    Field(json_schema_extra={"is_annotated_exception_type": True}),
]
"""将嵌套的 PyAHCBaseModel 序列化为字符串。"""

Decimal2f = Annotated[
    float | str,
    BeforeValidator(parse_decimal),
    PlainSerializer(serialize_decimal(precision=2), return_type=str, when_used="json"),
]
"""将浮点数序列化为保留 2 位小数的字符串。"""

Decimal3f = Annotated[
    float,
    PlainSerializer(serialize_decimal(precision=3), return_type=str, when_used="json"),
]
"""将浮点数序列化为保留 3 位小数的字符串。"""

Decimal4f = Annotated[
    float,
    PlainSerializer(serialize_decimal(precision=4), return_type=str, when_used="json"),
]
"""将浮点数序列化为保留 4 位小数的字符串。"""


# =============================================================================
# Enhanced Strong Type Validation Fields
# =============================================================================

class SwitchValue(_Enum):
    """开关值枚举"""
    OFF = 0
    ON = 1


class ValidationLevel(_Enum):
    """验证级别"""
    BASIC = "basic"
    STRICT = "strict"
    COMPREHENSIVE = "comprehensive"


# =============================================================================
# Validation Functions
# =============================================================================

def validate_positive(v):
    """验证正数

    Args:
        v: 待验证的值

    Returns:
        验证后的值

    Raises:
        ValueError: 如果值不是正数
    """
    if v is not None and v <= 0:
        raise ValueError(f"Value must be positive, got {v}")
    return v


def validate_unit_range(v):
    """验证单位区间 [0.0, 1.0]

    Args:
        v: 待验证的值

    Returns:
        验证后的值

    Raises:
        ValueError: 如果值不在单位区间内
    """
    if v is not None and (v < 0.0 or v > 1.0):
        raise ValueError(f"Value must be between 0.0 and 1.0, got {v}")
    return v


def validate_depth(v):
    """验证深度值（负数或零）

    Args:
        v: 待验证的值

    Returns:
        验证后的值

    Raises:
        ValueError: 如果深度值为正数
    """
    if v is not None and v > 0:
        raise ValueError(f"Depth value must be <= 0, got {v}")
    return v


def validate_concentration(v):
    """验证浓度值（非负）

    Args:
        v: 待验证的值

    Returns:
        验证后的值

    Raises:
        ValueError: 如果浓度值为负数
    """
    if v is not None and v < 0:
        raise ValueError(f"Concentration must be non-negative, got {v}")
    return v


def validate_percentage(v):
    """验证百分比值 [0.0, 100.0]

    Args:
        v: 待验证的值

    Returns:
        验证后的值

    Raises:
        ValueError: 如果值不在百分比范围内
    """
    if v is not None and (v < 0.0 or v > 100.0):
        raise ValueError(f"Percentage must be between 0.0 and 100.0, got {v}")
    return v


def validate_temperature(v):
    """验证温度值（开尔文以上）

    Args:
        v: 待验证的值

    Returns:
        验证后的值

    Raises:
        ValueError: 如果温度低于绝对零度
    """
    if v is not None and v < -273.15:
        raise ValueError(f"Temperature must be >= -273.15°C, got {v}")
    return v


# =============================================================================
# Strong Type Field Definitions
# =============================================================================

PositiveFloat = Annotated[
    float,
    BeforeValidator(validate_positive),
    Field(gt=0.0, description="正浮点数"),
    PlainSerializer(serialize_decimal(precision=2), return_type=str, when_used="json"),
]
"""正浮点数字段，自动验证值必须大于0"""

UnitFloat = Annotated[
    float,
    BeforeValidator(validate_unit_range),
    Field(ge=0.0, le=1.0, description="单位区间浮点数"),
    PlainSerializer(serialize_decimal(precision=3), return_type=str, when_used="json"),
]
"""单位区间浮点数字段，值必须在[0.0, 1.0]范围内"""

DepthFloat = Annotated[
    float,
    BeforeValidator(validate_depth),
    Field(le=0.0, description="深度值（负数或零）"),
    PlainSerializer(serialize_decimal(precision=2), return_type=str, when_used="json"),
]
"""深度值字段，值必须小于等于0（负数表示深度）"""

ConcentrationFloat = Annotated[
    float,
    BeforeValidator(validate_concentration),
    Field(ge=0.0, description="浓度值（非负）"),
    PlainSerializer(serialize_decimal(precision=3), return_type=str, when_used="json"),
]
"""浓度值字段，值必须非负"""

PercentageFloat = Annotated[
    float,
    BeforeValidator(validate_percentage),
    Field(ge=0.0, le=100.0, description="百分比值"),
    PlainSerializer(serialize_decimal(precision=2), return_type=str, when_used="json"),
]
"""百分比值字段，值必须在[0.0, 100.0]范围内"""

TemperatureFloat = Annotated[
    float,
    BeforeValidator(validate_temperature),
    Field(ge=-273.15, description="温度值（开尔文以上）"),
    PlainSerializer(serialize_decimal(precision=2), return_type=str, when_used="json"),
]
"""温度值字段，值必须大于等于绝对零度"""

# Integer field types
PositiveInt = Annotated[
    int,
    Field(gt=0, description="正整数"),
]
"""正整数字段，值必须大于0"""

NonNegativeInt = Annotated[
    int,
    Field(ge=0, description="非负整数"),
]
"""非负整数字段，值必须大于等于0"""


# =============================================================================
# Complex Type Definitions
# =============================================================================

DepthTuple = _Tuple[int, int]
"""深度元组类型 (上边界, 下边界)"""

DateTuple = _Tuple[int, int, int]
"""日期元组类型 (日, 月, 年)"""

CoordinateTuple = _Tuple[float, float]
"""坐标元组类型 (经度, 纬度)"""


# =============================================================================
# Integration with valueranges.py
# =============================================================================

def create_range_field(range_def, description="", precision=2):
    """基于范围定义创建字段

    Args:
        range_def (dict): 包含 'ge' 和 'le' 键的范围定义
        description (str): 字段描述
        precision (int): 序列化精度

    Returns:
        Annotated field type: 配置好的字段类型
    """
    return Annotated[
        float,
        Field(**range_def, description=description),
        PlainSerializer(serialize_decimal(precision=precision), return_type=str, when_used="json"),
    ]


# Import valueranges and create range-based fields
try:
    from .valueranges import UNITRANGE, YEARRANGE, DVSRANGE

    UnitRangeFloat = create_range_field(UNITRANGE, "单位范围浮点数", precision=3)
    """基于UNITRANGE的单位范围浮点数字段"""

    YearRangeInt = Annotated[
        int,
        Field(**YEARRANGE, description="年份范围整数"),
    ]
    """基于YEARRANGE的年份范围整数字段"""

    DVSRangeFloat = create_range_field(DVSRANGE, "DVS范围浮点数", precision=3)
    """基于DVSRANGE的DVS范围浮点数字段"""

except ImportError:
    # Fallback definitions if valueranges.py is not available
    UnitRangeFloat = UnitFloat
    YearRangeInt = Annotated[int, Field(ge=0, le=366, description="年份范围整数")]
    DVSRangeFloat = Annotated[
        float,
        Field(ge=0.0, le=2.0, description="DVS范围浮点数"),
        PlainSerializer(serialize_decimal(precision=3), return_type=str, when_used="json"),
    ]

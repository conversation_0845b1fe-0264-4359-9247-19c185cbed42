"""StateExtractor单元测试"""
import unittest
import numpy as np
import pandas as pd
from unittest.mock import Mock, patch
import logging

# 设置测试环境
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from pyahc.db.hdf5.state_extractor import StateExtractor


class TestStateExtractor(unittest.TestCase):
    """StateExtractor测试类"""
    
    def setUp(self):
        """测试设置"""
        self.logger = logging.getLogger(__name__)
        
    def create_mock_result_with_csv(self, csv_data: dict) -> Mock:
        """创建带CSV数据的模拟Result对象"""
        result = Mock()
        result.csv = pd.DataFrame(csv_data)
        result.ascii = {}
        return result
    
    def create_mock_result_with_ascii(self, ascii_data: dict) -> Mock:
        """创建带ASCII数据的模拟Result对象"""
        result = Mock()
        result.csv = None
        result.ascii = ascii_data
        return result
    
    def create_mock_result_empty(self) -> Mock:
        """创建空的模拟Result对象"""
        result = Mock()
        result.csv = None
        result.ascii = {}
        return result
    
    def test_extract_soil_moisture_from_csv(self):
        """测试从CSV提取土壤含水量"""
        csv_data = {
            'moisture_layer1': [0.25, 0.26, 0.27],
            'moisture_layer2': [0.23, 0.24, 0.25],
            'moisture_layer3': [0.21, 0.22, 0.23],
            'other_var': [1, 2, 3]
        }
        result = self.create_mock_result_with_csv(csv_data)
        
        soil_moisture = StateExtractor.extract_soil_moisture(result)
        
        self.assertIsNotNone(soil_moisture)
        self.assertIsInstance(soil_moisture, np.ndarray)
        self.assertEqual(len(soil_moisture), 3)
        np.testing.assert_array_equal(soil_moisture, [0.27, 0.25, 0.23])
    
    def test_extract_soil_moisture_from_ascii(self):
        """测试从ASCII提取土壤含水量"""
        ascii_data = {
            'sba': """# Soil moisture data
Layer1  0.25
Layer2  0.23
Layer3  0.21
"""
        }
        result = self.create_mock_result_with_ascii(ascii_data)
        
        soil_moisture = StateExtractor.extract_soil_moisture(result)
        
        self.assertIsNotNone(soil_moisture)
        self.assertIsInstance(soil_moisture, np.ndarray)
        self.assertEqual(len(soil_moisture), 3)
        np.testing.assert_array_equal(soil_moisture, [0.25, 0.23, 0.21])
    
    def test_extract_crop_lai_from_csv(self):
        """测试从CSV提取LAI"""
        csv_data = {
            'lai': [1.0, 2.0, 3.0],
            'other_var': [10, 20, 30]
        }
        result = self.create_mock_result_with_csv(csv_data)
        
        lai = StateExtractor.extract_crop_lai(result)
        
        self.assertIsNotNone(lai)
        self.assertEqual(lai, 3.0)
    
    def test_extract_crop_biomass_from_csv(self):
        """测试从CSV提取生物量"""
        csv_data = {
            'biomass': [1000.0, 2000.0, 3000.0],
            'other_var': [10, 20, 30]
        }
        result = self.create_mock_result_with_csv(csv_data)
        
        biomass = StateExtractor.extract_crop_biomass(result)
        
        self.assertIsNotNone(biomass)
        self.assertEqual(biomass, 3000.0)
    
    def test_extract_root_depth_from_csv(self):
        """测试从CSV提取根深"""
        csv_data = {
            'root_depth': [10.0, 20.0, 30.0],
            'other_var': [10, 20, 30]
        }
        result = self.create_mock_result_with_csv(csv_data)
        
        root_depth = StateExtractor.extract_root_depth(result)
        
        self.assertIsNotNone(root_depth)
        self.assertEqual(root_depth, 30.0)
    
    def test_extract_groundwater_level_from_csv(self):
        """测试从CSV提取地下水位"""
        csv_data = {
            'groundwater': [-100.0, -120.0, -150.0],
            'other_var': [10, 20, 30]
        }
        result = self.create_mock_result_with_csv(csv_data)
        
        gw_level = StateExtractor.extract_groundwater_level(result)
        
        self.assertIsNotNone(gw_level)
        self.assertEqual(gw_level, -150.0)
    
    def test_extract_all_states_complete(self):
        """测试提取所有状态变量"""
        csv_data = {
            'moisture_layer1': [0.25],
            'moisture_layer2': [0.23],
            'lai': [2.5],
            'biomass': [3500.0],
            'root_depth': [45.0],
            'groundwater': [-150.0]
        }
        result = self.create_mock_result_with_csv(csv_data)
        
        states = StateExtractor.extract_all_states(result)
        
        self.assertIsInstance(states, dict)
        self.assertIn('soil_moisture', states)
        self.assertIn('lai', states)
        self.assertIn('biomass', states)
        self.assertIn('root_depth', states)
        self.assertIn('groundwater_level', states)
        
        # 验证具体值
        self.assertEqual(len(states['soil_moisture']), 2)
        self.assertEqual(states['lai'], 2.5)
        self.assertEqual(states['biomass'], 3500.0)
        self.assertEqual(states['root_depth'], 45.0)
        self.assertEqual(states['groundwater_level'], -150.0)
    
    def test_extract_from_empty_result(self):
        """测试从空结果提取"""
        result = self.create_mock_result_empty()
        
        soil_moisture = StateExtractor.extract_soil_moisture(result)
        lai = StateExtractor.extract_crop_lai(result)
        biomass = StateExtractor.extract_crop_biomass(result)
        
        self.assertIsNone(soil_moisture)
        self.assertIsNone(lai)
        self.assertIsNone(biomass)
    
    def test_validate_soil_moisture_valid(self):
        """测试土壤含水量验证 - 有效值"""
        valid_moisture = np.array([0.25, 0.30, 0.35])
        validated = StateExtractor._validate_single_variable('soil_moisture', valid_moisture)
        
        self.assertIsNotNone(validated)
        np.testing.assert_array_equal(validated, valid_moisture)
    
    def test_validate_soil_moisture_out_of_range(self):
        """测试土壤含水量验证 - 超出范围"""
        invalid_moisture = np.array([-0.1, 0.5, 1.2])
        validated = StateExtractor._validate_single_variable('soil_moisture', invalid_moisture)
        
        self.assertIsNotNone(validated)
        np.testing.assert_array_equal(validated, [0.0, 0.5, 1.0])
    
    def test_validate_lai_valid(self):
        """测试LAI验证 - 有效值"""
        valid_lai = 3.5
        validated = StateExtractor._validate_single_variable('lai', valid_lai)
        
        self.assertEqual(validated, 3.5)
    
    def test_validate_lai_out_of_range(self):
        """测试LAI验证 - 超出范围"""
        invalid_lai = 20.0
        validated = StateExtractor._validate_single_variable('lai', invalid_lai)
        
        self.assertEqual(validated, 15.0)
    
    def test_validate_biomass_valid(self):
        """测试生物量验证 - 有效值"""
        valid_biomass = 5000.0
        validated = StateExtractor._validate_single_variable('biomass', valid_biomass)
        
        self.assertEqual(validated, 5000.0)
    
    def test_validate_biomass_out_of_range(self):
        """测试生物量验证 - 超出范围"""
        invalid_biomass = 60000.0
        validated = StateExtractor._validate_single_variable('biomass', invalid_biomass)
        
        self.assertEqual(validated, 50000.0)
    
    def test_validate_extracted_states(self):
        """测试状态变量验证"""
        states = {
            'soil_moisture': np.array([0.25, 0.30]),
            'lai': 3.5,
            'biomass': 5000.0,
            'root_depth': 45.0,
            'groundwater_level': -150.0
        }
        
        validated_states = StateExtractor.validate_extracted_states(states)
        
        self.assertEqual(len(validated_states), 5)
        self.assertIn('soil_moisture', validated_states)
        self.assertIn('lai', validated_states)
        self.assertIn('biomass', validated_states)
        self.assertIn('root_depth', validated_states)
        self.assertIn('groundwater_level', validated_states)
    
    def test_parse_numeric_from_ascii(self):
        """测试从ASCII解析数值"""
        content = """# Test data
LAI: 2.5
Biomass: 3500.0 kg/ha
Root depth: 45.0 cm
"""
        
        lai_value = StateExtractor._parse_numeric_from_ascii(content, ['lai'])
        biomass_value = StateExtractor._parse_numeric_from_ascii(content, ['biomass'])
        root_value = StateExtractor._parse_numeric_from_ascii(content, ['root depth'])
        
        self.assertEqual(lai_value, 2.5)
        self.assertEqual(biomass_value, 3500.0)
        self.assertEqual(root_value, 45.0)
    
    def test_parse_soil_moisture_from_sba(self):
        """测试从SBA文件解析土壤含水量"""
        content = """# Soil moisture profile
# Layer  Moisture
1  0.25
2  0.23
3  0.21
"""

        moisture = StateExtractor._parse_soil_moisture_from_sba(content)

        self.assertIsNotNone(moisture)
        self.assertEqual(len(moisture), 3)
        np.testing.assert_array_equal(moisture, [0.25, 0.23, 0.21])

    def test_extract_all_states_with_missing_data(self):
        """测试提取状态变量时部分数据缺失"""
        csv_data = {
            'lai': [2.5],  # 只有LAI数据
            'other_var': [10]
        }
        result = self.create_mock_result_with_csv(csv_data)

        states = StateExtractor.extract_all_states(result)

        # 应该只提取到LAI
        self.assertEqual(len(states), 1)
        self.assertIn('lai', states)
        self.assertEqual(states['lai'], 2.5)

    def test_extract_from_csv_with_alternative_column_names(self):
        """测试从CSV提取时使用替代列名"""
        csv_data = {
            'theta_layer1': [0.25],  # 使用theta而不是moisture
            'leaf_area': [3.0],      # 使用leaf_area而不是lai
            'dry_matter': [4000.0],  # 使用dry_matter而不是biomass
            'water_table': [-120.0]  # 使用water_table而不是groundwater
        }
        result = self.create_mock_result_with_csv(csv_data)

        # 测试各种替代列名
        soil_moisture = StateExtractor.extract_soil_moisture(result)
        lai = StateExtractor.extract_crop_lai(result)
        biomass = StateExtractor.extract_crop_biomass(result)
        gw_level = StateExtractor.extract_groundwater_level(result)

        self.assertIsNotNone(soil_moisture)
        self.assertEqual(lai, 3.0)
        self.assertEqual(biomass, 4000.0)
        self.assertEqual(gw_level, -120.0)

    def test_ascii_parsing_with_invalid_data(self):
        """测试ASCII解析时遇到无效数据"""
        ascii_data = {
            'sba': """# Invalid soil moisture data
Layer1  invalid_value
Layer2  0.23
Layer3  text_data
"""
        }
        result = self.create_mock_result_with_ascii(ascii_data)

        soil_moisture = StateExtractor.extract_soil_moisture(result)

        # 应该只提取到有效的数值
        self.assertIsNotNone(soil_moisture)
        self.assertEqual(len(soil_moisture), 1)
        self.assertEqual(soil_moisture[0], 0.23)


if __name__ == '__main__':
    # 设置日志级别
    logging.basicConfig(level=logging.WARNING)

    # 运行测试
    unittest.main()

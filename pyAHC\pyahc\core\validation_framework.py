#!/usr/bin/env python3
"""
验证框架核心模块

该模块提供了 pyAHC 的统一验证管理器，实现验证规则加载器、验证报告生成器、
验证上下文管理器、批量验证功能，并集成所有验证组件，为整个验证框架提供核心管理功能。

主要功能：
- 统一的验证管理接口
- 灵活的验证规则加载
- 完整的验证报告系统
- 组件间依赖关系验证
- 验证上下文管理
- 批量验证支持

参考 pySWAP-main 项目的验证管理模式，使用下划线导入前缀。
"""

from typing import Dict as _Dict, Any as _Any, List as _List, Optional as _Optional, Type as _Type
import yaml as _yaml
import logging as _logging
from pathlib import Path as _Path

from pyahc.core.basemodel import PyAHCBaseModel as _PyAHCBaseModel

logger = _logging.getLogger(__name__)


class ValidationManager:
    """统一验证管理器
    
    该类提供了完整的验证管理功能，包括：
    - 验证规则加载和管理
    - 单个组件验证
    - 多组件模型验证
    - 组件间依赖关系验证
    - 验证报告生成
    - 验证摘要统计
    
    使用示例：
        manager = ValidationManager()
        report = manager.validate_component(component)
        model_report = manager.validate_model(components)
    """

    def __init__(self, validation_rules_path: _Optional[str] = None):
        """初始化验证管理器

        Args:
            validation_rules_path: 验证规则文件路径，默认为当前目录下的 "validation.yaml"
        """
        if validation_rules_path is None:
            # 使用当前文件所在目录的 validation.yaml
            from pathlib import Path as _Path
            current_dir = _Path(__file__).parent
            self.validation_rules_path = str(current_dir / "validation.yaml")
        else:
            self.validation_rules_path = validation_rules_path
        self.validation_rules = self._load_validation_rules()
        self.validation_reports = []

    def _load_validation_rules(self) -> _Dict[str, _Any]:
        """加载验证规则
        
        Returns:
            验证规则字典
        """
        try:
            with open(self.validation_rules_path, 'r', encoding='utf-8') as f:
                rules = _yaml.safe_load(f)
                logger.info(f"Loaded validation rules from {self.validation_rules_path}")
                return rules
        except FileNotFoundError:
            logger.warning(f"Validation rules file not found: {self.validation_rules_path}")
            return {}
        except _yaml.YAMLError as e:
            logger.error(f"Error parsing validation rules: {e}")
            return {}

    def validate_component(self, component: _PyAHCBaseModel) -> _Dict[str, _Any]:
        """验证单个组件
        
        Args:
            component: 要验证的组件实例
            
        Returns:
            验证报告字典，包含状态、错误和警告信息
        """
        component_name = component.__class__.__name__
        validation_report = {
            'component': component_name,
            'status': 'passed',
            'errors': [],
            'warnings': []
        }

        try:
            # 启用验证模式
            if hasattr(component, 'enable_validation'):
                component.enable_validation()

            # 执行组件特定的验证
            if hasattr(component, 'validate_with_yaml'):
                component.validate_with_yaml()

            # 执行自定义验证方法
            validation_methods = [method for method in dir(component)
                                if method.startswith('validate_') and callable(getattr(component, method))]

            for method_name in validation_methods:
                try:
                    method = getattr(component, method_name)
                    method()
                except Exception as e:
                    validation_report['errors'].append(f"{method_name}: {str(e)}")
                    validation_report['status'] = 'failed'

        except Exception as e:
            validation_report['errors'].append(f"General validation error: {str(e)}")
            validation_report['status'] = 'failed'

        self.validation_reports.append(validation_report)
        return validation_report

    def validate_model(self, components: _Dict[str, _PyAHCBaseModel]) -> _Dict[str, _Any]:
        """验证完整模型（多个组件）
        
        Args:
            components: 组件字典，键为组件名称，值为组件实例
            
        Returns:
            模型验证报告字典
        """
        model_report = {
            'model_status': 'passed',
            'component_reports': {},
            'cross_component_errors': []
        }

        # 验证各个组件
        for component_name, component in components.items():
            report = self.validate_component(component)
            model_report['component_reports'][component_name] = report
            
            if report['status'] == 'failed':
                model_report['model_status'] = 'failed'

        # 验证组件间依赖关系
        try:
            self._validate_component_dependencies(components)
        except Exception as e:
            model_report['cross_component_errors'].append(str(e))
            model_report['model_status'] = 'failed'

        return model_report

    def _validate_component_dependencies(self, components: _Dict[str, _PyAHCBaseModel]):
        """验证组件间依赖关系
        
        Args:
            components: 组件字典
            
        Raises:
            ValueError: 当依赖关系验证失败时
        """
        if 'ComponentDependencies' not in self.validation_rules:
            return

        dependencies = self.validation_rules['ComponentDependencies']
        
        for component_name, component in components.items():
            if component_name in dependencies:
                component_deps = dependencies[component_name]
                
                if 'depends_on' in component_deps:
                    for dependency in component_deps['depends_on']:
                        dep_component_name = dependency['component']
                        
                        if dep_component_name not in components:
                            raise ValueError(f"{component_name} depends on {dep_component_name}, but it's not provided")
                        
                        dep_component = components[dep_component_name]
                        
                        # 检查依赖条件
                        for condition in dependency.get('conditions', []):
                            # 简单的条件检查（可以扩展为更复杂的表达式解析）
                            if 'is not None' in condition:
                                attr_name = condition.split(' is not None')[0].strip()
                                if not hasattr(dep_component, attr_name) or getattr(dep_component, attr_name) is None:
                                    raise ValueError(f"Dependency condition failed: {condition}")

    def generate_report(self, output_path: _Optional[str] = None) -> str:
        """生成验证报告
        
        Args:
            output_path: 输出文件路径，如果提供则保存到文件
            
        Returns:
            验证报告内容字符串
        """
        report_lines = ["# pyAHC 验证报告\n"]

        for report in self.validation_reports:
            report_lines.append(f"## 组件: {report['component']}")
            report_lines.append(f"状态: {report['status']}")

            if report['errors']:
                report_lines.append("### 错误:")
                for error in report['errors']:
                    report_lines.append(f"- {error}")

            if report['warnings']:
                report_lines.append("### 警告:")
                for warning in report['warnings']:
                    report_lines.append(f"- {warning}")

            report_lines.append("")

        report_content = "\n".join(report_lines)

        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            logger.info(f"Validation report saved to {output_path}")

        return report_content

    def clear_reports(self):
        """清除验证报告"""
        self.validation_reports.clear()

    def get_validation_summary(self) -> _Dict[str, _Any]:
        """获取验证摘要
        
        Returns:
            验证摘要字典，包含统计信息
        """
        total_components = len(self.validation_reports)
        passed_components = sum(1 for report in self.validation_reports if report['status'] == 'passed')
        failed_components = total_components - passed_components

        return {
            'total_components': total_components,
            'passed_components': passed_components,
            'failed_components': failed_components,
            'success_rate': passed_components / total_components if total_components > 0 else 0.0
        }


# 全局验证管理器实例
validation_manager = ValidationManager()


class ValidationContext:
    """验证上下文管理器
    
    该类提供了验证状态的上下文管理功能，允许在特定代码块中
    临时启用或禁用组件的验证状态，并在退出时自动恢复原始状态。
    
    使用示例：
        with ValidationContext([component1, component2], enable_validation=True):
            # 在此上下文中，验证被启用
            pass
        # 退出上下文后，验证状态自动恢复
    """

    def __init__(self, components: _List[_PyAHCBaseModel], enable_validation: bool = True):
        """初始化验证上下文管理器
        
        Args:
            components: 组件列表或单个组件
            enable_validation: 是否启用验证
        """
        self.components = components if isinstance(components, list) else [components]
        self.enable_validation = enable_validation
        self.original_states = []

    def __enter__(self):
        """进入上下文管理器"""
        # 保存原始验证状态并设置新状态
        for component in self.components:
            original_state = getattr(component, '_validation', False)
            self.original_states.append(original_state)
            
            if hasattr(component, '_validation'):
                component._validation = self.enable_validation
        
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文管理器"""
        # 恢复原始验证状态
        for component, original_state in zip(self.components, self.original_states):
            if hasattr(component, '_validation'):
                component._validation = original_state


class AdvancedValidationManager(ValidationManager):
    """高级验证管理器

    扩展基础验证管理器，提供高级功能：
    - 验证结果缓存
    - 批量验证
    - 性能优化

    使用示例：
        manager = AdvancedValidationManager()
        result = manager.validate_with_cache(component)
        results = manager.batch_validate(components)
    """

    def __init__(self, validation_rules_path: _Optional[str] = None):
        """初始化高级验证管理器

        Args:
            validation_rules_path: 验证规则文件路径
        """
        super().__init__(validation_rules_path)
        self.validation_cache = {}

    def validate_with_cache(self, component: _PyAHCBaseModel) -> _Dict[str, _Any]:
        """带缓存的验证

        Args:
            component: 要验证的组件

        Returns:
            验证结果字典
        """
        component_hash = self._get_component_hash(component)

        if component_hash in self.validation_cache:
            logger.debug(f"Using cached validation result for {component.__class__.__name__}")
            return self.validation_cache[component_hash]

        result = self.validate_component(component)
        self.validation_cache[component_hash] = result
        return result

    def _get_component_hash(self, component: _PyAHCBaseModel) -> str:
        """获取组件哈希值用于缓存

        Args:
            component: 组件实例

        Returns:
            组件的哈希值字符串
        """
        import hashlib as _hashlib
        import json as _json

        # 简化的哈希计算（实际应用中可能需要更复杂的逻辑）
        try:
            component_dict = component.model_dump()
            component_str = _json.dumps(component_dict, sort_keys=True)
            return _hashlib.md5(component_str.encode()).hexdigest()
        except Exception as e:
            logger.warning(f"Failed to generate hash for {component.__class__.__name__}: {e}")
            # 如果无法生成哈希，返回基于类名和时间的简单标识
            import time as _time
            return f"{component.__class__.__name__}_{int(_time.time() * 1000000)}"

    def batch_validate(self, components: _List[_PyAHCBaseModel]) -> _List[_Dict[str, _Any]]:
        """批量验证

        Args:
            components: 组件列表

        Returns:
            验证结果列表
        """
        results = []
        for component in components:
            result = self.validate_with_cache(component)
            results.append(result)
        return results

    def clear_cache(self):
        """清除验证缓存"""
        self.validation_cache.clear()
        logger.debug("Validation cache cleared")


# 验证插件系统
from abc import ABC as _ABC, abstractmethod as _abstractmethod


class ValidationPlugin(_ABC):
    """验证插件基类

    该抽象基类定义了验证插件的接口，允许扩展验证框架的功能。
    插件可以实现特定的验证逻辑，如物理一致性检查、数据范围验证等。

    使用示例：
        class MyPlugin(ValidationPlugin):
            def get_plugin_name(self) -> str:
                return "MyValidator"

            def validate(self, component) -> dict:
                # 实现验证逻辑
                return {'status': 'passed', 'errors': [], 'warnings': []}

            def get_supported_components(self) -> list:
                return ["MyComponent"]
    """

    @_abstractmethod
    def get_plugin_name(self) -> str:
        """获取插件名称

        Returns:
            插件名称字符串
        """
        pass

    @_abstractmethod
    def validate(self, component: _PyAHCBaseModel) -> _Dict[str, _Any]:
        """执行验证

        Args:
            component: 要验证的组件实例

        Returns:
            验证结果字典，包含状态、错误和警告信息
        """
        pass

    @_abstractmethod
    def get_supported_components(self) -> _List[str]:
        """获取支持的组件类型

        Returns:
            支持的组件类名列表
        """
        pass


class PhysicalConsistencyPlugin(ValidationPlugin):
    """物理一致性验证插件

    该插件验证组件参数的物理一致性，确保参数值在物理上是合理的。
    例如，验证温度参数的合理范围、地下水位与排水参数的一致性等。
    """

    def get_plugin_name(self) -> str:
        """获取插件名称"""
        return "PhysicalConsistencyValidator"

    def get_supported_components(self) -> _List[str]:
        """获取支持的组件类型"""
        return ["BottomBoundary", "EpicCrop", "SoilWater"]

    def validate(self, component: _PyAHCBaseModel) -> _Dict[str, _Any]:
        """验证物理参数的一致性

        Args:
            component: 要验证的组件

        Returns:
            验证结果字典
        """
        result = {'status': 'passed', 'errors': [], 'warnings': []}

        component_name = component.__class__.__name__

        if component_name == "BottomBoundary":
            # 验证地下水位和排水参数的物理一致性
            if hasattr(component, 'hdrain') and hasattr(component, 'gwlevel_data'):
                hdrain = getattr(component, 'hdrain', None)
                gwlevel_data = getattr(component, 'gwlevel_data', None)

                if hdrain and gwlevel_data:
                    try:
                        min_gwlevel = min(level for _, _, level in gwlevel_data)
                        if hdrain > min_gwlevel:
                            result['errors'].append(
                                f"排水基础 ({hdrain}) 不应高于最低地下水位 ({min_gwlevel})"
                            )
                            result['status'] = 'failed'
                    except (TypeError, ValueError) as e:
                        result['warnings'].append(f"无法验证地下水位数据格式: {e}")

        elif component_name == "EpicCrop":
            # 验证作物参数的合理性
            if hasattr(component, 'tba') and hasattr(component, 'topt'):
                tba = getattr(component, 'tba', None)
                topt = getattr(component, 'topt', None)

                if tba is not None and topt is not None and tba >= topt:
                    result['errors'].append(
                        f"基础温度 ({tba}) 不应高于或等于最适温度 ({topt})"
                    )
                    result['status'] = 'failed'

        return result


class PluginManager:
    """验证插件管理器

    该类管理验证插件的注册、查找和执行。支持动态注册插件，
    并根据组件类型自动选择合适的插件进行验证。

    使用示例：
        plugin_manager = PluginManager()
        plugin_manager.register_plugin(MyPlugin())
        results = plugin_manager.validate_with_plugins(component)
    """

    def __init__(self):
        """初始化插件管理器"""
        self.plugins = {}

    def register_plugin(self, plugin: ValidationPlugin):
        """注册验证插件

        Args:
            plugin: 验证插件实例
        """
        plugin_name = plugin.get_plugin_name()
        self.plugins[plugin_name] = plugin
        logger.debug(f"Registered validation plugin: {plugin_name}")

    def get_plugins_for_component(self, component_name: str) -> _List[ValidationPlugin]:
        """获取支持指定组件的插件

        Args:
            component_name: 组件类名

        Returns:
            支持该组件的插件列表
        """
        return [
            plugin for plugin in self.plugins.values()
            if component_name in plugin.get_supported_components()
        ]

    def validate_with_plugins(self, component: _PyAHCBaseModel) -> _List[_Dict[str, _Any]]:
        """使用插件验证组件

        Args:
            component: 要验证的组件

        Returns:
            插件验证结果列表
        """
        component_name = component.__class__.__name__
        plugins = self.get_plugins_for_component(component_name)

        results = []
        for plugin in plugins:
            try:
                result = plugin.validate(component)
                result['plugin'] = plugin.get_plugin_name()
                results.append(result)
            except Exception as e:
                results.append({
                    'plugin': plugin.get_plugin_name(),
                    'status': 'error',
                    'errors': [f"Plugin error: {str(e)}"],
                    'warnings': []
                })

        return results


class EnhancedValidationManager(ValidationManager):
    """增强的验证管理器 - 支持插件系统

    扩展基础验证管理器，集成插件系统，提供更全面的验证功能。
    支持基础验证、插件验证和结果合并。

    使用示例：
        manager = EnhancedValidationManager()
        report = manager.validate_component_with_plugins(component)
    """

    def __init__(self, validation_rules_path: _Optional[str] = None):
        """初始化增强验证管理器

        Args:
            validation_rules_path: 验证规则文件路径
        """
        super().__init__(validation_rules_path)
        self.plugin_manager = PluginManager()
        self._register_default_plugins()

    def _register_default_plugins(self):
        """注册默认插件"""
        self.plugin_manager.register_plugin(PhysicalConsistencyPlugin())

    def validate_component_with_plugins(self, component: _PyAHCBaseModel) -> _Dict[str, _Any]:
        """使用插件验证组件

        Args:
            component: 要验证的组件

        Returns:
            增强的验证报告，包含基础验证和插件验证结果
        """
        # 执行基础验证
        base_report = self.validate_component(component)

        # 执行插件验证
        plugin_results = self.plugin_manager.validate_with_plugins(component)

        # 合并结果
        enhanced_report = base_report.copy()
        enhanced_report['plugin_results'] = plugin_results

        # 检查插件验证是否有失败
        for plugin_result in plugin_results:
            if plugin_result['status'] == 'failed':
                enhanced_report['status'] = 'failed'
                enhanced_report['errors'].extend(plugin_result['errors'])
            elif plugin_result['status'] == 'error':
                enhanced_report['warnings'].append(f"Plugin error: {plugin_result['plugin']}")

        return enhanced_report

    def register_plugin(self, plugin: ValidationPlugin):
        """注册新的验证插件

        Args:
            plugin: 验证插件实例
        """
        self.plugin_manager.register_plugin(plugin)

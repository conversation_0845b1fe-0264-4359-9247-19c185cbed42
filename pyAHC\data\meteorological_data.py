#!/usr/bin/env python3
"""气象数据管理模块 - 支持数据同化的动态气象数据

这个模块提供了类似于 gwlevel_data.py 的气象数据管理方式，
支持数据同化中每天动态添加气象数据的需求。

主要功能：
1. 从标准案例文件中提取完整的气象数据数组
2. 支持动态获取指定日期范围的气象数据
3. 支持逐日添加新的气象数据
4. 支持预测模式（添加未来几天的气象数据）

数据格式: (day, month, year, rad, tmin, tmax, hum, wind, rain, etref, wet)
- day: 日期（天）
- month: 月份
- year: 年份
- rad: 辐射 (kJ/m2)
- tmin: 最低温度 (°C)
- tmax: 最高温度 (°C)
- hum: 湿度 (kPa)
- wind: 风速 (m/s)
- rain: 降雨 (mm)
- etref: 参考蒸发 (mm)
- wet: 湿润度
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import List, Tuple, Optional, Union

# 从标准案例 linhe.013 文件中提取的完整气象数据
# 格式: (day, month, year, rad, tmin, tmax, hum, wind, rain, etref, wet)
METEOROLOGICAL_DATA = [
    (2, 5, 2013, 26514.0, 10.5, 26.1, 0.460, 1.43, 0.00, -99.0, 1.0),
    (3, 5, 2013, 26445.9, 6.0, 27.8, 0.500, 0.38, 0.00, -99.0, 1.0),
    (4, 5, 2013, 27197.8, 10.9, 29.5, 0.500, 2.03, 0.00, -99.0, 1.0),
    (5, 5, 2013, 25067.8, 13.7, 27.8, 0.830, 2.63, 0.00, -99.0, 1.0),
    (6, 5, 2013, 14820.7, 16.8, 25.0, 1.240, 2.40, 0.00, -99.0, 1.0),
    (7, 5, 2013, 18994.8, 13.0, 25.5, 1.430, 1.35, 0.00, -99.0, 1.0),
    (8, 5, 2013, 15461.5, 10.3, 19.6, 0.900, 1.58, 1.20, -99.0, 1.0),
    (9, 5, 2013, 28198.7, 6.8, 19.6, 0.360, 2.25, 0.00, -99.0, 1.0),
    (10, 5, 2013, 28394.4, 7.1, 25.5, 0.320, 2.10, 0.00, -99.0, 1.0),
    (11, 5, 2013, 26376.0, 10.8, 30.2, 0.360, 3.60, 0.00, -99.0, 1.0),
    (12, 5, 2013, 25043.5, 14.0, 28.9, 0.360, 2.85, 0.00, -99.0, 1.0),
    (13, 5, 2013, 27725.3, 11.9, 24.6, 0.230, 2.85, 0.00, -99.0, 1.0),
    (14, 5, 2013, 26249.2, 7.6, 23.8, 0.480, 1.50, 0.00, -99.0, 1.0),
    (15, 5, 2013, 9912.8, 15.9, 23.2, 0.890, 2.33, 2.80, -99.0, 1.0),
    (16, 5, 2013, 12028.0, 13.5, 19.4, 1.570, 0.98, 4.60, -99.0, 1.0),
    (17, 5, 2013, 28470.5, 6.9, 25.6, 0.810, 2.03, 0.20, -99.0, 1.0),
    (18, 5, 2013, 23085.9, 8.8, 25.5, 0.670, 3.90, 0.00, -99.0, 1.0),
    (19, 5, 2013, 29113.1, 10.0, 19.7, 0.370, 2.18, 0.00, -99.0, 1.0),
    (20, 5, 2013, 27620.5, 6.5, 31.0, 0.670, 1.35, 0.00, -99.0, 1.0),
    (21, 5, 2013, 28076.9, 13.5, 31.4, 0.760, 0.98, 0.00, -99.0, 1.0),
    (22, 5, 2013, 28113.6, 15.4, 32.6, 0.980, 2.25, 0.00, -99.0, 1.0),
    (23, 5, 2013, 26473.1, 18.8, 26.9, 0.890, 4.43, 0.00, -99.0, 1.0),
    (24, 5, 2013, 29439.9, 12.5, 26.2, 0.740, 0.75, 0.00, -99.0, 1.0),
    (25, 5, 2013, 28354.4, 12.9, 30.4, 0.980, 1.65, 0.00, -99.0, 1.0),
    (26, 5, 2013, 25727.9, 16.5, 31.3, 1.250, 1.73, 0.40, -99.0, 1.0),
    (27, 5, 2013, 10225.6, 16.5, 28.5, 1.310, 2.48, 1.40, -99.0, 1.0),
    (28, 5, 2013, 28722.2, 13.1, 19.9, 0.340, 3.60, 0.00, -99.0, 1.0),
    (29, 5, 2013, 29028.7, 5.1, 25.6, 0.440, 2.25, 0.00, -99.0, 1.0),
    (30, 5, 2013, 26532.6, 11.8, 29.5, 0.460, 2.10, 0.00, -99.0, 1.0),
    (31, 5, 2013, 28237.0, 15.0, 28.3, 0.600, 1.58, 0.00, -99.0, 1.0),
    # 6月数据
    (1, 6, 2013, 21671.7, 16.3, 29.4, 0.610, 2.18, 0.00, -99.0, 1.0),
    (2, 6, 2013, 28560.3, 14.2, 30.6, 0.620, 1.05, 0.00, -99.0, 1.0),
    (3, 6, 2013, 29000.5, 14.0, 34.8, 0.760, 0.75, 0.00, -99.0, 1.0),
    (4, 6, 2013, 23268.2, 18.9, 34.1, 1.000, 0.83, 0.00, -99.0, 1.0),
    (5, 6, 2013, 24827.2, 22.1, 35.8, 1.240, 2.10, 0.00, -99.0, 1.0),
    (6, 6, 2013, 23298.8, 18.2, 29.7, 1.280, 1.58, 0.20, -99.0, 1.0),
    (7, 6, 2013, 27522.3, 16.6, 33.4, 1.580, 1.35, 0.00, -99.0, 1.0),
    (8, 6, 2013, 11956.6, 13.1, 30.0, 1.520, 1.88, 6.80, -99.0, 1.0),
    (9, 6, 2013, 27125.8, 10.0, 22.0, 0.910, 1.20, 0.00, -99.0, 1.0),
    (10, 6, 2013, 24328.8, 12.5, 22.8, 0.970, 2.10, 1.20, -99.0, 1.0),
    (11, 6, 2013, 30093.6, 9.0, 27.4, 0.980, 2.03, 0.00, -99.0, 1.0),
    (12, 6, 2013, 27153.6, 12.4, 31.7, 0.800, 2.10, 0.00, -99.0, 1.0),
    (13, 6, 2013, 20983.1, 19.4, 27.3, 1.260, 1.20, 0.00, -99.0, 1.0),
    (14, 6, 2013, 28289.4, 15.1, 29.5, 1.590, 2.40, 0.00, -99.0, 1.0),
    (15, 6, 2013, 16079.4, 19.1, 28.2, 1.870, 1.73, 10.00, -99.0, 1.0),
    (16, 6, 2013, 26893.3, 15.8, 25.6, 1.680, 2.03, 4.40, -99.0, 1.0),
    (17, 6, 2013, 11452.8, 15.8, 23.4, 1.730, 0.83, 0.00, -99.0, 1.0),
    (18, 6, 2013, 19877.6, 16.7, 25.1, 1.260, 1.58, 0.00, -99.0, 1.0),
    (19, 6, 2013, 10473.1, 17.3, 23.4, 1.530, 0.83, 0.00, -99.0, 1.0),
    (20, 6, 2013, 11315.5, 15.1, 22.1, 1.650, 2.03, 8.80, -99.0, 1.0),
    (21, 6, 2013, 18332.1, 15.4, 25.3, 1.620, 0.60, 0.00, -99.0, 1.0),
    (22, 6, 2013, 26468.9, 15.3, 28.7, 1.800, 1.28, 0.00, -99.0, 1.0),
    (23, 6, 2013, 29972.2, 15.1, 31.5, 1.430, 2.63, 0.00, -99.0, 1.0),
    (24, 6, 2013, 27581.2, 15.5, 32.9, 1.210, 1.88, 0.00, -99.0, 1.0),
    (25, 6, 2013, 29818.6, 18.5, 29.7, 1.140, 2.63, 0.00, -99.0, 1.0),
    (26, 6, 2013, 27426.0, 15.8, 29.8, 1.250, 0.98, 0.60, -99.0, 1.0),
    (27, 6, 2013, 27557.0, 12.8, 32.4, 1.100, 1.50, 0.00, -99.0, 1.0),
    (28, 6, 2013, 18854.9, 18.9, 32.1, 0.980, 2.03, 0.00, -99.0, 1.0),
    (29, 6, 2013, 27254.9, 18.7, 35.1, 1.910, 2.70, 1.40, -99.0, 1.0),
    (30, 6, 2013, 27802.8, 19.2, 30.6, 1.860, 2.25, 0.00, -99.0, 1.0),
    # 7月数据
    (1, 7, 2013, 19943.9, 16.8, 28.1, 1.930, 1.35, 2.60, -99.0, 1.0),
    (2, 7, 2013, 29454.5, 15.5, 32.8, 1.840, 1.50, 0.00, -99.0, 1.0),
    (3, 7, 2013, 15576.8, 19.9, 28.9, 1.710, 1.50, 0.00, -99.0, 1.0),
    (4, 7, 2013, 29419.8, 17.2, 33.0, 1.590, 1.73, 0.00, -99.0, 1.0),
    (5, 7, 2013, 27581.7, 16.1, 33.4, 1.400, 1.35, 0.20, -99.0, 1.0),
    (6, 7, 2013, 29520.1, 18.0, 36.6, 1.520, 1.35, 0.00, -99.0, 1.0),
    (7, 7, 2013, 17753.6, 19.6, 31.8, 1.820, 0.90, 0.40, -99.0, 1.0),
    (8, 7, 2013, 22766.8, 13.9, 28.1, 1.420, 0.68, 0.00, -99.0, 1.0),
    (9, 7, 2013, 16318.8, 18.4, 28.8, 1.750, 0.83, 0.00, -99.0, 1.0),
    (10, 7, 2013, 24258.7, 19.3, 30.2, 1.650, 0.98, 0.00, -99.0, 1.0),
    (11, 7, 2013, 24792.6, 20.2, 30.7, 1.690, 1.13, 0.00, -99.0, 1.0),
    (12, 7, 2013, 21418.3, 16.0, 31.5, 1.320, 1.28, 0.00, -99.0, 1.0),
    (13, 7, 2013, 27528.2, 17.7, 32.6, 1.470, 0.68, 0.00, -99.0, 1.0),
    (14, 7, 2013, 10774.6, 22.1, 29.3, 2.110, 1.65, 0.00, -99.0, 1.0),
    (15, 7, 2013, 28720.6, 18.2, 26.8, 1.690, 1.43, 0.00, -99.0, 1.0),
    (16, 7, 2013, 29800.9, 13.2, 31.6, 1.250, 0.83, 0.00, -99.0, 1.0),
    (17, 7, 2013, 26009.3, 17.5, 29.6, 1.450, 1.43, 0.00, -99.0, 1.0),
    (18, 7, 2013, 15131.2, 17.7, 27.8, 1.490, 1.65, 0.20, -99.0, 1.0),
    (19, 7, 2013, 29273.4, 15.9, 29.3, 1.320, 1.20, 0.00, -99.0, 1.0),
    (20, 7, 2013, 28817.4, 14.3, 33.3, 1.470, 1.13, 0.00, -99.0, 1.0),
    (21, 7, 2013, 19482.4, 18.9, 31.6, 1.960, 1.50, 0.80, -99.0, 1.0),
    (22, 7, 2013, 28180.1, 17.7, 30.0, 1.980, 0.83, 0.00, -99.0, 1.0),
    (23, 7, 2013, 27998.4, 17.9, 31.8, 1.920, 1.35, 0.00, -99.0, 1.0),
    (24, 7, 2013, 23387.4, 21.3, 33.0, 1.800, 1.65, 0.00, -99.0, 1.0),
    (25, 7, 2013, 19197.9, 20.1, 29.8, 1.930, 1.28, 0.00, -99.0, 1.0),
    (26, 7, 2013, 9904.7, 18.9, 26.4, 2.260, 1.50, 2.20, -99.0, 1.0),
    (27, 7, 2013, 22705.4, 16.4, 28.6, 1.990, 1.50, 0.00, -99.0, 1.0),
    (28, 7, 2013, 20730.1, 19.4, 29.2, 1.790, 2.03, 0.00, -99.0, 1.0),
    (29, 7, 2013, 27156.6, 16.3, 34.0, 1.790, 1.20, 0.00, -99.0, 1.0),
    (30, 7, 2013, 24627.6, 18.9, 33.3, 1.660, 1.20, 0.00, -99.0, 1.0),
    (31, 7, 2013, 25262.4, 20.8, 31.7, 1.300, 1.20, 0.00, -99.0, 1.0),
    # 8月数据
    (1, 8, 2013, 26442.7, 15.9, 32.5, 1.390, 1.05, 0.00, -99.0, 1.0),
    (2, 8, 2013, 24329.7, 17.6, 30.8, 1.150, 1.05, 0.00, -99.0, 1.0),
    (3, 8, 2013, 27557.5, 14.6, 32.4, 1.270, 0.98, 0.00, -99.0, 1.0),
    (4, 8, 2013, 27085.0, 16.4, 33.3, 1.480, 1.13, 0.00, -99.0, 1.0),
    (5, 8, 2013, 23064.0, 19.0, 33.1, 1.820, 1.28, 0.00, -99.0, 1.0),
    (6, 8, 2013, 15646.7, 16.5, 28.1, 1.880, 2.25, 0.00, -99.0, 1.0),
    (7, 8, 2013, 23623.8, 15.4, 28.7, 1.880, 2.93, 0.60, -99.0, 1.0),
    (8, 8, 2013, 27091.6, 16.9, 32.3, 1.610, 1.35, 0.00, -99.0, 1.0),
    (9, 8, 2013, 26613.8, 14.6, 35.1, 1.410, 0.98, 0.00, -99.0, 1.0),
    (10, 8, 2013, 15306.3, 21.6, 30.0, 1.960, 2.85, 14.80, -99.0, 1.0),
    (11, 8, 2013, 26332.7, 17.9, 31.0, 1.930, 1.05, 0.20, -99.0, 1.0),
    (12, 8, 2013, 25852.7, 16.6, 34.9, 1.590, 1.13, 0.00, -99.0, 1.0),
    (13, 8, 2013, 25641.6, 20.8, 35.3, 1.780, 1.35, 0.00, -99.0, 1.0),
    (14, 8, 2013, 25563.7, 22.6, 36.0, 1.960, 1.95, 0.00, -99.0, 1.0),
    (15, 8, 2013, 18914.6, 23.4, 32.2, 2.050, 1.95, 0.00, -99.0, 1.0),
    (16, 8, 2013, 12960.5, 19.5, 28.1, 1.440, 1.95, 0.00, -99.0, 1.0),
    (17, 8, 2013, 15574.4, 12.2, 25.1, 1.200, 1.50, 0.00, -99.0, 1.0),
    (18, 8, 2013, 8985.1, 17.6, 26.4, 1.370, 0.60, 0.00, -99.0, 1.0),
    (19, 8, 2013, 8936.3, 19.6, 23.6, 1.470, 2.55, 2.40, -99.0, 1.0),
    (20, 8, 2013, 24131.5, 15.0, 26.1, 1.720, 1.05, 0.20, -99.0, 1.0),
    (21, 8, 2013, 24440.3, 14.4, 29.9, 1.630, 1.88, 0.00, -99.0, 1.0),
    (22, 8, 2013, 15908.3, 19.9, 27.3, 1.600, 1.35, 0.00, -99.0, 1.0),
    (23, 8, 2013, 25046.6, 13.6, 29.1, 1.370, 0.90, 0.00, -99.0, 1.0),
    (24, 8, 2013, 25081.5, 14.8, 31.6, 1.220, 0.75, 0.00, -99.0, 1.0),
    (25, 8, 2013, 24329.0, 15.8, 32.8, 1.580, 0.75, 0.00, -99.0, 1.0),
    (26, 8, 2013, 23056.7, 15.6, 33.3, 1.290, 2.33, 0.00, -99.0, 1.0),
    (27, 8, 2013, 8522.8, 20.0, 27.0, 1.940, 1.80, 0.40, -99.0, 1.0),
    (28, 8, 2013, 23380.7, 16.9, 29.2, 1.350, 2.85, 0.00, -99.0, 1.0),
    (29, 8, 2013, 24313.5, 13.4, 23.8, 0.960, 2.63, 0.00, -99.0, 1.0),
    (30, 8, 2013, 24464.9, 6.3, 23.7, 0.800, 0.68, 0.00, -99.0, 1.0),
    (31, 8, 2013, 24099.1, 9.2, 28.5, 0.980, 0.83, 0.00, -99.0, 1.0),
    # 9月数据
    (1, 9, 2013, 21045.4, 11.5, 28.3, 1.020, 1.35, 0.00, -99.0, 1.0),
    (2, 9, 2013, 21071.7, 14.3, 29.2, 1.280, 2.03, 0.00, -99.0, 1.0),
    (3, 9, 2013, 12579.3, 14.1, 21.7, 1.500, 1.73, 0.20, -99.0, 1.0),
    (4, 9, 2013, 23143.8, 12.1, 23.6, 1.120, 0.98, 0.00, -99.0, 1.0),
    (5, 9, 2013, 22146.1, 8.4, 27.0, 1.110, 1.13, 0.00, -99.0, 1.0),
    (6, 9, 2013, 23039.2, 8.8, 25.4, 0.780, 1.28, 0.00, -99.0, 1.0),
    (7, 9, 2013, 22044.7, 9.4, 27.8, 1.050, 1.05, 0.00, -99.0, 1.0),
    (8, 9, 2013, 14819.8, 15.0, 22.2, 1.500, 1.80, 6.40, -99.0, 1.0),
    (9, 9, 2013, 17215.5, 10.7, 23.7, 1.490, 0.98, 1.80, -99.0, 1.0),
    (10, 9, 2013, 20826.7, 7.9, 26.2, 1.240, 1.35, 0.00, -99.0, 1.0),
    (11, 9, 2013, 21942.0, 8.6, 29.2, 1.090, 2.18, 0.00, -99.0, 1.0),
    (12, 9, 2013, 19611.4, 12.7, 28.8, 1.050, 1.88, 0.00, -99.0, 1.0),
    (13, 9, 2013, 19741.0, 14.5, 27.6, 0.700, 3.08, 0.00, -99.0, 1.0),
    (14, 9, 2013, 21568.9, 12.7, 25.2, 0.810, 1.13, 0.00, -99.0, 1.0),
    (15, 9, 2013, 20474.1, 9.7, 29.2, 0.990, 1.20, 0.00, -99.0, 1.0),
    (16, 9, 2013, 20350.5, 11.9, 26.7, 1.320, 1.50, 0.00, -99.0, 1.0),
    (17, 9, 2013, 15791.2, 14.2, 25.8, 1.660, 2.93, 0.00, -99.0, 1.0),
    (18, 9, 2013, 7218.5, 16.5, 21.0, 1.860, 2.10, 10.20, -99.0, 1.0),
    (19, 9, 2013, 20922.8, 10.0, 24.6, 1.200, 1.65, 0.00, -99.0, 1.0),
    (20, 9, 2013, 20081.7, 10.4, 25.8, 1.180, 1.50, 0.00, -99.0, 1.0),
    (21, 9, 2013, 20421.7, 10.9, 26.6, 1.250, 2.10, 0.00, -99.0, 1.0),
    (22, 9, 2013, 15168.0, 14.0, 20.8, 1.090, 2.32, 0.00, -99.0, 1.0),
    (23, 9, 2013, 19949.0, 3.9, 14.7, 0.470, 2.65, 0.00, -99.0, 1.0),
    (24, 9, 2013, 20163.0, 0.2, 16.6, 0.660, 0.52, 0.00, -99.0, 1.0),
    (25, 9, 2013, 19912.0, 0.2, 19.6, 0.740, 0.90, 0.00, -99.0, 1.0)
]


class MeteorologicalDataManager:
    """气象数据管理器 - 支持数据同化的动态气象数据管理"""
    
    def __init__(self, station_name: str = "linhe"):
        """初始化气象数据管理器
        
        Args:
            station_name: 气象站名称
        """
        self.station_name = station_name
        self.base_data = []
        self.additional_data = []  # 动态添加的数据
        self._load_base_data()
    
    def _load_base_data(self):
        """加载基础气象数据"""
        self.base_data = METEOROLOGICAL_DATA.copy()
    

    
    def get_data_up_to_date(self, target_date: Union[datetime, Tuple[int, int, int]]) -> List[Tuple]:
        """获取到指定日期为止的所有气象数据
        
        Args:
            target_date: 目标日期，可以是 datetime 对象或 (day, month, year) 元组
            
        Returns:
            List[Tuple]: 气象数据列表
        """
        if isinstance(target_date, datetime):
            target_day, target_month, target_year = target_date.day, target_date.month, target_date.year
        else:
            target_day, target_month, target_year = target_date
        
        # 合并基础数据和额外数据
        all_data = self.base_data + self.additional_data
        
        # 筛选到目标日期为止的数据
        filtered_data = []
        for record in all_data:
            day, month, year = record[0], record[1], record[2]
            
            # 比较日期
            record_date = datetime(year, month, day)
            target_datetime = datetime(target_year, target_month, target_day)
            
            if record_date <= target_datetime:
                filtered_data.append(record)
        
        return sorted(filtered_data, key=lambda x: (x[2], x[1], x[0]))  # 按年月日排序
    
    def add_daily_data(self, day: int, month: int, year: int, 
                      rad: float, tmin: float, tmax: float, 
                      hum: float, wind: float, rain: float, 
                      etref: float = -99.0, wet: float = 1.0):
        """添加单日气象数据
        
        Args:
            day: 日期
            month: 月份
            year: 年份
            rad: 辐射 (kJ/m2)
            tmin: 最低温度 (°C)
            tmax: 最高温度 (°C)
            hum: 湿度 (kPa)
            wind: 风速 (m/s)
            rain: 降雨 (mm)
            etref: 参考蒸发 (mm)
            wet: 湿润度
        """
        new_record = (day, month, year, rad, tmin, tmax, hum, wind, rain, etref, wet)
        self.additional_data.append(new_record)
        
        # 保持数据按日期排序
        self.additional_data.sort(key=lambda x: (x[2], x[1], x[0]))
    
    def add_forecast_data(self, start_date: Union[datetime, Tuple[int, int, int]], 
                         forecast_days: int, weather_pattern: str = "average"):
        """添加未来几天的预测气象数据
        
        Args:
            start_date: 开始日期
            forecast_days: 预测天数
            weather_pattern: 天气模式 ("average", "dry", "wet", "hot", "cold")
        """
        if isinstance(start_date, datetime):
            current_date = start_date
        else:
            current_date = datetime(start_date[2], start_date[1], start_date[0])
        
        # 基于历史数据计算平均值作为预测基准
        base_values = self._calculate_average_values()
        
        # 根据天气模式调整预测值
        pattern_adjustments = {
            "average": {"rad": 1.0, "tmin": 0.0, "tmax": 0.0, "hum": 1.0, "wind": 1.0, "rain": 1.0},
            "dry": {"rad": 1.1, "tmin": 2.0, "tmax": 3.0, "hum": 0.7, "wind": 1.2, "rain": 0.3},
            "wet": {"rad": 0.8, "tmin": -1.0, "tmax": -2.0, "hum": 1.3, "wind": 0.9, "rain": 2.0},
            "hot": {"rad": 1.2, "tmin": 3.0, "tmax": 5.0, "hum": 0.8, "wind": 1.1, "rain": 0.7},
            "cold": {"rad": 0.9, "tmin": -3.0, "tmax": -4.0, "hum": 1.1, "wind": 1.0, "rain": 1.2}
        }
        
        adjustments = pattern_adjustments.get(weather_pattern, pattern_adjustments["average"])
        
        for i in range(forecast_days):
            forecast_date = current_date + timedelta(days=i)
            
            # 应用调整因子
            rad = base_values["rad"] * adjustments["rad"]
            tmin = base_values["tmin"] + adjustments["tmin"]
            tmax = base_values["tmax"] + adjustments["tmax"]
            hum = base_values["hum"] * adjustments["hum"]
            wind = base_values["wind"] * adjustments["wind"]
            rain = base_values["rain"] * adjustments["rain"]
            
            self.add_daily_data(
                forecast_date.day, forecast_date.month, forecast_date.year,
                rad, tmin, tmax, hum, wind, rain
            )
    
    def _calculate_average_values(self) -> dict:
        """计算历史数据的平均值"""
        if not self.base_data:
            return {
                "rad": 25000.0, "tmin": 10.0, "tmax": 25.0,
                "hum": 0.5, "wind": 2.0, "rain": 0.5
            }
        
        total_records = len(self.base_data)
        sums = {"rad": 0, "tmin": 0, "tmax": 0, "hum": 0, "wind": 0, "rain": 0}
        
        for record in self.base_data:
            sums["rad"] += record[3]
            sums["tmin"] += record[4]
            sums["tmax"] += record[5]
            sums["hum"] += record[6]
            sums["wind"] += record[7]
            sums["rain"] += record[8]
        
        return {key: value / total_records for key, value in sums.items()}
    
    def to_dataframe(self, end_date: Optional[Union[datetime, Tuple[int, int, int]]] = None) -> pd.DataFrame:
        """转换为 pandas DataFrame 格式
        
        Args:
            end_date: 结束日期，如果为 None 则包含所有数据
            
        Returns:
            pd.DataFrame: 气象数据 DataFrame
        """
        if end_date:
            data = self.get_data_up_to_date(end_date)
        else:
            data = self.base_data + self.additional_data
        
        df_data = []
        for record in data:
            df_data.append({
                'Station': self.station_name,
                'DD': record[0],
                'MM': record[1],
                'YYYY': record[2],
                'Rad': record[3],
                'TMin': record[4],
                'TMax': record[5],
                'Hum': record[6],
                'Wind': record[7],
                'Rain': record[8],
                'ETref': record[9],
                'Unnamed: 11': record[10]
            })
        
        return pd.DataFrame(df_data)
    
    def clear_additional_data(self):
        """清除所有动态添加的数据"""
        self.additional_data.clear()
    
    def get_data_count(self) -> dict:
        """获取数据统计信息"""
        return {
            "base_data_count": len(self.base_data),
            "additional_data_count": len(self.additional_data),
            "total_count": len(self.base_data) + len(self.additional_data)
        }


# 创建默认的气象数据管理器实例
default_meteo_manager = MeteorologicalDataManager()

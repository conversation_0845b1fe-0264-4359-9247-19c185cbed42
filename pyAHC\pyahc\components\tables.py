# pyright: reportInvalidTypeForm=false

from typing import Literal

import pandera as pa
from pandera.typing import Series

from pyahc.core.basemodel import BaseTableModel
from pyahc.core.valueranges import DVSRANGE, UNITRANGE, YEARRANGE

__all__ = [
    "RDTB",
    "RDCTB",
    "GCTB",
    "CFTB",
    "KYTB",
    "MRFTB",
    "WRTB",
    "CROPROTATION",
    "DTSMTB",
    "SLATB",
    "AMAXTB",
    "TMPFTB",
    "TMNFTB",
    "RFSETB",
    "FRTB",
    "FLTB",
    "FSTB",
    "FOTB",
    "RDRRTB",
    "RDRSTB",
    "DMGRZTB",
    "LSDATB",
    "LSDBTB",
    "RLWTB",
    "DMMOWTB",
    "DMMOWDELAY",
    "SHORTINTERVALMETEODATA",
]  #  TODO: 需要更新吗？

# %% ++++++++++++++++++++++++++++ CROP TABLES ++++++++++++++++++++++++++++

crop_tables = [
    "DATEHARVEST",
    "RDTB",
    "RDCTB",
    "GCTB",
    "CFTB",
    "INTERTB",
    "KYTB",
    "MRFTB",
    "WRTB",
    "CROPROTATION",
    "DTSMTB",
    "SLATB",
    "AMAXTB",
    "TMPFTB",
    "TMNFTB",
    "RFSETB",
    "FRTB",
    "FLTB",
    "FSTB",
    "FOTB",
    "RDRRTB",
    "RDRSTB",
    "DMGRZTB",
    "LSDATB",
    "LSDBTB",
    "RLWTB",
    "DMMOWTB",
    "DMMOWDELAY",
    "IRRIGEVENTS",
    "TC1TB",
    "TC2TB",
    "TC3TB",
    "TC4TB",
    "TC7TB",
    "TC8TB",
    "DC1TB",
    "DC2TB",
]

class DATEHARVEST(BaseTableModel):
    """收获日期

    属性：
        DATEHARVEST (Series[pa.DateTime]): 收获日期。
    """

    DATEHARVEST: Series[pa.DateTime]


class RDTB(BaseTableModel):
    """根深 [0..1000 cm, R]，作为发育阶段 [0..2 -, R] 的函数。

    属性：
        DVS (Series[float]): 作物发育阶段。
        RD (Series[float]): 作物根深。
    """

    DVS: Series[float] | None = pa.Field(**DVSRANGE)
    DNR: Series[float] | None = pa.Field(**YEARRANGE)
    RD: Series[float] = pa.Field(ge=0.0, le=100.0)


class RDCTB(BaseTableModel):
    """根密度列表 [0..100 cm/cm3, R]，作为相对根深 [0..1 -, R] 的函数。

    属性：
        RRD (Series[float]): 作物的相对根深。
        RDENS (Series[float]): 作物的根密度。

    """

    RRD: Series[float] = pa.Field(ge=0.0, le=100.0)
    RDENS: Series[float] = pa.Field(**UNITRANGE)


class GCTB(BaseTableModel):
    """叶面积指数 [0..12 (m2 叶)/(m2 土壤), R]，作为发育阶段 [0..2 -, R] 的函数。

    属性：
        DVS (Series[float]): 作物发育阶段。
        LAI (Series[float]): 作物的叶面积指数。
    """

    DVS: Series[float] = pa.Field(**DVSRANGE)
    LAI: Series[float] = pa.Field(ge=0.0, le=12.0)


class CFTB(BaseTableModel):
    """作物高度 [0..1.d4 cm, R]，作为发育阶段 [0..2 -, R] 的函数。

    属性：
        DVS (Series[float]): 作物发育阶段。
        CF (Series[float]): 作物系数。
    """

    DVS: Series[float] | None = pa.Field(**DVSRANGE)
    DNR: Series[float] | None = pa.Field(**YEARRANGE)
    CH: Series[float] | None
    CF: Series[float] | None


class INTERTB(BaseTableModel):
    """封闭森林冠层的截留参数 (SWINTER=2)。

    属性：
        T (Series[int]): 时间 [0..366 d, R]。
        PFREE (Series[float]): 自由穿透系数 [0..1 -, R]。
        PSTEM (Series[float]): 茎流系数 [0..1 -, R]。
        SCANOPY (Series[float]): 冠层储水能力 [0..10 cm, R]。
        AVPREC (Series[float]): 平均降雨强度 [0..100 cm/d, R]。
        AVEVAP (Series[float]): 湿冠层降雨期间的平均蒸发强度 [0..10 cm/d, R]。
    """

    T: Series[float] = pa.Field(ge=0, le=366)
    PFREE: Series[float] = pa.Field(ge=0.0, le=1.0)
    PSTEM: Series[float] = pa.Field(ge=0.0, le=1.0)
    SCANOPY: Series[float] = pa.Field(ge=0.0, le=10.0)
    AVPREC: Series[float] = pa.Field(ge=0.0, le=100.0)
    AVEVAP: Series[float] = pa.Field(ge=0.0, le=10.0)


class KYTB(BaseTableModel):
    """产量响应因子 [0..5 -, R]，作为发育阶段 [0..2 -, R] 的函数。

    属性：
        DVS (Series[float]): 作物发育阶段。
        KY (Series[float]): 作物的产量响应因子。
    """

    DVS: Series[float] = pa.Field(**DVSRANGE)
    KY: Series[float] = pa.Field(ge=0.0, le=5.0)


class MRFTB(BaseTableModel):
    """根系总呼吸/维持呼吸比 [1..5.0 -, R]。

    属性：
        DVS (Series[float]): 作物发育阶段。
        MAX_RESP_FACTOR (Series[float]): 根系总呼吸/维持呼吸比。
    """

    DVS: Series[float] = pa.Field(**DVSRANGE)
    MAX_RESP_FACTOR: Series[float] = pa.Field(ge=1.0, le=5.0)


class WRTB(BaseTableModel):
    """土壤表面根系干重 [0..10 kg/m3, R]，作为发育阶段 [0..2 -,R] 的函数。

    属性：
        DVS (Series[float]): 作物发育阶段。
        W_ROOT_SS (Series[float]): 土壤表面根系干重。
    """

    DVS: Series[float] = pa.Field(**DVSRANGE)
    W_ROOT_SS: Series[float] = pa.Field(ge=0.0, le=10.0)


class CROPROTATION(BaseTableModel):
    """作物轮作设置

    属性：
        CROPSTART (Series[pa.DateTime]): 作物开始日期。
        CROPEND (Series[pa.DateTime]): 作物结束日期。
        CROPFIL (Series[str]): 作物文件名。
        CROPTYPE (Series[int]): 作物模块类型

            * 1 - 简单
            * 2 - 详细，WOFOST 通用
            * 3 - 详细，WOFOST 草地
    """

    CROPSTART: Series[pa.DateTime]
    CROPEND: Series[pa.DateTime]
    CROPFIL: Series[str]
    CROPTYPE: Series[int] = pa.Field(ge=1, le=3)


# WOFOST 特定表格
class DTSMTB(BaseTableModel):
    """温度总和增加 [0..60 oC, R]，作为日平均温度 [0..100 oC, R] 的函数。

    属性：
        TAV (Series[float]): 日平均温度。
        DTSM (Series[float]): 温度总和增加。
    """

    TAV: Series[float] = pa.Field(ge=0.0, le=100.0)
    DTSM: Series[float] = pa.Field(ge=0.0, le=60.0)


class SLATB(BaseTableModel):
    """比叶面积 [0..1 ha/kg, R]，作为作物发育阶段 [0..2 -, R] 的函数。

    属性：
        DVS (Series[float]): 作物发育阶段。
        SLA (Series[float]): 叶面积。
    """

    DVS: Series[float] | None = pa.Field(**DVSRANGE)
    DNR: Series[float] | None = pa.Field(**YEARRANGE)
    SLA: Series[float] = pa.Field(ge=0.0, le=1.0)


class AMAXTB(BaseTableModel):
    """最大 CO2 同化率 [0..100 kg/ha/hr, R]，作为发育阶段 [0..2 -, R] 的函数。

    属性：
        DVS (Series[float]): 作物发育阶段。
        AMAX (Series[float]): 最大 CO2 同化率。
    """

    DVS: Series[float] | None = pa.Field(**DVSRANGE)
    DNR: Series[float] | None = pa.Field(**YEARRANGE)
    AMAX: Series[float] = pa.Field(ge=0.0, le=100.0)


class TMPFTB(BaseTableModel):
    """AMAX 降低因子 [-, R]，作为日平均温度 [-10..50 oC, R] 的函数。

    属性：
        TAVD (Series[float]): 最低温度。
        TMPF (Series[float]): AMAX 降低因子。
    """

    TAVD: Series[float] = pa.Field(ge=-10.0, le=50.0)
    TMPF: Series[float] = pa.Field(ge=0.0, le=1.0)


class TMNFTB(BaseTableModel):
    """AMAX 降低因子 [-, R]，作为最低日温度 [-10..50 oC, R] 的函数。

    属性：
        TMNR (Series[float]): 最低温度。
        TMNF (Series[float]): AMAX 降低因子。
    """

    TMNR: Series[float] = pa.Field(ge=-10.0, le=50.0)
    TMNF: Series[float] = pa.Field(ge=0.0, le=1.0)


class RFSETB(BaseTableModel):
    """衰老降低因子 [-, R]，作为发育阶段 [0..2 -, R] 的函数。

    属性：
        DVS (Series[float]): 作物发育阶段。
        RFSE (Series[float]): 衰老降低因子。
    """

    DVS: Series[float] | None = pa.Field(**DVSRANGE)
    DNR: Series[float] | None = pa.Field(**YEARRANGE)
    RFSE: Series[float] = pa.Field(ge=0.0, le=1.0)


class FRTB(BaseTableModel):
    """总干物质增加量分配给根系的比例 [kg/kg, R]。

    属性：
        DVS (Series[float]): 作物发育阶段。
        FR (Series[float]): 总干物质增加量分配给根系的比例。
    """

    DVS: Series[float] | None = pa.Field(**DVSRANGE)
    DNR: Series[float] | None = pa.Field(**YEARRANGE)
    FR: Series[float] = pa.Field(ge=0.0, le=1.0)


class FLTB(BaseTableModel):
    """地上总干物质增加量分配给叶片的比例 [kg/kg, R]。

    属性：
        DVS (Series[float]): 作物发育阶段。
        FL (Series[float]): 地上总干物质增加量分配给叶片的比例。
    """

    DVS: Series[float] | None = pa.Field(**DVSRANGE)
    DNR: Series[float] | None = pa.Field(**YEARRANGE)
    FL: Series[float] = pa.Field(ge=0.0, le=1.0)


class FSTB(BaseTableModel):
    """地上总干物质增加量分配给茎的比例 [kg/kg, R]。

    属性：
        DVS (Series[float]): 作物发育阶段。
        FS (Series[float]): 地上总干物质增加量分配给茎的比例。
    """

    DVS: Series[float] | None = pa.Field(**DVSRANGE)
    DNR: Series[float] | None = pa.Field(**YEARRANGE)
    FS: Series[float] = pa.Field(ge=0.0, le=1.0)


class FOTB(BaseTableModel):
    """地上总干物质增加量分配给储存器官的比例 [kg/kg, R]。

    属性：
        DVS (Series[float]): 作物发育阶段。
        FO (Series[float]): 地上总干物质增加量分配给储存器官的比例。
    """

    DVS: Series[float] = pa.Field(**DVSRANGE)
    FO: Series[float] = pa.Field(ge=0.0, le=1.0)


class RDRRTB(BaseTableModel):
    """根系相对死亡率 [kg/kg/d]，作为发育阶段 [0..2 -, R] 的函数。

    属性：
        DVS (Series[float]): 作物发育阶段。
        RDRR (Series[float]): 根系相对死亡率。
    """

    DVS: Series[float] | None = pa.Field(**DVSRANGE)
    DNR: Series[float] | None = pa.Field(**YEARRANGE)
    RDRR: Series[float] = pa.Field(ge=0.0)


class RDRSTB(BaseTableModel):
    """茎相对死亡率 [kg/kg/d]，作为发育阶段 [0..2 -, R] 的函数。

    属性：
        DVS (Series[float]): 作物发育阶段。
        RDRS (Series[float]): 茎相对死亡率。
    """

    DVS: Series[float] | None = pa.Field(**DVSRANGE)
    DNR: Series[float] | None = pa.Field(**YEARRANGE)
    RDRS: Series[float] = pa.Field(ge=0.0)


class DMGRZTB(BaseTableModel):
    """地上干物质阈值 [0..1d6 kg DM/ha, R]，作为天数 [1..366 d, R] 的函数，用于触发放牧。

    属性：
        DNR (Series[float]): 天数。
        DMGRZ (Series[float]): 根系干物质生长率。
    """

    DNR: Series[float] = pa.Field(**YEARRANGE)
    DMGRZ: Series[float] = pa.Field(ge=0.0, le=1.0e6)


class LSDATB(BaseTableModel):
    """每个放牧期的实际牲畜密度

    !!! 注意

        总周期数应等于 SEQGRAZMOW 中的周期数

    属性：
        SEQNR (Series[int]): 割草/放牧序列期的编号 [0..366 d, I]
        LSDA (Series[float]): 放牧期的实际牲畜密度 [0.0..1000.0 LS/ha, R]
    """

    SEQNR: Series[int] = pa.Field(**YEARRANGE)
    LSDA: Series[float] = pa.Field(ge=0.0, le=1000.0)


class LSDBTB(BaseTableModel):
    """牲畜密度、放牧天数和干物质吸收之间的关系

    属性：
        LSDB (Series[float]): 基本牲畜密度 [0.0..1000.0 LS/ha, R]
        DAYSGRAZING (Series[float]): 最大放牧天数 [0.0..366.0 d, R]
        UPTGRAZING (Series[float]): 放牧干物质吸收量 [0.0..1000.0 kg/ha, R] (kg/ha DM)
        LOSSGRAZING (Series[float]): 放牧期间因粪便和踩踏造成的干物质损失 [0.0..1000.0 kg/ha, R] (kg/ha DM)
    """

    LSDb: Series[float] = pa.Field(ge=0.0, le=1000.0)
    DAYSGRAZING: Series[float] = pa.Field(**YEARRANGE)
    UPTGRAZING: Series[float] = pa.Field(ge=0.0, le=1000.0)
    LOSSGRAZING: Series[float] = pa.Field(ge=0.0, le=1000.0)


class RLWTB(BaseTableModel):
    """根深 RL [0..5000 cm, R]，作为根重 RW [0..5000 kg DM/ha, R] 的函数。

    属性：
        RW (Series[float]): 根深
        RL (Series[float]): 根重
    """

    RW: Series[float] = pa.Field(ge=0.0, le=5000.0)
    RL: Series[float] = pa.Field(ge=0.0, le=5000.0)


class DMMOWTB(BaseTableModel):
    """地上干物质阈值列表 [0..1d6 kg DM/ha, R]，作为天数 [1..366 d, R] 的函数，用于触发割草。

    !!! 注意

        最多 20 条记录


    属性：
        DNR (Series[float]): 天数。
        DMMOW (Series[float]): 地上干物质阈值 [0..1d6 kg DM/ha, R]
    """

    DNR: Series[float] = pa.Field(**YEARRANGE)
    DMMOW: Series[float] = pa.Field(ge=0.0, le=1.0e6)


class DMMOWDELAY(BaseTableModel):
    """割草后干物质收获量 [0..1d6 kg/ha, R] 与再生延迟天数 [0..366 d, I] 之间的关系。

    属性：
        DMMOWDELAY (Series[float]): 干物质收获量 [0..1d6 kg/ha, R]
        DAYDELAY (Series[int]): 再生延迟天数 [0..366 d, I]
    """

    DMMOWDELAY: Series[float] = pa.Field(ge=0.0, le=1.0e6)
    DAYDELAY: Series[int] = pa.Field(**YEARRANGE)


class IRRIGEVENTS(BaseTableModel):
    """每个固定灌溉事件的信息。

    属性：
        IRDATE (Series[datetime]): 灌溉日期。
        IRDEPTH (Series[float]): 水量 [0..1000 mm, R]。
        IRCONC (Series[float]): 灌溉水浓度 [0..1000 mg/cm3, R]。
        IRTYPE (Series[int]): 灌溉类型

            * 0 - 喷灌
            * 1 - 地表灌溉

    """

    IRDATE: Series[pa.DateTime]
    IRDEPTH: Series[float] | None = pa.Field(default=None, ge=0.0, le=1000.0)
    IRCONC: Series[float] = pa.Field(ge=0.0, le=1000.0)
    IRTYPE: Series[int] = pa.Field(ge=0, le=1)


class TC1TB(BaseTableModel):
    """tc1tb 选项表"""

    DVS_TC1: Series[float] = pa.Field(ge=0.0, le=2.0)
    TREL: Series[float] = pa.Field(ge=0.0, le=1.0)


class TC2TB(BaseTableModel):
    """tc2tb 选项表"""

    DVS_TC2: Series[float] = pa.Field(ge=0.0, le=2.0)
    RAW: Series[float] = pa.Field(ge=0.0, le=1.0)


class TC3TB(BaseTableModel):
    """tc3tb 选项表"""

    DVS_TC3: Series[float] = pa.Field(ge=0.0, le=2.0)
    TAW: Series[float] = pa.Field(ge=0.0, le=1.0)


class TC4TB(BaseTableModel):
    """tc4tb 选项表"""

    DVS_TC4: Series[float] = pa.Field(ge=0.0, le=2.0)
    DWA: Series[float] = pa.Field(ge=0.0, le=500.0)


class TC7TB(BaseTableModel):
    """tc7tb 选项表"""

    DVS_TC7: Series[float] = pa.Field(ge=0.0, le=2.0)
    HCRI: Series[float] = pa.Field(ge=-1000.0, le=-100.0)


class TC8TB(BaseTableModel):
    """tc8tb 选项表"""

    DVS_TC8: Series[float] = pa.Field(ge=0.0, le=2.0)
    TCRI: Series[float] = pa.Field(ge=0.0, le=1.0)


class DC1TB(BaseTableModel):
    DVS_DC1: Series[float]
    DI: Series[float]


class DC2TB(BaseTableModel):
    DVS_DC2: Series[float]
    FID: Series[float]


# %% ++++++++++++++++++++++++++++ METEO TABLES ++++++++++++++++++++++++++++

meteo_tables = [
    "DAILYMETEODATA",
    "SHORTINTERVALMETEODATA",
    "DETAILEDRAINFALL",
    "RAINFLUX",
]


class DAILYMETEODATA(BaseTableModel):
    """格式化详细的每日气象数据。

    验证站点是否在单引号中。
    检查 dd、mm、yyyy 列是否已在数据框中。如果不在，则需要 datetime 索引并
        将 datetime 索引解析为单独的列。
    格式化变量中的小数。
    """

    Station: Series[str]
    DD: Series[str]
    MM: Series[str]
    YYYY: Series[str]
    RAD: Series[float]
    Tmin: Series[float]
    Tmax: Series[float]
    HUM: Series[float]
    WIND: Series[float]
    RAIN: Series[float]
    ETref: Series[float]
    WET: Series[float]


class SHORTINTERVALMETEODATA(BaseTableModel):
    Date: Series[pa.DateTime]
    Record: Series[int] = pa.Field(ge=1, le=10)
    Rad: Series[float]
    Temp: Series[float]
    Hum: Series[float]
    Wind: Series[float]
    Rain: Series[float]


class DETAILEDRAINFALL(BaseTableModel):
    Station: Series[str]
    Day: Series[int]
    Month: Series[int]
    Year: Series[int]
    Time: Series[float]
    Amount: Series[float]


class RAINFLUX(BaseTableModel):
    TIME: Series[float] = pa.Field(**YEARRANGE)
    RAINFLUX: Series[float] = pa.Field(ge=0, le=1000.0)


# %% ++++++++++++++++++++++++++++ SOILWATER TABLES ++++++++++++++++++++++++++++

soilwater_tables = [
    "INIPRESSUREHEAD",
    "MXPONDTB",
    "SOILPROFILE",
    "SOILHYDRFUNC",
    "SOILTEXTURES",
    "INITSOILTEMP",
]


class INIPRESSUREHEAD(BaseTableModel):
    """初始压头 [cm, R]，作为土壤层 [1..N, I] 的函数。

    属性：
        ZI: Series[int]: 土壤深度 [-1.d5..0 cm, R]。
        H: Series[float]: 初始土壤水压头 [-1.d10..1.d4 cm, R]。
    """

    ZI: Series[float] = pa.Field(ge=-1.0e5, le=0.0)
    H: Series[float] = pa.Field(ge=-1.0e10, le=1.0e4)


class MXPONDTB(BaseTableModel):
    """径流最小厚度 PONDMXTB [0..1000 cm, R]，作为时间的函数。

    属性：
        DATEPMX: Series[pa.DateTime]: 径流积水阈值的日期。
        PONDMXTB: Series[float]: 径流最小厚度。
    """

    DATEPMX: Series[pa.DateTime]
    PONDMXTB: Series[float]


class SOILPROFILE(BaseTableModel):
    """土壤剖面的垂直离散化

    属性：
        ISUBLAY: Series[int]: 子层编号，从土壤表面开始为 1 [1..MACP, I]。
        ISOILLAY: Series[int]: 土壤物理层编号，从土壤表面开始为 1 [1..MAHO, I]。
        HSUBLAY: Series[float]: 子层高度 [0..1.d4 cm, R]。
        HCOMP: Series[float]: 子层中隔室的高度 [0.0..1000.0 cm, R]。
        NCOMP: Series[int]: 子层中隔室的数量 (注意 NCOMP = HSUBLAY/HCOMP) [1..MACP, I]。
    """

    ISOILLAY: Series[int] = pa.Field(ge=1)
    ISUBLAY: Series[int] = pa.Field(ge=1)
    HSUBLAY: Series[float] = pa.Field(ge=0.0, le=1.0e4)
    HCOMP: Series[float] = pa.Field(ge=0.0, le=1.0e3)
    NCOMP: Series[int] = pa.Field(ge=1)


class SOILHYDRFUNC(BaseTableModel):
    """土壤水力函数表。

        !!! 警告
            ALFAW 仅在滞后选项设置为 1 或 2 时才需要。此列设置为可选列，并且（目前）未进行检查。
    
    属性：
        ORES (Series[float]): 残余含水量 [0..1 cm3/cm3, R]
        OSAT (Series[float]): 饱和含水量 [0..1 cm3/cm3, R]
        ALFA (Series[float]): 主干燥曲线的参数 alfa [0.0001..100 /cm, R]
        NPAR (Series[float]): 参数 n [1.001..9 -, R]
        LEXP (Series[float]): 水力传导函数中的指数 [-25..25 -, R]
        KSATFIT (Series[float]): 水力传导函数 Ksat 的拟合参数 [1.d-5..1d5 cm/d, R]
        H_ENPR (Series[float]): 进气压头 [-40.0..0.0 cm, R]
        KSATEXM (Series[float]): 饱和条件下的测量水力传导率 [1.d-5..1d5 cm/d, R]
        BDENS (Series[float]): 干土壤容重 [100..1d4 mg/cm3, R]
        ALFAW (Optional[Series[float]]): 滞后情况下主湿润曲线的 Alfa 参数 [0.0001..100 /cm, R]
    """

    ORES: Series[float] = pa.Field(ge=0.0, le=1.0)
    OSAT: Series[float] = pa.Field(ge=0.0, le=1.0)
    ALFA: Series[float] = pa.Field(ge=0.0001, le=100.0)
    NPAR: Series[float] = pa.Field(ge=1.001, le=9.0)
    LEXP: Series[float] = pa.Field(ge=-25.0, le=25.0)
    KSATFIT: Series[float] = pa.Field(ge=1.0e-5, le=1.0e5)
    H_ENPR: Series[float] = pa.Field(ge=-40.0, le=0.0)
    KSATEXM: Series[float] = pa.Field(ge=1.0e-5, le=1.0e5)
    BDENS: Series[float] = pa.Field(ge=100.0, le=1.0e4)
    ALFAW: Series[float] | None = pa.Field(ge=0.0001, le=100.0)


# %% ++++++++++++++++++++++++++++ HEAT FLOW TABLES ++++++++++++++++++++++++++++


class SOILTEXTURES(BaseTableModel):
    """土壤质地表。

    属性：
        PSAND (float): 土壤层深度 [cm, R]
        PSILT (float): 砂含量 [g/g 矿物部分, R]
        PCLAY (float): 粘土含量 [g/g 矿物部分, R]
        ORGMAT (float): 有机质含量 [g/g 干土, R]
    """

    PSAND: float
    PSILT: float
    PCLAY: float
    ORGMAT: float


class INITSOILTEMP(BaseTableModel):
    """初始土壤温度表。

    属性：
        ZH (float): 土壤层深度 [cm, R]
        TSOIL (float): 初始温度 [oC, R]
    """

    ZH: float = pa.Field(ge=-100000, le=0)
    TSOIL: float = pa.Field(ge=-50, le=50)


# %% ++++++++++++++++++++++++++++ BOUNDARY TABLES ++++++++++++++++++++++++++++

boundary_tables = [
    "GWLEVEL",
    "QBOT2",
    "HAQUIF",
    "QBOT4",
    "QTAB",
    "HBOT5",
    "DATET",
    "CSEEPARR",
    "INISSOIL",
    "MISC"
]

class GWLEVEL(BaseTableModel):
    """地下水位表。

    属性：
        DATE (Series[pa.DateTime]): 地下水位日期。
        GWLEVEL (Series[float]): 地下水位。
    """

    DATE1: Series[pa.DateTime]
    GWLEVEL: Series[float]


class QBOT2(BaseTableModel):
    """底部边界流量表。

    属性：
        DATE2 (Series[pa.DateTime]): 底部边界流量日期。
        QBOT2 (Series[float]): 底部边界流量。
    """

    DATE2: Series[pa.DateTime]
    QBOT2: Series[float]


class HAQUIF(BaseTableModel):
    """含水层厚度表。

    属性：
        DATE3 (Series[pa.DateTime]): 含水层厚度日期。
        HAQUIF (Series[float]): 含水层厚度。
    """

    DATE3: Series[pa.DateTime]
    HAQUIF: Series[float]


class QBOT4(BaseTableModel):
    """底部边界流量表。

    属性：
        DATE4 (Series[pa.DateTime]): 底部边界流量日期。
        QBOT4 (Series[float]): 底部边界流量。
    """

    DATE4: Series[pa.DateTime]
    QBOT4: Series[float]


class QTAB(BaseTableModel):
    """地下水位高度表。

    属性：
        HTAB (Series[pa.DateTime]): 地下水位高度日期。
        HTABLE (Series[float]): 地下水位高度。
    """

    HTAB: Series[float]
    QTAB: Series[float]


class HBOT5(BaseTableModel):
    """底部隔室压头表。

    属性：
        DATE6 (Series[pa.DateTime]): 底部隔室压头日期。
        HBOT5 (Series[float]): 底部隔室压头。
    """

    DATE5: Series[pa.DateTime]
    HBOT5: Series[float]


class DATET(BaseTableModel):
    """时间表。

    属性：
        DATE7 (Series[pa.DateTime]): 时间日期。
        TIME (Series[float]): 时间。
    """

    DATET: Series[pa.DateTime]
    TBOT: Series[float]


class CSEEPARR(BaseTableModel):
    """渗流表。

    属性：
        DATE8 (Series[pa.DateTime]): 渗流日期。
        CSEEPARR (Series[float]): 渗流。
    """

    DATEC: Series[pa.DateTime]
    CSEEPARR: Series[float]


class INISSOIL(BaseTableModel):
    """毛细上升表。

    属性：
        DATE9 (Series[pa.DateTime]): 毛细上升日期。
        CML (Series[float]): 毛细上升。
    """

    ZC: Series[float]
    CML: Series[float]


class MISC(BaseTableModel):
    """杂项表。

    属性：
        DATE10 (Series[pa.DateTime]): 杂项日期。
        MISC (Series[float]): 杂项。
    """

    LDIS: Series[float]
    KF: Series[float]
    DECPOT: Series[float]
    FDEPTH: Series[float]


# %% ++++++++++++++++++++++++++++ DRAINAGE TABLES ++++++++++++++++++++++++++++

drainage_tables = [
    "DRNTB",
    "DRAINAGELEVELTOPPARAMS",
    "DATOWLTB1",
    "DATOWLTB2",
    "DATOWLTB3",
    "DATOWLTB4",
    "DATOWLTB5",
    "SECWATLVL",
    "MANSECWATLVL",
    "QWEIR",
    "QWEIRTB",
    "PRIWATLVL",
    "QDRNTB"
]

class DRNTB(BaseTableModel):
    """排水特性表。

    属性：
        LEV (Series[int]): 排水层 [1..5, I]。
        SWDTYP (Series[int]): 排水介质类型：1 = 排水管，2 = 明渠。
        L (Series[float]): 排水间距 [1..100000 m, R]。
        ZBOTDRE (Series[float]): 排水介质底部水位 [-10000..0 cm, R]。
        GWLINF (Series[float]): 地下水位影响 [-10000..200 cm, R]。
        RDRAIN (Series[float]): 排水阻力 [10..1d5 d, R]。
        RINFI (Series[float]): 渗透阻力 [0..1d5 d, R]。
        RENTRY (Series[float]): 入口阻力 [0..1000 d, R]。
        REXIT (Series[float]): 出口阻力 [0..1000 d, R]。
        WIDTHR (Series[float]): 排水介质宽度 [0..1000 cm, R]。
        TALUDR (Series[float]): 排水介质坡度 [0..1000 cm, R]。
    """

    LEV: Series[int] = pa.Field(ge=1, le=5)
    SWDTYP: Series[Literal[0, 1]]
    L: Series[float] = pa.Field(ge=1.0, le=100000.0)
    ZBOTDRE: Series[float]
    GWLINF: Series[float] = pa.Field(ge=-1000.0, le=0.0)
    RDRAIN: Series[float] = pa.Field(ge=1.0, le=100000.0)
    RINFI: Series[float] = pa.Field(ge=1.0, le=100000.0)
    RENTRY: Series[float] = pa.Field(ge=0.0, le=100.0)
    REXIT: Series[float] = pa.Field(ge=0.0, le=100.0)
    WIDTHR: Series[float] = pa.Field(ge=0.0, le=10000.0)
    TALUDR: Series[float] = pa.Field(ge=0.01, le=5.0)


class DRAINAGELEVELTOPPARAMS(BaseTableModel):
    """排水层顶部参数表。

    我找不到此表的描述。它出现在 Hupselbrook 示例的 swa.dra 文件中，但不在模板中。

    属性：
        SWTOPDISLAY (Series[Literal[0, 1]]): 每个排水层开关，用于在给定模型排水层顶部位置的情况下垂直分布排水通量 [Y=1, N=0]
        ZTOPDISLAY (Series[float]): 包含每个排水层模型排水层顶部深度的数组 [-10000.0..0.0, cm, R]
        FTOPDISLAY (Series[float]): 包含每个排水层模型排水层顶部因子的数组 [0.0..1.0, -, R]
    """

    SWTOPDISLAY: Series[Literal[0, 1]]
    ZTOPDISLAY: Series[float] = pa.Field(ge=-10000.0, le=0.0)
    FTOPDISLAY: Series[float] = pa.Field(ge=0.0, le=1.0)


class DATOWLTB1(BaseTableModel):
    """排水水位表。

    属性：
        DATOWL1 (Series[pa.DateTime]): 排水水位日期。
        WLEVEL (Series[float]): 排水水位。
    """

    DATOWL1: Series[pa.DateTime]
    LEVEL1: Series[float]


class DATOWLTB2(BaseTableModel):
    """排水水位表。

    属性：
        DATOWL2 (Series[pa.DateTime]): 排水水位日期。
        LEVEL2 (Series[float]): 排水水位。
    """

    DATOWL2: Series[pa.DateTime]
    LEVEL2: Series[float]


class DATOWLTB3(BaseTableModel):
    """排水水位表。

    属性：
        DATOWL3 (Series[pa.DateTime]): 排水水位日期。
        LEVEL3 (Series[float]): 排水水位。
    """

    DATOWL3: Series[pa.DateTime]
    LEVEL3: Series[float]


class DATOWLTB4(BaseTableModel):
    """排水水位表。

    属性：
        DATOWL4 (Series[pa.DateTime]): 排水水位日期。
        LEVEL4 (Series[float]): 排水水位。
    """

    DATOWL4: Series[pa.DateTime]
    LEVEL4: Series[float]


class DATOWLTB5(BaseTableModel):
    """排水水位表。

    属性：
        DATOWL5 (Series[pa.DateTime]): 排水水位日期。
        LEVEL5 (Series[float]): 排水水位。
    """

    DATOWL5: Series[pa.DateTime]
    LEVEL5: Series[float]


class SECWATLVL(BaseTableModel):
    DATE2: Series[pa.DateTime]
    WLS: Series[float]


class MANSECWATLVL(BaseTableModel):
    IMPER_4B: Series[float]
    IMPEND: Series[pa.DateTime]
    SWMAN: Series[float]
    WSCAP: Series[float]
    WLDIP: Series[float]
    INTWL: Series[float]


class QWEIR(BaseTableModel):
    IMPER_4C: Series[float]
    HBWEIR: Series[float]
    ALPHAW: Series[float]
    BETAW: Series[float]


class QWEIRTB(BaseTableModel):
    IMPER_4D: Series[float]
    IMPTAB: Series[float]
    HTAB: Series[float]
    QTAB: Series[float]


class PRIWATLVL(BaseTableModel):
    DATE1: Series[pa.DateTime]
    WLP: Series[float]


class QDRNTB(BaseTableModel):
    QDRAIN: Series[float]
    GWL: Series[float]


# %% ++++++++++++++++++++++++++++ GENERAL SETTINGS TABLES ++++++++++++++++++++++++++++

general_settings_tables = [
    "OUTDATIN",
    "OUTDAT"
]

class OUTDATIN(BaseTableModel):
    """OUTDATIN 表

    属性：
        OUTDAT: Series[str]: 输出文件名。
    """

    OUTDATIN: Series[pa.DateTime]


class OUTDAT(BaseTableModel):
    """OUTDAT 表

    属性：
        OUTDAT: Series[str]: 输出文件名。
    """

    OUTDAT: Series[pa.DateTime]

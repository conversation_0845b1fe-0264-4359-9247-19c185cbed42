#!/usr/bin/env python3
"""数据包 - 包含 hetao_corn_2013 测试案例的数据文件

这个包包含了从 hetao_corn_2013.py 中提取的数据数组：
- gwlevel_data: 地下水位数据
- hbot5_data: 压力水头数据
- gwc_data: 地下水浓度数据
- thetai_data: 土壤初始含水量数据

这些数据原本硬编码在主测试文件中，现在被分离到独立的模块中以提高代码组织性。
"""

from .gwlevel_data import GWLEVEL_DATA
from .hbot5_data import HBOT5_DATA
from .gwc_data import GWC_DATA
from .thetai_data import STANDARD_THETAI, THETAI_STATS
from .meteorological_data import METEOROLOGICAL_DATA, MeteorologicalDataManager, default_meteo_manager

__all__ = [
    'GWLEVEL_DATA',
    'HBOT5_DATA',
    'GWC_DATA',
    'STANDARD_THETAI',
    'THETAI_STATS',
    'METEOROLOGICAL_DATA',
    'MeteorologicalDataManager',
    'default_meteo_manager'
]

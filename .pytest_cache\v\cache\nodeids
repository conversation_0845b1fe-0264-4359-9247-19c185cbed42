["tests/test_state_injection_integration.py::TestStateInjectionIntegration::test_end_to_end_state_injection", "tests/test_state_injection_integration.py::TestStateInjectionIntegration::test_partial_state_injection", "tests/test_state_injection_integration.py::TestStateInjectionIntegration::test_state_injection_memory_usage", "tests/test_state_injection_integration.py::TestStateInjectionIntegration::test_state_injection_performance", "tests/test_state_injection_integration.py::TestStateInjectionIntegration::test_state_injection_preserves_original_model", "tests/test_state_injection_integration.py::TestStateInjectionIntegration::test_state_injection_validation_accuracy", "tests/test_state_injection_integration.py::TestStateInjectionIntegration::test_state_injection_with_different_layer_counts", "tests/test_state_injection_integration.py::TestStateInjectionIntegration::test_state_injection_with_invalid_data", "tests/test_state_injection_integration.py::TestStateInjectionIntegration::test_state_injection_with_missing_components", "tests/test_state_injector.py::TestStateInjector::test_adjust_array_size", "tests/test_state_injector.py::TestStateInjector::test_estimate_development_stage_from_lai", "tests/test_state_injector.py::TestStateInjector::test_inject_all_states_success", "tests/test_state_injector.py::TestStateInjector::test_inject_all_states_with_exception", "tests/test_state_injector.py::TestStateInjector::test_inject_biomass", "tests/test_state_injector.py::TestStateInjector::test_inject_crop_state_no_component", "tests/test_state_injector.py::TestStateInjector::test_inject_crop_state_success", "tests/test_state_injector.py::TestStateInjector::test_inject_groundwater_level_no_component", "tests/test_state_injector.py::TestStateInjector::test_inject_groundwater_level_success", "tests/test_state_injector.py::TestStateInjector::test_inject_lai", "tests/test_state_injector.py::TestStateInjector::test_inject_root_depth", "tests/test_state_injector.py::TestStateInjector::test_inject_soil_moisture_layer_mismatch", "tests/test_state_injector.py::TestStateInjector::test_inject_soil_moisture_no_component", "tests/test_state_injector.py::TestStateInjector::test_inject_soil_moisture_success", "tests/test_state_injector.py::TestStateInjector::test_validate_injection_result", "tests/test_state_injector.py::TestStateInjector::test_validate_input_states", "tests/test_state_injector.py::TestStateInjector::test_validate_state_value_crop_states", "tests/test_state_injector.py::TestStateInjector::test_validate_state_value_soil_moisture", "tests/test_state_injector.py::TestStateInjector::test_verify_crop_state_injection", "tests/test_state_injector.py::TestStateInjector::test_verify_groundwater_injection", "tests/test_state_injector.py::TestStateInjector::test_verify_soil_moisture_injection"]
# ruff: noqa: S603
# mypy: disable-error-code="override, func-returns-value, call-overload, operator, no-any-return"
# 覆盖错误与 validate 方法有关，可能与 Pydantic 有关。
# func-returns-value 在模型构建器上引发，因为它不返回任何内容。这不是一个优先修复的问题。
# 运算符在其中一个检查（swdra 比较）中返回，这是可以的。它在该点之前已经过验证。
# no-any-return 在 run_parallel 函数上引发，因为它返回一个结果列表。这不是一个优先修复的问题。
# 保护子进程调用似乎不是本项目中的优先事项
"""构建、运行和解析 AHC 模型运行结果。

当 Model 类开始增长时，很明显它需要重构为更模块化的结构。
构建环境、运行和解析结果的功能已被抽象为 3 个类，
将主要（并暴露给用户）Model 类专注于模型组件及其交互。
此模块中的四个类是：

类：

    ModelBuilder: 负责构建模型组件的类。
    ModelRunner: 负责运行模型的类。
    ResultReader: 负责解析模型结果的类。
    Model: 运行 AHC 模型的主类。
"""

from __future__ import annotations

import logging as _logging
import os as _os
import shutil as _shutil
import subprocess as _subprocess
import tempfile as _tempfile
import time as _time
from multiprocessing import Pool as _Pool
from pathlib import Path as _Path
from typing import Literal as _Literal

from pandas import DataFrame as _DataFrame, read_csv as _read_csv, to_datetime as _to_datetime
from pydantic import Field as _Field, PrivateAttr as _PrivateAttr, model_validator as _model_validator

from pyahc.components.boundary import BottomBoundary
from pyahc.components.crop import Crop
from pyahc.components.ctr import CtrFile
from pyahc.components.drainage import Drainage
from pyahc.components.irrigation import FixedIrrigation
from pyahc.components.metadata import Metadata
from pyahc.components.meteorology import Meteorology
from pyahc.components.simsettings import GeneralSettings, RichardsSettings
from pyahc.components.soilwater import (
    Evaporation,
    SnowAndFrost,
    SoilMoisture,
    SoilProfile,
    SurfaceFlow,
)
from pyahc.components.transport import HeatFlow, SoluteTransport
from pyahc.core.basemodel import PyAHCBaseModel
from pyahc.core.defaults import IS_WINDOWS
from pyahc.core.fields import Subsection
from pyahc.core.io.io_ascii import open_ascii
from pyahc.libs import ahc_linux, ahc_windows
from pyahc.model.result import Result
from pyahc.utils.mixins import FileMixin, SerializableMixin

logger = _logging.getLogger(__name__)

__all__ = ["Model", "run_parallel"]


class ModelBuilder:
    """构建模型组件。

    属性：
        model (Model): 要构建的模型。
        tempdir (str): 存储输入文件的临时目录。

    方法：
        copy_executable: 将适当的 AHC 可执行文件复制到临时目录。
        write_inputs: 将输入文件写入临时目录。
    """

    def __init__(self, model: Model, tempdir: str):
        self.model = model
        self.tempdir = tempdir

    def copy_executable(self) -> None:
        """将适当的 AHC 可执行文件复制到临时目录。"""
        if IS_WINDOWS:
            _shutil.copy(ahc_windows, self.tempdir)
            logger.info(
                "正在将 AHC 的 Windows 版本复制到临时目录..."
            )
        else:
            _shutil.copy(ahc_linux, self.tempdir)
            logger.info("正在将 Linux 可执行文件复制到临时目录...")

        return self

    def get_inputs(self) -> dict:
        """获取字典中的输入文件。"""
        inputs = {}

        inputs["swp"] = self.model.swp
        if self.model.lateraldrainage.swdra in [1, 2]:
            inputs["dra"] = self.model.lateraldrainage.drafile.dra
        if self.model.crop.cropfiles:
            inputs["crop"] = self.model.crop.cropfiles
        if self.model.meteorology.metfile:
            inputs["met"] = self.model.meteorology.met
        if self.model.fixedirrigation.swirgfil == 1:
            inputs["irg"] = self.model.fixedirrigation.irg
        if self.model.bottomboundary.swbbcfile == 1:
            inputs["bbc"] = self.model.bottomboundary.bbc

        return inputs

    def write_inputs(self) -> None:
        """将输入文件写入临时目录。"""
        logger.info("正在准备文件...")

        # 生成 AHC.PAT 文件
        self.write_ahc_pat()
        
        # 生成 AHC.CTR 文件
        if self.model.ctr_file:
            self.model.write_ctr(self.tempdir)

        # 生成 hetao.SWP 文件
        self.model.write_swp(self.tempdir)

        if self.model.lateraldrainage.swdra in [1, 2]:
            self.model.lateraldrainage.write_dra(self.tempdir)
        if self.model.crop.cropfiles:
            self.model.crop.write_crop(self.tempdir, self.model.ctr_file)
        if self.model.meteorology.metfile:
            self.model.meteorology.write_met(self.tempdir, self.model.ctr_file)
        if self.model.fixedirrigation.swirgfil == 1:
            self.model.fixedirrigation.write_irg(self.tempdir)
        if self.model.bottomboundary.swbbcfile == 1:
            self.model.bottomboundary.write_bbc(self.tempdir)
        # 生成 HeatFlow 文件 (hetao.HEA)
        if self.model.heatflow and self.model.heatflow.swhea == 1:
            project_name = getattr(self.model.ctr_file, 'project', 'hetao') if self.model.ctr_file else 'hetao'
            hea_file_path = _Path(self.tempdir) / f"{project_name}.HEA"
            with open(hea_file_path, 'w', encoding='utf-8') as f:
                f.write(self.model.heatflow.model_string())
            logger.info(f"已生成 HeatFlow 文件: {hea_file_path}")
            
        # 生成 SoluteTransport 文件 (hetao.SLT)
        if self.model.solutetransport and self.model.solutetransport.swsolu == 1:
            project_name = getattr(self.model.ctr_file, 'project', 'hetao') if self.model.ctr_file else 'hetao'
            slt_file_path = _Path(self.tempdir) / f"{project_name}.SLT"
            with open(slt_file_path, 'w', encoding='utf-8') as f:
                f.write(self.model.solutetransport.model_string())
            logger.info(f"已生成 SoluteTransport 文件: {slt_file_path}")
            
        # 生成 SoilProfile 文件 (hetao.SOL)
        if self.model.soilprofile:
            project_name = getattr(self.model.ctr_file, 'project', 'hetao') if self.model.ctr_file else 'hetao'
            sol_file_path = _Path(self.tempdir) / f"{project_name}.SOL"
            with open(sol_file_path, 'w', encoding='utf-8') as f:
                f.write(self.model.soilprofile.model_string())
            logger.info(f"已生成 SoilProfile 文件: {sol_file_path}")

        return self
    
    def write_ahc_pat(self) -> None:
        """生成 AHC.PAT 文件到临时目录。"""
        # 将 Windows 路径分隔符转换为正确格式
        temp_path = self.tempdir.replace('\\', '\\\\')
        if not temp_path.endswith('\\\\'):
            temp_path += '\\\\'
            
        pat_content = f"  PATH = '{temp_path}'\n  CTRNAM ='AHC'                                   ! the name for .key file, [A8]"
        
        pat_file_path = _Path(self.tempdir) / "AHC.PAT"
        with open(pat_file_path, 'w', encoding='utf-8') as f:
            f.write(pat_content)
        
        logger.info(f"已生成 AHC.PAT 文件: {pat_file_path}")


class ModelRunner:
    """负责运行模型的类。

    在 run 方法中，ResultReader 用于抽象模型结果的解析。

    属性：
        model (Model): 要运行的模型。

    方法：
        run_ahc: 运行 AHC 可执行文件。
        raise_ahc_warning: 触发警告。
        run: 运行模型的主函数
    """

    def __init__(self, model: Model):
        self.model = model

    @staticmethod
    def run_ahc(tempdir: _Path) -> str:
        """Run the AHC executable.

        在临时目录中运行可执行文件，并在可执行文件请求输入（终止时）时将换行符传递给 stdin。

        参数：
            tempdir (_Path): 可执行文件存储的临时目录。
        """
        ahc_path = _Path(tempdir, "ahc.exe") if IS_WINDOWS else _Path(tempdir, "ahc420")
        if not _Path(tempdir, "AHC.CTR").exists():
            raise FileNotFoundError(f"AHC.CTR not found in {tempdir}")
        p = _subprocess.Popen(
            [str(ahc_path), "AHC.CTR"],
            stdout=_subprocess.PIPE,
            stdin=_subprocess.PIPE,
            stderr=_subprocess.STDOUT,
            cwd=tempdir,
        )
        stdout = p.communicate(input=b"\n")[0]

        return stdout.decode()

    def raise_ahc_warning(self, warnings: list):
        """记录模型运行中的警告。

        参数：
            warnings (list): 通过 ResultReader 解析的模型运行警告。
        """
        for message in warnings:
            logger.warning(message)

    def run(self, path: str | _Path, silence_warnings: bool = False) -> Result:
        """运行模型的主函数。

        首先使用 ModelBuilder 准备模型运行环境。
        其次，运行 AHC 可执行文件，并使用 ResultReader 解析从可执行文件传递的解码结果，
        然后用于更新 Result 对象。

        参数：
            path (str | _Path): 临时目录的路径。
            silence_warnings (bool): 如果为 True，则不触发警告。

        返回：
            Result: 解析后的模型结果。
        """

        # 为了查看输出文件，您可以暂时禁用自动删除。
        # 将以下两行注释掉，并取消注释下面的手动目录创建部分。
        with _tempfile.TemporaryDirectory(dir=path) as tempdir:
        #     # ... 原始代码 ...
        #     return result

        # --- 手动目录创建（用于调试，保留文件） ---
        # output_dir = _Path(path) / f"ahc_run_output_{_time.strftime('%Y%m%d_%H%M%S')}"
        # output_dir.mkdir(parents=True, exist_ok=True)
        # tempdir = str(output_dir)
        # logger.info(f"临时文件将保存在: {tempdir}")

        # try:
            builder = ModelBuilder(self.model, tempdir)
            builder.copy_executable().write_inputs()
            stdout = self.run_ahc(tempdir)

            if "normal completion" not in stdout:
                msg = f"Model run failed. \n {stdout}"
                raise RuntimeError(msg)

            logger.info(stdout)

            # --- Handle the results ---
            result: Result = Result()

            reader = ResultReader(self.model, tempdir)

            log = reader.read_ahc_log()
            result.log = log

            warnings = reader.identify_warnings(log)
            result.warning = warnings

            if warnings and not silence_warnings:
                self.raise_ahc_warning(warnings=warnings)

            if "csv" in self.model.generalsettings.extensions:
                output = reader.read_csv_output(which="csv")
                result.output.update({"csv": output})

            if "csv_tz" in self.model.generalsettings.extensions:
                output_tz = reader.read_csv_output(which="csv_tz")
                result.output.update({"csv_tz": output_tz})

            ascii_files = reader.read_ascii_output()

            result.output.update(ascii_files)
            _time.sleep(0.1) # 添加短暂延迟以确保文件句柄释放
            return result
        # finally:
        #     # 如果手动创建目录，则不需要清理
        #     pass


class ResultReader:
    """负责读取模型结果的类。

    属性：
        model (Model): 要读取结果的模型。
        tempdir (str): 存储结果的临时目录。

    方法：
        read_csv_output: 读取 CSV 输出。
        read_ahc_log: 读取日志文件。
        identify_warnings: 从日志文件中捕获警告。
        read_ascii_output: 将所有非 CSV 格式的输出文件读取为字符串。
    """

    def __init__(self, model: Model, tempdir: str):
        self.model: Model = model
        self.tempdir = tempdir

    def read_csv_output(self, which: _Literal["csv", "csv_tz"]) -> _DataFrame:
        """读取 CSV 输出。

        有两种类型的 CSV 输出文件：csv 和 csv_tz。它们都通过模式更改在相同的方法中处理。

        参数：
            which (str): 要读取的输出文件类型。

        返回：
            _DataFrame: 作为 _DataFrame 的输出文件。
        """

        outfil = self.model.generalsettings.outfil
        output_suffix = "_output.csv" if which == "csv" else "_output_tz.csv"
        index_col = "DATETIME" if which == "csv" else "DATE"

        path = _Path(self.tempdir, outfil + output_suffix)

        if not path.exists():
            logger.warning(f"Expected output file {path} not found.")
            return _DataFrame()

        df = _read_csv(path, comment="*", index_col=index_col)
        df.index = _to_datetime(df.index)

        return df

    def read_ahc_log(self) -> str:
        """读取日志文件。

        返回：
            str: 日志文件的内容。

        引发：
            FileNotFoundError: 如果未找到日志文件。应该始终存在日志文件。如果不是，则表示出现问题。
            FileExistsError: 如果找到多个日志文件。不确定这是否可能。如果可能，则应进行处理。
        """

        log_files = [
            f for f in _Path(self.tempdir).glob("*.log") if f.name != "reruns.log"
        ]

        if len(log_files) == 0:
            msg = "No .log file found in the directory."
            raise FileNotFoundError(msg)

        elif len(log_files) > 1:
            msg = "Multiple .log files found in the directory."
            raise FileExistsError(msg)

        log_file = log_files[0]

        with open(log_file) as file:
            log_content = file.read()

        return log_content

    @staticmethod
    def identify_warnings(log: str) -> list:
        """从日志文件中捕获警告。

        ModelRunner 使用此方法在模型运行后触发警告。

        参数：
            log (str): 日志文件内容。

        返回：
            list: 警告列表。
        """
        lines = log.split("\n")
        warnings = [
            line for line in lines if line.strip().lower().startswith("warning")
        ]
        return warnings

    def read_ascii_output(self):
        """将所有非 CSV 格式的输出文件读取为字符串。

        此方法可能有点过于简化。将来，我们可能会考虑为不同的输出文件引入解析器。
        目前，我们只是将它们读取为字符串。

        返回：
            dict (dict): 以扩展名为键的输出字符串字典。
        """

        ascii_extensions = [
            ext
            for ext in self.model.generalsettings.extensions
            if ext not in ["csv", "csv_tz"]
        ]

        list_dir = _os.listdir(self.tempdir)
        list_dir = [f for f in list_dir if f.endswith(tuple(ascii_extensions))]

        if list_dir:
            dict_files = {
                f.split(".")[1]: open_ascii(_Path(self.tempdir, f)) for f in list_dir
            }
            return dict_files
        return {}


class Model(PyAHCBaseModel, FileMixin, SerializableMixin):
    """运行 AHC 模型的主类

    即使所有部分都设置为可选，如果缺少任何组件，模型也将无法运行

    属性：
        metadata (Subsection): 模型的元数据
        version (str): 模型的版本
        generalsettings (Subsection): 模拟设置
        meteorology (Subsection): 气象数据
        crop (Subsection): 作物数据
        fixedirrigation (Subsection): 固定灌溉设置
        soilmoisture (Subsection): 土壤水分数据
        surfaceflow (Subsection): 地表径流数据
        evaporation (Subsection): 蒸发数据
        soilprofile (Subsection): 土壤剖面数据
        snowandfrost (Subsection): 积雪和霜冻数据
        richards (Subsection): Richards 数据
        lateraldrainage (Subsection): 侧向排水数据
        bottomboundary (Subsection): 底部边界数据
        heatflow (Subsection): 热流数据
        solutetransport (Subsection): 溶质传输数据

    方法：
        write_swp: 写入 .swp 输入文件。
        validate: 验证模型。
        run: 运行模型。
    """

    _validate_on_run: bool = _PrivateAttr(default=False)
    _extension = "swp"

    metadata: Subsection[Metadata] | None = _Field(default=None, repr=False)
    version: str = _Field(exclude=True, default="base")
    ctr_file: Subsection[CtrFile] | None = _Field(default=None, repr=False)
    generalsettings: Subsection[GeneralSettings] | None = _Field(
        default=None, repr=False
    )
    meteorology: Subsection[Meteorology] | None = _Field(default=None, repr=False)
    crop: Subsection[Crop] | None = _Field(default=None, repr=False)
    fixedirrigation: Subsection[FixedIrrigation] | None = _Field(
        default=FixedIrrigation(swirfix=0), repr=False
    )
    soilmoisture: Subsection[SoilMoisture] | None = _Field(default=None, repr=False)
    surfaceflow: Subsection[SurfaceFlow] | None = _Field(default=None, repr=False)
    evaporation: Subsection[Evaporation] | None = _Field(default=None, repr=False)
    soilprofile: Subsection[SoilProfile] | None = _Field(default=None, repr=False)
    snowandfrost: Subsection[SnowAndFrost] | None = _Field(
        default=SnowAndFrost(swsnow=0, swfrost=0), repr=False
    )
    richards: Subsection[RichardsSettings] | None = _Field(
        default=RichardsSettings(swkmean=1, swkimpl=0), repr=False
    )
    lateraldrainage: Subsection[Drainage] | None = _Field(default=None, repr=False)
    bottomboundary: Subsection[BottomBoundary] | None = _Field(default=None, repr=False)
    heatflow: Subsection[HeatFlow] | None = _Field(default=HeatFlow(swhea=0), repr=False)
    solutetransport: Subsection[SoluteTransport] | None = _Field(
        default=SoluteTransport(swsolu=0), repr=False
    )

    @property
    def swp(self):
        """SWP 文件的内容

        Subsection 字段类型的序列化已设置为在父类上调用 `model_string()` 时生成 AHC 格式的字符串
        """
        return self.model_string()

    @_model_validator(mode="after")
    def validate_missing_components(self):
        """在运行时验证所有必需组件是否存在。"""

        if not self._validate_on_run:
            return self

        required_components = [
            "metadata",
            "ctr_file",
            "generalsettings",
            "meteorology",
            "crop",
            "fixedirrigation",
            "soilmoisture",
            "surfaceflow",
            "evaporation",
            "soilprofile",
            "snowandfrost",
            "richards",
            "lateraldrainage",
            "bottomboundary",
            "heatflow",
            "solutetransport",
        ]

        missing_components = [
            comp for comp in required_components if getattr(self, comp) is None
        ]

        if missing_components:
            msg = f"Missing required components: {', '.join(missing_components)}"
            raise ValueError(msg)

        # 验证每个组件
        for comp in required_components:
            getattr(self, comp)

        return self

    @_model_validator(mode="after")
    def validate_each_component(self):
        """在运行时验证所有必需组件是否存在。"""

        if not self._validate_on_run:
            return self

        for comp in self.model_fields:
            item = getattr(self, comp)
            if hasattr(item, "validate_with_yaml"):
                item._validation = True
                item.validate_with_yaml()

        return self

    def validate(self):
        """在调用 `run()` 时执行模型验证。

        此方法可能需要重构。它似乎遮蔽了 Pydantic 中的某些验证方法。
        """

        try:
            self._validate_on_run = True
            self.model_validate(self)
        finally:
            self._validate_on_run = False
            logger.info("Validation successful.")

    def write_swp(self, path: str | _Path):
        """写入 .swp 输入文件。

        参数：
            path (str | _Path): 写入文件的路径。
        """
        from pyahc.components.soilwater import SoilWater
        
        project_name = self.ctr_file.project if self.ctr_file else "hetao"
        
        # 创建SoilWater实例并设置组件
        soilwater = SoilWater(
            surfaceflow=self.surfaceflow if self.surfaceflow else None,
            evaporation=self.evaporation if self.evaporation else None,
            soilmoisture=self.soilmoisture if self.soilmoisture else None,
            soilprofile=self.soilprofile if self.soilprofile else None,
            snowandfrost=self.snowandfrost if self.snowandfrost else None,
            richards=self.richards if self.richards else None,
        )
        
        # 使用SoilWater的write_swp方法
        soilwater.write_swp(path, project_name)
    
    def write_ctr(self, path: str | _Path):
        """写入 AHC.CTR 控制文件。

        参数：
            path (str | _Path): 写入文件的路径。
        """
        if self.ctr_file:
            ctr_content = self.ctr_file.model_string()
            ctr_file_path = _Path(path) / "AHC.CTR"
            with open(ctr_file_path, 'w', encoding='utf-8') as f:
                f.write(ctr_content)
            logger.info(f"已生成 AHC.CTR 文件: {ctr_file_path}")

    def get_inputs(self) -> dict:
        """获取字典中的输入文件。"""
        builder = ModelBuilder(model=self, tempdir=_Path.cwd())
        return builder.get_inputs()

    def to_classic_ahc(self, path: _Path) -> None:
        """在用户目录中准备模型运行的所有文件。"""
        self.validate()
        builder = ModelBuilder(model=self, tempdir=path)

        builder.write_inputs()
        builder.copy_executable()

        logger.info(f"Model files written to {path}")

    def run(
        self, path: str | _Path | None = None, silence_warnings: bool = False
    ) -> Result:
        """使用 ModelRunner 运行模型。"""
        self.validate()
        path = _Path.cwd() if path is None else path
        return ModelRunner(self).run(path, silence_warnings)


def _run_model_with_params(args) -> Result:
    """运行带参数模型的辅助函数。"""
    model, path, silence_warnings = args
    return model.run(path=path, silence_warnings=silence_warnings)


def run_parallel(
    mls: list[Model],
    path: _Path | str | None = None,
    silence_warnings: bool = False,
    **kwargs,
) -> list[Result]:
    """并行运行多个模型。

    参数：
        mls (list[Model]): 要运行的模型列表。
        path (_Path | str): 临时目录的路径。
        silence_warnings (bool): 如果为 True，则不触发警告。
        **kwargs (dict): _Pool() 的关键字参数。

    返回：
        list[Result]: 模型运行结果列表。
    """
    with _Pool(**kwargs) as pool:
        results = pool.map(
            _run_model_with_params, [(model, path, silence_warnings) for model in mls]
        )

    return results

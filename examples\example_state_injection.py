"""StateInjector使用示例

本示例展示如何使用StateInjector进行状态变量注入，
实现日循环模拟中的状态传递。
"""

import numpy as np
import logging
from datetime import date, timedelta
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入必要的模块
from pyahc.db.hdf5.state_injector import StateInjector
from pyahc.db.hdf5.state_extractor import StateExtractor


def create_example_model():
    """创建示例模型对象"""
    try:
        from pyahc.model.model import Model
        from pyahc.components.soilwater import SoilMoisture
        from pyahc.components.crop import Crop
        from pyahc.components.boundary import BottomBoundary
        from pyahc.components.simsettings import GeneralSettings
        from pyahc.components.meteorology import Meteorology
        
        logger.info("创建示例模型...")
        
        # 创建基本设置
        general_settings = GeneralSettings(
            project="State Injection Example",
            pathwork=str(Path.cwd()),
            swscre=0,
            swerror=1,
            extensions=["csv"]
        )
        
        # 创建土壤含水量组件
        soil_moisture = SoilMoisture(
            swinco=0,  # 土壤含水量作为输入
            thetai=[0.40, 0.35, 0.30, 0.25]  # 初始土壤含水量（4层）
        )
        
        # 创建作物组件
        crop = Crop(
            swcrop=1,  # 模拟作物
            leaf_area_index=2.5,  # 初始LAI
            rds=60.0  # 初始根深
        )
        
        # 创建底边界组件
        bottom_boundary = BottomBoundary(
            swbbcfile=0,
            swbotb=1,  # 规定地下水位
            gwlevel_data=[(1, 1, -120.0), (31, 12, -120.0)]  # 地下水位数据
        )
        
        # 创建气象组件（简化）
        meteorology = Meteorology(
            swmetfil=0
        )
        
        # 创建模型
        model = Model(
            generalsettings=general_settings,
            soilmoisture=soil_moisture,
            crop=crop,
            bottomboundary=bottom_boundary,
            meteorology=meteorology
        )
        
        logger.info("示例模型创建成功")
        return model
        
    except ImportError as e:
        logger.error(f"无法导入必要的组件: {e}")
        return None


def example_basic_state_injection():
    """基本状态注入示例"""
    logger.info("=== 基本状态注入示例 ===")
    
    # 创建模型
    model = create_example_model()
    if model is None:
        logger.error("无法创建模型，跳过示例")
        return
    
    # 显示原始状态
    logger.info("原始模型状态:")
    logger.info(f"  土壤含水量: {model.soilmoisture.thetai}")
    logger.info(f"  LAI: {model.crop.leaf_area_index}")
    logger.info(f"  根深: {model.crop.rds}")
    
    # 准备要注入的状态变量
    states_to_inject = {
        'soil_moisture': np.array([0.45, 0.40, 0.35, 0.30]),
        'lai': 4.2,
        'root_depth': 85.0,
        'groundwater_level': -150.0
    }
    
    logger.info("要注入的状态变量:")
    for key, value in states_to_inject.items():
        logger.info(f"  {key}: {value}")
    
    # 执行状态注入
    logger.info("执行状态注入...")
    updated_model = StateInjector.inject_all_states(model, states_to_inject)
    
    # 显示更新后的状态
    logger.info("更新后的模型状态:")
    logger.info(f"  土壤含水量: {updated_model.soilmoisture.thetai}")
    logger.info(f"  LAI: {updated_model.crop.leaf_area_index}")
    logger.info(f"  根深: {updated_model.crop.rds}")
    
    # 验证原始模型未被修改
    logger.info("验证原始模型未被修改:")
    logger.info(f"  原始土壤含水量: {model.soilmoisture.thetai}")
    logger.info(f"  原始LAI: {model.crop.leaf_area_index}")
    
    return updated_model


def example_partial_state_injection():
    """部分状态注入示例"""
    logger.info("\n=== 部分状态注入示例 ===")
    
    model = create_example_model()
    if model is None:
        return
    
    # 只注入部分状态变量
    partial_states = {
        'soil_moisture': np.array([0.38, 0.33, 0.28, 0.23]),
        'lai': 3.8
        # 不注入生物量、根深和地下水位
    }
    
    logger.info("只注入土壤含水量和LAI...")
    updated_model = StateInjector.inject_all_states(model, partial_states)
    
    logger.info("部分注入结果:")
    logger.info(f"  土壤含水量 (已更新): {updated_model.soilmoisture.thetai}")
    logger.info(f"  LAI (已更新): {updated_model.crop.leaf_area_index}")
    logger.info(f"  根深 (未更新): {updated_model.crop.rds}")


def example_individual_state_injection():
    """单独状态注入示例"""
    logger.info("\n=== 单独状态注入示例 ===")
    
    model = create_example_model()
    if model is None:
        return
    
    # 单独注入土壤含水量
    logger.info("单独注入土壤含水量...")
    moisture = np.array([0.42, 0.37, 0.32, 0.27])
    model_with_moisture = StateInjector.inject_soil_moisture(model, moisture)
    logger.info(f"注入后土壤含水量: {model_with_moisture.soilmoisture.thetai}")
    
    # 单独注入作物状态
    logger.info("单独注入作物状态...")
    model_with_crop = StateInjector.inject_crop_state(
        model_with_moisture,
        lai=3.5,
        root_depth=75.0
    )
    logger.info(f"注入后LAI: {model_with_crop.crop.leaf_area_index}")
    logger.info(f"注入后根深: {model_with_crop.crop.rds}")
    
    # 单独注入地下水位
    logger.info("单独注入地下水位...")
    model_with_gw = StateInjector.inject_groundwater_level(model_with_crop, -180.0)
    if model_with_gw.bottomboundary.gwlevel_data:
        gw_level = model_with_gw.bottomboundary.gwlevel_data[0][2]
        logger.info(f"注入后地下水位: {gw_level}")


def example_daily_simulation_workflow():
    """日循环模拟工作流程示例"""
    logger.info("\n=== 日循环模拟工作流程示例 ===")
    
    base_model = create_example_model()
    if base_model is None:
        return
    
    # 模拟3天的循环
    simulation_days = 3
    current_date = date(2023, 6, 1)
    
    # 初始状态（第一天）
    previous_states = None
    
    for day in range(simulation_days):
        logger.info(f"\n--- 第 {day + 1} 天 ({current_date}) ---")
        
        # 准备当日模型
        if previous_states is None:
            # 第一天使用原始模型
            daily_model = base_model.model_copy(deep=True)
            logger.info("使用原始模型状态")
        else:
            # 后续天数注入前一天的状态
            daily_model = StateInjector.inject_all_states(base_model, previous_states)
            logger.info("注入前一天的状态:")
            for key, value in previous_states.items():
                logger.info(f"  {key}: {value}")
        
        # 模拟运行模型（这里只是示例，不实际运行）
        logger.info("模拟运行模型...")
        
        # 模拟提取状态（实际应该从模型结果中提取）
        simulated_states = {
            'soil_moisture': np.array([0.35 + day * 0.02, 0.30 + day * 0.02,
                                     0.25 + day * 0.02, 0.20 + day * 0.02]),
            'lai': 2.5 + day * 0.5,
            'root_depth': 60.0 + day * 5.0,
            'groundwater_level': -120.0 - day * 10.0
        }
        
        logger.info("提取的状态变量:")
        for key, value in simulated_states.items():
            logger.info(f"  {key}: {value}")
        
        # 保存状态用于下一天
        previous_states = simulated_states
        
        # 移动到下一天
        current_date += timedelta(days=1)


def example_error_handling():
    """错误处理示例"""
    logger.info("\n=== 错误处理示例 ===")
    
    model = create_example_model()
    if model is None:
        return
    
    # 测试无效的状态数据
    invalid_states = {
        'soil_moisture': np.array([1.5, -0.1, 0.3, 0.25]),  # 超出范围
        'lai': -1.0,  # 负值
        'root_depth': 'invalid_value',  # 非数值
        'groundwater_level': None  # 空值
    }
    
    logger.info("测试无效状态数据的处理...")
    logger.info("无效状态数据:")
    for key, value in invalid_states.items():
        logger.info(f"  {key}: {value}")
    
    # StateInjector应该能够处理这些无效数据
    result_model = StateInjector.inject_all_states(model, invalid_states)
    
    logger.info("处理结果:")
    logger.info(f"  土壤含水量 (应被裁剪): {result_model.soilmoisture.thetai}")
    logger.info(f"  LAI (应保持原值): {result_model.crop.leaf_area_index}")
    logger.info("错误处理成功，模型保持稳定")


def main():
    """主函数"""
    logger.info("StateInjector使用示例开始")
    
    try:
        # 运行各种示例
        example_basic_state_injection()
        example_partial_state_injection()
        example_individual_state_injection()
        example_daily_simulation_workflow()
        example_error_handling()
        
        logger.info("\n所有示例运行完成！")
        
    except Exception as e:
        logger.error(f"示例运行出错: {e}")
        raise


if __name__ == "__main__":
    main()

# pyAHC集成HDF5数据同化支持方案

## 1. 项目背景与需求分析

### 1.1 当前项目架构
pyAHC是一个Python封装的AHC（Agricultural Hydrological Crop）模型，具有以下特点：
- 基于Pydantic的组件化架构
- 支持多种输入输出格式（.txt, .csv等）
- 完整的验证框架
- 模块化的组件设计（气象、作物、土壤等）

### 1.2 数据同化需求
您的需求场景：
1. **日循环模拟**：每天运行模型，前一天的输出状态作为下一天的输入
2. **状态变量传递**：土壤含水量、作物LAI等状态变量需要在时间步之间传递
3. **数据同化准备**：为后续集成观测数据同化功能提供基础架构
4. **科学数据管理**：结构化存储模型状态、参数和结果

### 1.3 HDF5的优势
- **层次化存储**：适合存储复杂的模型状态和时间序列数据
- **高性能**：二进制格式，读写效率高
- **跨平台**：广泛的科学计算支持
- **元数据支持**：可存储丰富的描述信息
- **压缩支持**：节省存储空间

## 2. HDF5集成架构设计

### 2.1 核心设计原则
1. **非侵入性**：不破坏现有组件架构
2. **可扩展性**：为未来数据同化功能预留接口
3. **向后兼容**：保持对现有文本格式的支持
4. **性能优化**：高效的数据读写操作

### 2.2 HDF5数据结构设计

基于您的建议，采用更加清晰的项目-日期分层结构：

```
project.h5
├── corn_001-2013/                    # 项目组（玉米地块001-2013年）
│   ├── metadata/                     # 项目元数据
│   │   ├── model_info               # 模型版本、配置信息
│   │   ├── simulation_period        # 模拟时间段
│   │   ├── location_info            # 地块位置信息
│   │   └── crop_info                # 作物信息
│   ├── day_05-01/                    # 日期组（5月1日）
│   │   ├── input/                    # 模型输入
│   │   │   └── model_object         # pickled Model对象
│   │   ├── output/                   # 模型输出
│   │   │   └── result_object        # pickled Result对象
│   │   └── state-parameter_variables/ # 状态-参数变量
│   │       ├── soil_moisture         # 土壤含水量（状态变量）
│   │       ├── lai                   # 叶面积指数（状态变量）
│   │       ├── biomass               # 生物量（状态变量）
│   │       ├── root_depth            # 根深（状态变量）
│   │       ├── groundwater_level     # 地下水位（状态变量）
│   │       ├── param_hydraulic_conductivity # 土壤导水率（参数变量）
│   │       ├── param_crop_coefficient       # 作物系数（参数变量）
│   │       └── assimilated_*         # 数据同化后的变量
│   ├── day_05-02/                    # 5月2日
│   │   ├── input/                    # 包含前一天的状态-参数变量
│   │   ├── output/                   # 当天模拟结果
│   │   └── state-parameter_variables/ # 状态-参数变量
│   ├── day_05-03/                    # 5月3日
│   │   └── ...                       # 类似结构
│   └── summary/                      # 项目汇总数据
│       ├── timeseries/              # 时间序列汇总
│       │   ├── lai_timeseries       # LAI时间序列
│       │   ├── biomass_timeseries   # 生物量时间序列
│       │   └── moisture_timeseries  # 土壤含水量时间序列
│       └── statistics/              # 统计信息
├── corn_002-2013/                    # 其他地块项目
│   └── ...                          # 相同结构
└── wheat_001-2014/                   # 不同作物项目
    └── ...                          # 相同结构
```

#### 数据结构优势：
1. **项目隔离**：不同地块和年份的数据完全分离
2. **日期组织**：每日数据独立存储，便于访问和管理
3. **状态-参数分离**：清晰区分状态变量和参数变量
4. **对象序列化**：直接存储Model和Result对象，保持完整性
5. **汇总支持**：项目级别的时间序列和统计数据

### 2.3 类层次结构设计

基于新的分层结构，重新设计类层次：

```python
# 核心HDF5管理类
pyahc.db.hdf5.ProjectHDF5Manager
├── ProjectManager        # 项目级别管理
├── DailyManager         # 日期级别管理
├── StateParameterManager # 状态-参数变量管理
└── AssimilationManager   # 数据同化管理（预留）

# 项目管理类
pyahc.db.hdf5.ProjectManager
├── create_project()      # 创建新项目
├── list_projects()       # 列出所有项目
├── get_project_metadata() # 获取项目元数据
└── delete_project()      # 删除项目

# 日期管理类
pyahc.db.hdf5.DailyManager
├── save_daily_data()     # 保存日数据
├── load_daily_data()     # 加载日数据
├── get_date_range()      # 获取日期范围
└── copy_states_to_next_day() # 状态传递到下一天

# 状态-参数管理类
pyahc.db.hdf5.StateParameterManager
├── StateVariables        # 状态变量子类
│   ├── soil_moisture     # 土壤含水量
│   ├── lai              # 叶面积指数
│   ├── biomass          # 生物量
│   ├── root_depth       # 根深
│   └── groundwater_level # 地下水位
├── ParameterVariables    # 参数变量子类
│   ├── hydraulic_conductivity # 土壤导水率
│   ├── crop_coefficient      # 作物系数
│   └── custom_parameters     # 自定义参数
└── AssimilatedVariables  # 同化变量子类
    ├── assimilated_lai   # 同化后LAI
    ├── assimilated_biomass # 同化后生物量
    └── assimilated_moisture # 同化后土壤含水量

# 对象序列化管理
pyahc.db.hdf5.ObjectManager
├── save_model_object()   # 保存Model对象
├── load_model_object()   # 加载Model对象
├── save_result_object()  # 保存Result对象
└── load_result_object()  # 加载Result对象
```

## 3. 核心功能实现

### 3.1 ProjectHDF5Manager核心类

```python
class ProjectHDF5Manager:
    """基于项目-日期结构的HDF5数据管理核心类"""

    def __init__(self, filepath: Path, mode: str = 'a'):
        self.filepath = filepath
        self.mode = mode
        self._file = None
        self.logger = logging.getLogger(__name__)

    def __enter__(self):
        self.open()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def open(self):
        """打开HDF5文件"""
        if self._file is None:
            self._file = h5py.File(self.filepath, self.mode)
            self.logger.info(f"Opened HDF5 file: {self.filepath}")

    def close(self):
        """关闭HDF5文件"""
        if self._file is not None:
            self._file.close()
            self._file = None
            self.logger.info("Closed HDF5 file")

    def create_project(self, project_name: str, metadata: Dict[str, Any]) -> None:
        """创建新项目"""
        self.open()

        if project_name in self._file:
            raise ValueError(f"Project {project_name} already exists")

        # 创建项目组
        project_group = self._file.create_group(project_name)

        # 创建元数据组
        metadata_group = project_group.create_group('metadata')

        # 保存元数据
        for key, value in metadata.items():
            if isinstance(value, dict):
                subgroup = metadata_group.create_group(key)
                for subkey, subvalue in value.items():
                    subgroup.attrs[subkey] = subvalue
            else:
                metadata_group.attrs[key] = value

        self.logger.info(f"Created project: {project_name}")

    def save_daily_data(self, project_name: str, date: Union[date, datetime],
                       model: 'Model', result: 'Result',
                       states: Dict[str, Any], parameters: Dict[str, Any] = None) -> None:
        """保存日数据（模型、结果、状态、参数）"""
        self.open()

        if project_name not in self._file:
            raise ValueError(f"Project {project_name} does not exist")

        # 格式化日期字符串
        if isinstance(date, datetime):
            date = date.date()
        date_str = f"day_{date.strftime('%m-%d')}"

        project_group = self._file[project_name]

        # 创建或获取日期组
        if date_str in project_group:
            day_group = project_group[date_str]
        else:
            day_group = project_group.create_group(date_str)

        # 保存模型输入对象
        self._save_model_object(day_group, model)

        # 保存模型输出对象
        self._save_result_object(day_group, result)

        # 保存状态-参数变量
        self._save_state_parameter_variables(day_group, states, parameters)

        self.logger.info(f"Saved daily data for {project_name}/{date_str}")

    def load_daily_data(self, project_name: str, date: Union[date, datetime]) -> Dict[str, Any]:
        """加载日数据"""
        self.open()

        if isinstance(date, datetime):
            date = date.date()
        date_str = f"day_{date.strftime('%m-%d')}"

        if project_name not in self._file:
            raise ValueError(f"Project {project_name} does not exist")

        project_group = self._file[project_name]
        if date_str not in project_group:
            raise ValueError(f"Date {date_str} not found in project {project_name}")

        day_group = project_group[date_str]

        # 加载各种数据
        data = {
            'model': self._load_model_object(day_group),
            'result': self._load_result_object(day_group),
            'states': self._load_state_variables(day_group),
            'parameters': self._load_parameter_variables(day_group)
        }

        return data
```

    def _save_model_object(self, day_group: h5py.Group, model: 'Model') -> None:
        """保存Model对象"""
        import pickle

        # 创建input组
        if 'input' not in day_group:
            input_group = day_group.create_group('input')
        else:
            input_group = day_group['input']

        # 序列化Model对象
        model_bytes = pickle.dumps(model)

        # 保存为数据集
        if 'model_object' in input_group:
            del input_group['model_object']
        dataset = input_group.create_dataset('model_object', data=np.void(model_bytes))
        dataset.attrs['type'] = 'pickled_model'
        dataset.attrs['class'] = 'pyahc.model.model.Model'

    def _save_result_object(self, day_group: h5py.Group, result: 'Result') -> None:
        """保存Result对象"""
        import pickle

        # 创建output组
        if 'output' not in day_group:
            output_group = day_group.create_group('output')
        else:
            output_group = day_group['output']

        # 序列化Result对象
        result_bytes = pickle.dumps(result)

        # 保存为数据集
        if 'result_object' in output_group:
            del output_group['result_object']
        dataset = output_group.create_dataset('result_object', data=np.void(result_bytes))
        dataset.attrs['type'] = 'pickled_result'
        dataset.attrs['class'] = 'pyahc.model.result.Result'

    def _save_state_parameter_variables(self, day_group: h5py.Group,
                                      states: Dict[str, Any],
                                      parameters: Dict[str, Any] = None) -> None:
        """保存状态-参数变量"""

        # 创建state-parameter_variables组
        if 'state-parameter_variables' not in day_group:
            sp_group = day_group.create_group('state-parameter_variables')
        else:
            sp_group = day_group['state-parameter_variables']

        # 保存状态变量
        for var_name, value in states.items():
            if var_name in sp_group:
                del sp_group[var_name]

            if isinstance(value, np.ndarray):
                dataset = sp_group.create_dataset(var_name, data=value, compression='gzip')
            else:
                dataset = sp_group.create_dataset(var_name, data=value)

            dataset.attrs['type'] = 'state_variable'
            dataset.attrs['units'] = self._get_variable_units(var_name)

        # 保存参数变量
        if parameters:
            for param_name, value in parameters.items():
                param_key = f"param_{param_name}"
                if param_key in sp_group:
                    del sp_group[param_key]

                if isinstance(value, np.ndarray):
                    dataset = sp_group.create_dataset(param_key, data=value, compression='gzip')
                else:
                    dataset = sp_group.create_dataset(param_key, data=value)

                dataset.attrs['type'] = 'parameter_variable'
                dataset.attrs['units'] = self._get_variable_units(param_name)

    def _load_model_object(self, day_group: h5py.Group) -> 'Model':
        """加载Model对象"""
        import pickle

        if 'input' not in day_group or 'model_object' not in day_group['input']:
            return None

        dataset = day_group['input']['model_object']
        model_bytes = dataset[()].tobytes()
        model = pickle.loads(model_bytes)

        return model

    def _load_result_object(self, day_group: h5py.Group) -> 'Result':
        """加载Result对象"""
        import pickle

        if 'output' not in day_group or 'result_object' not in day_group['output']:
            return None

        dataset = day_group['output']['result_object']
        result_bytes = dataset[()].tobytes()
        result = pickle.loads(result_bytes)

        return result

    def _load_state_variables(self, day_group: h5py.Group) -> Dict[str, Any]:
        """加载状态变量"""
        states = {}

        if 'state-parameter_variables' not in day_group:
            return states

        sp_group = day_group['state-parameter_variables']

        for key in sp_group.keys():
            dataset = sp_group[key]
            if dataset.attrs.get('type') == 'state_variable':
                states[key] = dataset[()]

        return states

    def _load_parameter_variables(self, day_group: h5py.Group) -> Dict[str, Any]:
        """加载参数变量"""
        parameters = {}

        if 'state-parameter_variables' not in day_group:
            return parameters

        sp_group = day_group['state-parameter_variables']

        for key in sp_group.keys():
            if key.startswith('param_'):
                dataset = sp_group[key]
                if dataset.attrs.get('type') == 'parameter_variable':
                    param_name = key[6:]  # 移除'param_'前缀
                    parameters[param_name] = dataset[()]

        return parameters

### 3.2 状态变量提取器

```python
class StateExtractor:
    """从模型结果中提取状态变量 - 适配新的HDF5结构"""

    @staticmethod
    def extract_all_states(result: 'Result') -> Dict[str, Any]:
        """提取所有状态变量"""
        states = {}

        # 提取土壤含水量
        soil_moisture = StateExtractor.extract_soil_moisture(result)
        if soil_moisture is not None:
            states['soil_moisture'] = soil_moisture

        # 提取LAI
        lai = StateExtractor.extract_crop_lai(result)
        if lai is not None:
            states['lai'] = lai

        # 提取生物量
        biomass = StateExtractor.extract_crop_biomass(result)
        if biomass is not None:
            states['biomass'] = biomass

        # 提取根深
        root_depth = StateExtractor.extract_root_depth(result)
        if root_depth is not None:
            states['root_depth'] = root_depth

        # 提取地下水位
        groundwater_level = StateExtractor.extract_groundwater_level(result)
        if groundwater_level is not None:
            states['groundwater_level'] = groundwater_level

        return states

    @staticmethod
    def extract_soil_moisture(result: 'Result') -> Optional[np.ndarray]:
        """提取土壤含水量剖面"""
        try:
            # 从CSV输出中提取
            if hasattr(result, 'csv') and result.csv is not None:
                moisture_cols = [col for col in result.csv.columns
                               if 'moisture' in col.lower() or 'theta' in col.lower()]
                if moisture_cols:
                    return result.csv[moisture_cols].iloc[-1].values

            # 从ASCII输出中解析
            if hasattr(result, 'ascii') and result.ascii:
                for file_ext, content in result.ascii.items():
                    if 'sba' in file_ext.lower():
                        return StateExtractor._parse_soil_moisture_from_sba(content)

            return None

        except Exception as e:
            logging.warning(f"Failed to extract soil moisture: {e}")
            return None
```

    @staticmethod
    def extract_crop_lai(result: 'Result') -> Optional[float]:
        """提取作物叶面积指数"""
        try:
            if hasattr(result, 'csv') and result.csv is not None:
                lai_cols = [col for col in result.csv.columns
                           if 'lai' in col.lower() or 'leaf_area' in col.lower()]
                if lai_cols:
                    return float(result.csv[lai_cols[0]].iloc[-1])

            return None

        except Exception as e:
            logging.warning(f"Failed to extract LAI: {e}")
            return None

    @staticmethod
    def extract_crop_biomass(result: 'Result') -> Optional[float]:
        """提取作物生物量"""
        try:
            if hasattr(result, 'csv') and result.csv is not None:
                biomass_cols = [col for col in result.csv.columns
                              if 'biomass' in col.lower() or 'dry_matter' in col.lower()]
                if biomass_cols:
                    return float(result.csv[biomass_cols[0]].iloc[-1])

            return None

        except Exception as e:
            logging.warning(f"Failed to extract biomass: {e}")
            return None

    @staticmethod
    def extract_root_depth(result: 'Result') -> Optional[float]:
        """提取根深"""
        try:
            if hasattr(result, 'csv') and result.csv is not None:
                root_cols = [col for col in result.csv.columns
                           if 'root' in col.lower() and 'depth' in col.lower()]
                if root_cols:
                    return float(result.csv[root_cols[0]].iloc[-1])

            return None

        except Exception as e:
            logging.warning(f"Failed to extract root depth: {e}")
            return None

    @staticmethod
    def extract_groundwater_level(result: 'Result') -> Optional[float]:
        """提取地下水位"""
        try:
            if hasattr(result, 'csv') and result.csv is not None:
                gw_cols = [col for col in result.csv.columns
                          if 'groundwater' in col.lower() or 'water_table' in col.lower()]
                if gw_cols:
                    return float(result.csv[gw_cols[0]].iloc[-1])

            return None

        except Exception as e:
            logging.warning(f"Failed to extract groundwater level: {e}")
            return None

### 3.3 状态变量注入器

```python
class StateInjector:
    """将状态变量注入到模型输入中 - 适配新的HDF5结构"""

    @staticmethod
    def inject_all_states(model: 'Model', states: Dict[str, Any]) -> 'Model':
        """注入所有状态变量"""
        updated_model = model.model_copy(deep=True)

        # 注入土壤含水量
        if 'soil_moisture' in states:
            updated_model = StateInjector.inject_soil_moisture(
                updated_model, states['soil_moisture']
            )

        # 注入作物状态
        if 'lai' in states or 'biomass' in states:
            updated_model = StateInjector.inject_crop_state(
                updated_model,
                lai=states.get('lai'),
                biomass=states.get('biomass'),
                root_depth=states.get('root_depth')
            )

        # 注入地下水位
        if 'groundwater_level' in states:
            updated_model = StateInjector.inject_groundwater_level(
                updated_model, states['groundwater_level']
            )

        return updated_model

    @staticmethod
    def inject_soil_moisture(model: 'Model', moisture: np.ndarray) -> 'Model':
        """注入土壤含水量"""
        try:
            updated_model = model.model_copy(deep=True)

            if updated_model.soilmoisture and hasattr(updated_model.soilmoisture, 'thetai'):
                updated_model.soilmoisture.thetai = moisture.tolist()

            return updated_model

        except Exception as e:
            logging.error(f"Failed to inject soil moisture: {e}")
            return model

    @staticmethod
    def inject_crop_state(model: 'Model', lai: Optional[float] = None,
                         biomass: Optional[float] = None,
                         root_depth: Optional[float] = None) -> 'Model':
        """注入作物状态变量"""
        try:
            updated_model = model.model_copy(deep=True)

            # 这里需要根据具体的pyAHC模型结构来实现
            # 可能需要更新作物发育阶段或直接设置状态值

            return updated_model

        except Exception as e:
            logging.error(f"Failed to inject crop state: {e}")
            return model

    @staticmethod
    def inject_groundwater_level(model: 'Model', gw_level: float) -> 'Model':
        """注入地下水位"""
        try:
            updated_model = model.model_copy(deep=True)

            # 更新地下水位相关参数
            if updated_model.bottomboundary:
                # 根据pyAHC的具体结构更新地下水位
                pass

            return updated_model

        except Exception as e:
            logging.error(f"Failed to inject groundwater level: {e}")
            return model
```

## 4. 数据同化工作流程

### 4.1 初始化阶段
```python
def initialize_project_simulation(config: Dict[str, Any]) -> ProjectHDF5Manager:
    """初始化项目数据同化模拟"""

    # 创建HDF5管理器
    hdf5_path = config.get('hdf5_path', 'project.h5')
    manager = ProjectHDF5Manager(hdf5_path)

    # 项目配置
    project_name = config['project_name']  # 例如: "corn_001-2013"

    # 项目元数据
    metadata = {
        'model_info': {
            'pyahc_version': '0.1.0',
            'ahc_version': 'V201',
            'creation_date': datetime.now().isoformat()
        },
        'simulation_period': {
            'start_date': config['start_date'].isoformat(),
            'end_date': config['end_date'].isoformat()
        },
        'location_info': {
            'field_id': config.get('field_id', 'unknown'),
            'latitude': config.get('latitude'),
            'longitude': config.get('longitude'),
            'altitude': config.get('altitude')
        },
        'crop_info': {
            'crop_type': config.get('crop_type', 'corn'),
            'variety': config.get('variety', 'unknown'),
            'planting_date': config.get('planting_date', '').isoformat() if config.get('planting_date') else ''
        }
    }

    # 创建项目
    manager.create_project(project_name, metadata)

    return manager
```

### 4.2 日循环模拟
```python
def daily_simulation_step_v2(manager: ProjectHDF5Manager,
                           project_name: str,
                           current_date: date,
                           base_model: 'Model') -> 'Result':
    """执行单日模拟步骤 - 新版本"""

    # 1. 准备当日模型
    daily_model = base_model.model_copy(deep=True)

    # 2. 加载前一日状态（如果存在）
    previous_date = current_date - timedelta(days=1)
    try:
        previous_data = manager.load_daily_data(project_name, previous_date)
        if previous_data and previous_data['states']:
            daily_model = StateInjector.inject_all_states(
                daily_model, previous_data['states']
            )
            logging.info(f"Loaded states from {previous_date}")
    except Exception as e:
        logging.warning(f"Could not load previous states: {e}")

    # 3. 更新模拟日期
    daily_model.generalsettings.tstart = current_date
    daily_model.generalsettings.tend = current_date

    # 4. 运行模型
    try:
        result = daily_model.run()
        logging.info(f"Model run completed for {current_date}")
    except Exception as e:
        logging.error(f"Model run failed for {current_date}: {e}")
        raise

    # 5. 提取状态变量
    current_states = StateExtractor.extract_all_states(result)

    # 6. 提取参数变量（如果需要）
    current_parameters = extract_parameters_from_model(daily_model)

    # 7. 保存所有数据
    manager.save_daily_data(
        project_name, current_date, daily_model, result,
        current_states, current_parameters
    )

    logging.info(f"Saved all data for {project_name}/{current_date}")

    return result

def extract_parameters_from_model(model: 'Model') -> Dict[str, Any]:
    """从模型中提取参数变量"""
    parameters = {}

    try:
        # 提取土壤导水率
        if model.soilprofile and hasattr(model.soilprofile, 'hydraulic_conductivity'):
            parameters['hydraulic_conductivity'] = model.soilprofile.hydraulic_conductivity

        # 提取作物系数
        if model.crop and hasattr(model.crop, 'crop_coefficient'):
            parameters['crop_coefficient'] = model.crop.crop_coefficient

        # 可以根据需要添加更多参数

    except Exception as e:
        logging.warning(f"Failed to extract parameters: {e}")

    return parameters
```

### 4.3 数据同化接口（预留）
```python
def assimilate_observations_v2(manager: ProjectHDF5Manager,
                             project_name: str,
                             observations: Dict[str, Any],
                             date: date) -> None:
    """数据同化接口 - 新版本（预留实现）"""

    try:
        # 1. 加载当前日数据
        daily_data = manager.load_daily_data(project_name, date)
        current_states = daily_data['states']

        # 2. 应用同化算法（这里是占位符）
        # updated_states = apply_enkf_assimilation(current_states, observations)

        # 3. 保存同化后的状态变量
        with manager:
            date_str = f"day_{date.strftime('%m-%d')}"
            project_group = manager._file[project_name]
            day_group = project_group[date_str]
            sp_group = day_group['state-parameter_variables']

            # 保存同化后的变量
            for var_name, value in observations.items():
                assim_key = f"assimilated_{var_name}"
                if assim_key in sp_group:
                    del sp_group[assim_key]

                dataset = sp_group.create_dataset(assim_key, data=value)
                dataset.attrs['type'] = 'assimilated_variable'
                dataset.attrs['assimilation_date'] = date.isoformat()
                dataset.attrs['original_variable'] = var_name

        logging.info(f"Applied data assimilation for {project_name}/{date}")

    except Exception as e:
        logging.error(f"Data assimilation failed for {project_name}/{date}: {e}")

def run_project_simulation(config: Dict[str, Any]) -> ProjectHDF5Manager:
    """运行完整的项目模拟"""

    # 初始化项目
    manager = initialize_project_simulation(config)
    project_name = config['project_name']

    # 获取模拟参数
    start_date = config['start_date']
    end_date = config['end_date']

    # 创建基础模型
    base_model = create_model_components()  # 使用现有函数

    # 日循环模拟
    current_date = start_date
    total_days = (end_date - start_date).days + 1

    logging.info(f"Starting project simulation: {project_name}")
    logging.info(f"Period: {start_date} to {end_date} ({total_days} days)")

    with manager:
        day_count = 0
        while current_date <= end_date:
            try:
                # 执行单日模拟
                result = daily_simulation_step_v2(
                    manager, project_name, current_date, base_model
                )

                # 可选：检查观测数据同化
                if config.get('enable_assimilation', False):
                    observations = load_observations_for_date(current_date, config)
                    if observations:
                        assimilate_observations_v2(
                            manager, project_name, observations, current_date
                        )

                day_count += 1
                if day_count % 10 == 0:
                    logging.info(f"Completed {day_count}/{total_days} days")

            except Exception as e:
                logging.error(f"Simulation failed on {current_date}: {e}")
                if config.get('stop_on_error', True):
                    raise

            current_date += timedelta(days=1)

        # 生成项目汇总数据
        generate_project_summary(manager, project_name, start_date, end_date)

    logging.info(f"Project simulation completed: {project_name}")
    return manager

def generate_project_summary(manager: ProjectHDF5Manager,
                           project_name: str,
                           start_date: date,
                           end_date: date) -> None:
    """生成项目汇总数据"""

    with manager:
        project_group = manager._file[project_name]

        # 创建summary组
        if 'summary' not in project_group:
            summary_group = project_group.create_group('summary')
            timeseries_group = summary_group.create_group('timeseries')
            statistics_group = summary_group.create_group('statistics')
        else:
            summary_group = project_group['summary']
            timeseries_group = summary_group['timeseries']
            statistics_group = summary_group['statistics']

        # 收集时间序列数据
        lai_series = []
        biomass_series = []
        moisture_series = []
        dates = []

        current_date = start_date
        while current_date <= end_date:
            try:
                daily_data = manager.load_daily_data(project_name, current_date)
                states = daily_data['states']

                dates.append(current_date.isoformat())
                lai_series.append(states.get('lai', np.nan))
                biomass_series.append(states.get('biomass', np.nan))

                if 'soil_moisture' in states:
                    moisture_series.append(states['soil_moisture'])
                else:
                    moisture_series.append(np.full(30, np.nan))  # 假设30层

            except Exception:
                # 如果某天数据不存在，填充NaN
                dates.append(current_date.isoformat())
                lai_series.append(np.nan)
                biomass_series.append(np.nan)
                moisture_series.append(np.full(30, np.nan))

            current_date += timedelta(days=1)

        # 保存时间序列
        if 'lai_timeseries' in timeseries_group:
            del timeseries_group['lai_timeseries']
        lai_dataset = timeseries_group.create_dataset('lai_timeseries', data=lai_series)
        lai_dataset.attrs['units'] = 'm2/m2'
        lai_dataset.attrs['dates'] = [d.encode('utf-8') for d in dates]

        if 'biomass_timeseries' in timeseries_group:
            del timeseries_group['biomass_timeseries']
        biomass_dataset = timeseries_group.create_dataset('biomass_timeseries', data=biomass_series)
        biomass_dataset.attrs['units'] = 'kg/ha'
        biomass_dataset.attrs['dates'] = [d.encode('utf-8') for d in dates]

        if 'moisture_timeseries' in timeseries_group:
            del timeseries_group['moisture_timeseries']
        moisture_dataset = timeseries_group.create_dataset(
            'moisture_timeseries',
            data=np.array(moisture_series),
            compression='gzip'
        )
        moisture_dataset.attrs['units'] = 'cm3/cm3'
        moisture_dataset.attrs['dates'] = [d.encode('utf-8') for d in dates]

        # 计算和保存统计信息
        lai_array = np.array(lai_series)
        biomass_array = np.array(biomass_series)

        stats = {
            'lai_max': np.nanmax(lai_array),
            'lai_mean': np.nanmean(lai_array),
            'lai_min': np.nanmin(lai_array),
            'biomass_max': np.nanmax(biomass_array),
            'biomass_mean': np.nanmean(biomass_array),
            'biomass_final': biomass_array[-1] if not np.isnan(biomass_array[-1]) else np.nan
        }

        for key, value in stats.items():
            statistics_group.attrs[key] = value

        logging.info(f"Generated summary for project {project_name}")
```

## 5. 集成到现有架构

### 5.1 Model类扩展
```python
# 在pyahc/model/model.py中添加HDF5支持
class Model:
    # 现有代码...

    def save_to_project_hdf5(self, manager: ProjectHDF5Manager,
                           project_name: str, date: date) -> None:
        """保存模型到项目HDF5"""
        # 提取状态和参数
        states = self.extract_current_states()
        parameters = self.extract_parameters()

        # 运行模型获取结果
        result = self.run()

        # 保存到HDF5
        manager.save_daily_data(project_name, date, self, result, states, parameters)

    @classmethod
    def load_from_project_hdf5(cls, manager: ProjectHDF5Manager,
                             project_name: str, date: date) -> 'Model':
        """从项目HDF5加载模型"""
        daily_data = manager.load_daily_data(project_name, date)
        return daily_data['model']

    def extract_current_states(self) -> Dict[str, Any]:
        """提取当前模型状态"""
        states = {}

        # 提取土壤含水量
        if self.soilmoisture and hasattr(self.soilmoisture, 'thetai'):
            states['soil_moisture'] = np.array(self.soilmoisture.thetai)

        # 可以添加更多状态提取逻辑

        return states

    def extract_parameters(self) -> Dict[str, Any]:
        """提取模型参数"""
        parameters = {}

        # 提取相关参数
        # 这里需要根据具体的pyAHC结构来实现

        return parameters
```

### 5.2 Result类扩展
```python
# 在pyahc/model/result.py中添加状态提取方法
class Result:
    # 现有代码...

    def extract_states_for_hdf5(self) -> Dict[str, Any]:
        """为HDF5存储提取状态变量"""
        return StateExtractor.extract_all_states(self)

    def get_soil_moisture_profile(self) -> Optional[np.ndarray]:
        """获取土壤含水量剖面"""
        return StateExtractor.extract_soil_moisture(self)

    def get_crop_lai(self) -> Optional[float]:
        """获取作物LAI"""
        return StateExtractor.extract_crop_lai(self)

    def get_crop_biomass(self) -> Optional[float]:
        """获取作物生物量"""
        return StateExtractor.extract_crop_biomass(self)
```

### 5.3 组件级别的状态支持
```python
# 为关键组件添加状态序列化/反序列化方法
class SoilMoisture:
    def to_state_dict(self) -> Dict[str, Any]:
        """转换为状态字典"""
        return {
            'thetai': self.thetai if hasattr(self, 'thetai') else None,
            'swinco': self.swinco if hasattr(self, 'swinco') else None
        }

    @classmethod
    def from_state_dict(cls, state: Dict[str, Any]) -> 'SoilMoisture':
        """从状态字典创建"""
        return cls(
            thetai=state.get('thetai'),
            swinco=state.get('swinco', 0)
        )

# 为其他组件添加类似方法
class Crop:
    def to_state_dict(self) -> Dict[str, Any]:
        """转换为状态字典"""
        # 根据具体的作物组件结构实现
        return {}

    @classmethod
    def from_state_dict(cls, state: Dict[str, Any]) -> 'Crop':
        """从状态字典创建"""
        # 根据具体的作物组件结构实现
        return cls()
```

## 6. 使用示例

### 6.1 基本使用流程
```python
from pyahc.db.hdf5 import ProjectHDF5Manager, StateExtractor, StateInjector
from pyahc.model.model import Model
from datetime import date, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)

def main():
    """主函数 - 演示新HDF5结构的使用"""

    # 1. 配置项目参数
    config = {
        'hdf5_path': 'project.h5',
        'project_name': 'corn_001-2013',
        'start_date': date(2013, 5, 1),
        'end_date': date(2013, 5, 10),  # 短期测试
        'field_id': 'field_001',
        'crop_type': 'corn',
        'latitude': 45.75,
        'longitude': None,
        'altitude': 1039.3,
        'enable_assimilation': False,
        'stop_on_error': True
    }

    # 2. 运行项目模拟
    manager = run_project_simulation(config)

    # 3. 分析结果
    analyze_project_results(manager, config)

def analyze_project_results(manager: ProjectHDF5Manager, config: Dict[str, Any]):
    """分析项目结果"""

    project_name = config['project_name']
    start_date = config['start_date']
    end_date = config['end_date']

    print(f"\n=== 项目结果分析: {project_name} ===")
    print(f"模拟期间: {start_date} 到 {end_date}")

    with manager:
        project_group = manager._file[project_name]

        # 显示项目结构
        print(f"\n项目数据结构:")
        _print_project_structure(project_group, indent=0)

        # 分析汇总数据
        if 'summary' in project_group:
            summary_group = project_group['summary']

            if 'statistics' in summary_group:
                stats = summary_group['statistics']
                print(f"\n统计信息:")
                for key in stats.attrs.keys():
                    print(f"  {key}: {stats.attrs[key]:.3f}")

            if 'timeseries' in summary_group:
                ts_group = summary_group['timeseries']
                if 'lai_timeseries' in ts_group:
                    lai_data = ts_group['lai_timeseries'][:]
                    print(f"\nLAI时间序列:")
                    print(f"  长度: {len(lai_data)} 天")
                    print(f"  范围: {np.nanmin(lai_data):.3f} - {np.nanmax(lai_data):.3f}")

def _print_project_structure(group, indent=0):
    """递归打印项目结构"""
    prefix = "  " * indent

    for key in sorted(group.keys()):
        item = group[key]
        if isinstance(item, h5py.Group):
            print(f"{prefix}{key}/")
            if indent < 2:  # 限制深度
                _print_project_structure(item, indent + 1)
        else:
            shape_str = f" {item.shape}" if hasattr(item, 'shape') else ""
            dtype_str = f" {item.dtype}" if hasattr(item, 'dtype') else ""
            print(f"{prefix}{key}{shape_str}{dtype_str}")

if __name__ == "__main__":
    main()
```

### 6.2 高级使用示例
```python
def advanced_project_usage():
    """高级项目使用示例"""

    # 1. 多项目管理
    def manage_multiple_projects():
        """管理多个项目"""
        manager = ProjectHDF5Manager('multi_project.h5')

        projects = [
            'corn_001-2013', 'corn_002-2013', 'wheat_001-2014'
        ]

        with manager:
            # 列出所有项目
            existing_projects = list(manager._file.keys())
            print(f"现有项目: {existing_projects}")

            # 比较不同项目的结果
            for project in projects:
                if project in manager._file:
                    project_group = manager._file[project]
                    if 'summary/statistics' in project_group:
                        stats = project_group['summary/statistics']
                        max_lai = stats.attrs.get('lai_max', 0)
                        print(f"{project}: 最大LAI = {max_lai:.3f}")

    # 2. 状态变量追踪
    def track_state_evolution():
        """追踪状态变量演化"""
        manager = ProjectHDF5Manager('project.h5')
        project_name = 'corn_001-2013'

        # 获取特定变量的演化
        dates = []
        lai_values = []
        moisture_profiles = []

        start_date = date(2013, 5, 1)
        end_date = date(2013, 5, 31)

        current_date = start_date
        while current_date <= end_date:
            try:
                daily_data = manager.load_daily_data(project_name, current_date)
                states = daily_data['states']

                dates.append(current_date)
                lai_values.append(states.get('lai', np.nan))
                moisture_profiles.append(states.get('soil_moisture', np.full(30, np.nan)))

            except Exception:
                pass

            current_date += timedelta(days=1)

        # 分析演化趋势
        lai_array = np.array(lai_values)
        moisture_array = np.array(moisture_profiles)

        print(f"LAI演化: {np.nanmin(lai_array):.3f} -> {np.nanmax(lai_array):.3f}")
        print(f"平均土壤含水量: {np.nanmean(moisture_array):.3f}")

    # 3. 数据导出
    def export_project_data():
        """导出项目数据"""
        manager = ProjectHDF5Manager('project.h5')
        project_name = 'corn_001-2013'

        with manager:
            project_group = manager._file[project_name]

            # 导出时间序列数据
            if 'summary/timeseries' in project_group:
                ts_group = project_group['summary/timeseries']

                # 导出为CSV
                import pandas as pd

                data = {}
                for var_name in ts_group.keys():
                    dataset = ts_group[var_name]
                    data[var_name] = dataset[:]

                    # 获取日期信息
                    if 'dates' in dataset.attrs:
                        dates = [d.decode('utf-8') for d in dataset.attrs['dates']]
                        data['date'] = dates

                df = pd.DataFrame(data)
                df.to_csv(f'{project_name}_timeseries.csv', index=False)
                print(f"时间序列数据已导出到 {project_name}_timeseries.csv")

    # 执行高级功能
    manage_multiple_projects()
    track_state_evolution()
    export_project_data()
```

### 6.3 数据同化示例（预留）
```python
def data_assimilation_example():
    """数据同化使用示例（预留功能）"""

    manager = ProjectHDF5Manager('project.h5')
    project_name = 'corn_001-2013'

    # 模拟观测数据
    observations = {
        'lai': 2.5,  # 观测到的LAI值
        'biomass': 3500.0,  # 观测到的生物量
        'soil_moisture': np.array([0.25, 0.23, 0.21, 0.20])  # 观测到的土壤含水量
    }

    # 应用数据同化
    assimilation_date = date(2013, 6, 15)
    assimilate_observations_v2(manager, project_name, observations, assimilation_date)

    # 检查同化结果
    with manager:
        date_str = f"day_{assimilation_date.strftime('%m-%d')}"
        project_group = manager._file[project_name]
        day_group = project_group[date_str]
        sp_group = day_group['state-parameter_variables']

        print(f"同化后的变量:")
        for key in sp_group.keys():
            if key.startswith('assimilated_'):
                dataset = sp_group[key]
                print(f"  {key}: {dataset[()]}")
                print(f"    原始变量: {dataset.attrs['original_variable']}")
                print(f"    同化日期: {dataset.attrs['assimilation_date']}")

def visualization_example():
    """可视化示例"""
    import matplotlib.pyplot as plt

    manager = ProjectHDF5Manager('project.h5')
    project_name = 'corn_001-2013'

    with manager:
        project_group = manager._file[project_name]

        if 'summary/timeseries' in project_group:
            ts_group = project_group['summary/timeseries']

            # 创建图表
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))

            # LAI时间序列
            if 'lai_timeseries' in ts_group:
                lai_data = ts_group['lai_timeseries'][:]
                dates = [d.decode('utf-8') for d in ts_group['lai_timeseries'].attrs['dates']]
                axes[0, 0].plot(lai_data)
                axes[0, 0].set_title('LAI Evolution')
                axes[0, 0].set_ylabel('LAI (m²/m²)')

            # 生物量时间序列
            if 'biomass_timeseries' in ts_group:
                biomass_data = ts_group['biomass_timeseries'][:]
                axes[0, 1].plot(biomass_data)
                axes[0, 1].set_title('Biomass Evolution')
                axes[0, 1].set_ylabel('Biomass (kg/ha)')

            # 土壤含水量热图
            if 'moisture_timeseries' in ts_group:
                moisture_data = ts_group['moisture_timeseries'][:]
                im = axes[1, 0].imshow(moisture_data.T, aspect='auto', cmap='Blues')
                axes[1, 0].set_title('Soil Moisture Profile')
                axes[1, 0].set_ylabel('Soil Layer')
                axes[1, 0].set_xlabel('Time (days)')
                plt.colorbar(im, ax=axes[1, 0], label='Moisture Content')

            # 统计信息
            if 'statistics' in project_group['summary']:
                stats = project_group['summary']['statistics']
                stats_text = []
                for key in stats.attrs.keys():
                    stats_text.append(f"{key}: {stats.attrs[key]:.3f}")
                axes[1, 1].text(0.1, 0.9, '\n'.join(stats_text),
                               transform=axes[1, 1].transAxes,
                               verticalalignment='top')
                axes[1, 1].set_title('Statistics')
                axes[1, 1].axis('off')

            plt.tight_layout()
            plt.savefig(f'{project_name}_analysis.png', dpi=300, bbox_inches='tight')
            plt.show()
```

## 7. 实施计划

### 7.1 第一阶段：基础HDF5支持
- [ ] 实现ProjectHDF5Manager核心类
- [ ] 实现StateExtractor和StateInjector（适配新结构）
- [ ] 添加对象序列化/反序列化支持
- [ ] 集成到现有Model和Result类

### 7.2 第二阶段：完整工作流程
- [ ] 实现项目级日循环模拟工作流程
- [ ] 添加项目汇总和时间序列管理
- [ ] 实现数据验证和错误处理
- [ ] 编写完整的使用文档和示例

### 7.3 第三阶段：数据同化准备
- [ ] 设计数据同化接口（基于新结构）
- [ ] 实现观测数据处理和同化变量存储
- [ ] 预留集合模拟支持
- [ ] 性能优化和大规模测试

### 7.4 第四阶段：高级功能
- [ ] 多项目管理和比较分析
- [ ] 数据导出和可视化工具
- [ ] 并行计算支持
- [ ] 云存储适配

## 8. 技术优势

### 8.1 数据组织优势
1. **项目隔离**：不同地块和年份的数据完全分离，避免混淆
2. **日期结构化**：每日数据独立存储，便于随机访问和管理
3. **状态-参数分离**：清晰区分状态变量和参数变量，支持数据同化
4. **对象完整性**：直接存储Model和Result对象，保持数据完整性

### 8.2 性能优势
1. **高效存储**：HDF5二进制格式和压缩支持，显著节省空间
2. **快速访问**：层次化结构支持快速定位和读取特定日期数据
3. **批量操作**：支持批量读写和时间序列分析
4. **内存优化**：按需加载，支持大规模长期模拟

### 8.3 科学计算优势
1. **标准化格式**：HDF5是科学计算领域的标准格式
2. **元数据丰富**：支持详细的元数据存储和查询
3. **跨平台兼容**：支持多种编程语言和工具
4. **版本控制友好**：结构化数据便于版本管理

### 8.4 扩展性优势
1. **数据同化就绪**：预留完整的数据同化接口和存储结构
2. **多项目支持**：单文件支持多个项目，便于比较分析
3. **自定义变量**：灵活支持新的状态变量和参数变量
4. **集成友好**：不破坏现有架构，平滑集成



## 9. 总结与展望

### 9.1 方案总结

基于您建议的项目-日期分层结构，新的HDF5集成方案具有以下核心特点：

1. **清晰的数据组织**：project.h5 -> 项目组 -> 日期组 -> 输入/输出/状态-参数变量
2. **完整的对象存储**：直接序列化存储Model和Result对象，保持数据完整性
3. **灵活的状态管理**：状态变量和参数变量分离存储，支持数据同化
4. **项目级别汇总**：自动生成时间序列和统计信息，便于分析

### 9.2 实施建议

1. **优先实施**：先实现ProjectHDF5Manager核心类和基本的日循环工作流程
2. **渐进集成**：逐步集成到现有pyAHC架构中，保持向后兼容
3. **充分测试**：在小规模数据上充分测试后再应用到大规模模拟
4. **文档完善**：提供详细的使用文档和示例代码

### 9.3 未来发展

1. **数据同化算法**：基于新结构实现EnKF、3DVar等同化算法
2. **并行计算**：支持多项目并行模拟和分析
3. **云计算集成**：适配云存储和分布式计算环境
4. **机器学习**：结合深度学习进行模型校准和预测

这个更新的方案更好地满足了您的需求，为pyAHC项目的数据同化功能提供了科学、高效、可扩展的基础架构。


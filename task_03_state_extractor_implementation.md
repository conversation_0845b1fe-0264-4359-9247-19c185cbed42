# 任务03：StateExtractor状态提取器实现

## 任务概述
实现StateExtractor类，从pyAHC模型的Result对象中提取关键状态变量，包括土壤含水量、LAI、生物量、根深、地下水位等。这些状态变量将用于日循环模拟中的状态传递。

## 任务需求

### 核心目标
1. 从Result对象中准确提取各种状态变量
2. 支持多种输出格式（CSV、ASCII等）的数据解析
3. 实现健壮的数据验证和错误处理
4. 提供统一的状态变量提取接口
5. 确保提取数据的精度和可靠性

### 具体要求
- 支持土壤含水量剖面的完整提取
- 提取作物生长状态变量（LAI、生物量、根深）
- 提取水文状态变量（地下水位）
- 处理缺失数据和异常值
- 返回标准化的numpy数组或标量值

## 实现步骤

### 步骤1：实现核心提取框架

```python
"""状态变量提取器"""
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List
import logging
import re

class StateExtractor:
    """从模型结果中提取状态变量"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    @staticmethod
    def extract_all_states(result: 'Result') -> Dict[str, Any]:
        """提取所有状态变量
        
        Args:
            result: pyAHC模型结果对象
            
        Returns:
            包含所有状态变量的字典
        """
        states = {}
        
        try:
            # 提取土壤含水量
            soil_moisture = StateExtractor.extract_soil_moisture(result)
            if soil_moisture is not None:
                states['soil_moisture'] = soil_moisture
            
            # 提取LAI
            lai = StateExtractor.extract_crop_lai(result)
            if lai is not None:
                states['lai'] = lai
            
            # 提取生物量
            biomass = StateExtractor.extract_crop_biomass(result)
            if biomass is not None:
                states['biomass'] = biomass
            
            # 提取根深
            root_depth = StateExtractor.extract_root_depth(result)
            if root_depth is not None:
                states['root_depth'] = root_depth
            
            # 提取地下水位
            groundwater_level = StateExtractor.extract_groundwater_level(result)
            if groundwater_level is not None:
                states['groundwater_level'] = groundwater_level
            
            logging.info(f"Extracted {len(states)} state variables")
            return states
            
        except Exception as e:
            logging.error(f"Failed to extract states: {e}")
            return {}
```

### 步骤2：实现土壤含水量提取

```python
    @staticmethod
    def extract_soil_moisture(result: 'Result') -> Optional[np.ndarray]:
        """提取土壤含水量剖面
        
        Args:
            result: pyAHC模型结果对象
            
        Returns:
            土壤含水量数组，形状为(n_layers,)，单位为cm3/cm3
        """
        try:
            # 方法1：从CSV输出中提取
            moisture_data = StateExtractor._extract_from_csv(result, 'moisture')
            if moisture_data is not None:
                return moisture_data
            
            # 方法2：从ASCII输出中解析
            moisture_data = StateExtractor._extract_from_ascii(result, 'moisture')
            if moisture_data is not None:
                return moisture_data
            
            # 方法3：从其他可能的输出格式提取
            moisture_data = StateExtractor._extract_from_other_formats(result, 'moisture')
            if moisture_data is not None:
                return moisture_data
            
            logging.warning("Could not extract soil moisture from any output format")
            return None
            
        except Exception as e:
            logging.error(f"Failed to extract soil moisture: {e}")
            return None
    
    @staticmethod
    def _extract_from_csv(result: 'Result', variable_type: str) -> Optional[np.ndarray]:
        """从CSV输出中提取变量"""
        if not hasattr(result, 'csv') or result.csv is None:
            return None
        
        try:
            df = result.csv
            
            if variable_type == 'moisture':
                # 查找土壤含水量相关列
                moisture_cols = []
                for col in df.columns:
                    col_lower = col.lower()
                    if any(keyword in col_lower for keyword in ['moisture', 'theta', 'swc', 'water_content']):
                        moisture_cols.append(col)
                
                if moisture_cols:
                    # 取最后一行（模拟结束时的状态）
                    moisture_values = df[moisture_cols].iloc[-1].values
                    return np.array(moisture_values, dtype=np.float64)
            
            elif variable_type == 'lai':
                # 查找LAI相关列
                lai_cols = [col for col in df.columns 
                           if any(keyword in col.lower() for keyword in ['lai', 'leaf_area', 'leaf area'])]
                if lai_cols:
                    return float(df[lai_cols[0]].iloc[-1])
            
            elif variable_type == 'biomass':
                # 查找生物量相关列
                biomass_cols = [col for col in df.columns 
                               if any(keyword in col.lower() for keyword in ['biomass', 'dry_matter', 'drymatter', 'yield'])]
                if biomass_cols:
                    return float(df[biomass_cols[0]].iloc[-1])
            
            return None
            
        except Exception as e:
            logging.error(f"Failed to extract {variable_type} from CSV: {e}")
            return None
    
    @staticmethod
    def _extract_from_ascii(result: 'Result', variable_type: str) -> Optional[np.ndarray]:
        """从ASCII输出中提取变量"""
        if not hasattr(result, 'ascii') or not result.ascii:
            return None
        
        try:
            if variable_type == 'moisture':
                # 查找土壤含水量相关文件
                for file_ext, content in result.ascii.items():
                    if any(keyword in file_ext.lower() for keyword in ['sba', 'moisture', 'theta']):
                        return StateExtractor._parse_soil_moisture_from_sba(content)
            
            return None
            
        except Exception as e:
            logging.error(f"Failed to extract {variable_type} from ASCII: {e}")
            return None
    
    @staticmethod
    def _parse_soil_moisture_from_sba(content: str) -> Optional[np.ndarray]:
        """从SBA文件内容解析土壤含水量"""
        try:
            lines = content.strip().split('\n')
            moisture_values = []
            
            # 查找数据行（通常包含数值）
            for line in lines:
                line = line.strip()
                if not line or line.startswith('#') or line.startswith('!'):
                    continue
                
                # 尝试解析数值
                parts = line.split()
                if len(parts) >= 2:
                    try:
                        # 假设第二列是含水量值
                        moisture = float(parts[1])
                        if 0 <= moisture <= 1:  # 合理的含水量范围
                            moisture_values.append(moisture)
                    except ValueError:
                        continue
            
            if moisture_values:
                return np.array(moisture_values, dtype=np.float64)
            
            return None
            
        except Exception as e:
            logging.error(f"Failed to parse soil moisture from SBA: {e}")
            return None
    
    @staticmethod
    def _extract_from_other_formats(result: 'Result', variable_type: str) -> Optional[np.ndarray]:
        """从其他格式中提取变量"""
        # 预留接口，用于支持其他可能的输出格式
        return None
```

### 步骤3：实现作物状态变量提取

```python
    @staticmethod
    def extract_crop_lai(result: 'Result') -> Optional[float]:
        """提取作物叶面积指数
        
        Args:
            result: pyAHC模型结果对象
            
        Returns:
            LAI值，单位为m2/m2
        """
        try:
            # 从CSV提取
            lai_value = StateExtractor._extract_from_csv(result, 'lai')
            if lai_value is not None:
                return float(lai_value)
            
            # 从ASCII提取
            lai_value = StateExtractor._extract_lai_from_ascii(result)
            if lai_value is not None:
                return float(lai_value)
            
            logging.warning("Could not extract LAI from any output format")
            return None
            
        except Exception as e:
            logging.error(f"Failed to extract LAI: {e}")
            return None
    
    @staticmethod
    def extract_crop_biomass(result: 'Result') -> Optional[float]:
        """提取作物生物量
        
        Args:
            result: pyAHC模型结果对象
            
        Returns:
            生物量值，单位为kg/ha
        """
        try:
            # 从CSV提取
            biomass_value = StateExtractor._extract_from_csv(result, 'biomass')
            if biomass_value is not None:
                return float(biomass_value)
            
            # 从ASCII提取
            biomass_value = StateExtractor._extract_biomass_from_ascii(result)
            if biomass_value is not None:
                return float(biomass_value)
            
            logging.warning("Could not extract biomass from any output format")
            return None
            
        except Exception as e:
            logging.error(f"Failed to extract biomass: {e}")
            return None
    
    @staticmethod
    def extract_root_depth(result: 'Result') -> Optional[float]:
        """提取根深
        
        Args:
            result: pyAHC模型结果对象
            
        Returns:
            根深值，单位为cm
        """
        try:
            # 从CSV提取
            if hasattr(result, 'csv') and result.csv is not None:
                df = result.csv
                root_cols = [col for col in df.columns 
                           if any(keyword in col.lower() for keyword in ['root', 'rooting'])]
                if root_cols:
                    return float(df[root_cols[0]].iloc[-1])
            
            # 从ASCII提取
            root_value = StateExtractor._extract_root_from_ascii(result)
            if root_value is not None:
                return float(root_value)
            
            logging.warning("Could not extract root depth from any output format")
            return None
            
        except Exception as e:
            logging.error(f"Failed to extract root depth: {e}")
            return None
    
    @staticmethod
    def extract_groundwater_level(result: 'Result') -> Optional[float]:
        """提取地下水位
        
        Args:
            result: pyAHC模型结果对象
            
        Returns:
            地下水位值，单位为cm
        """
        try:
            # 从CSV提取
            if hasattr(result, 'csv') and result.csv is not None:
                df = result.csv
                gw_cols = [col for col in df.columns 
                          if any(keyword in col.lower() for keyword in ['groundwater', 'water_table', 'watertable'])]
                if gw_cols:
                    return float(df[gw_cols[0]].iloc[-1])
            
            # 从ASCII提取
            gw_value = StateExtractor._extract_groundwater_from_ascii(result)
            if gw_value is not None:
                return float(gw_value)
            
            logging.warning("Could not extract groundwater level from any output format")
            return None
            
        except Exception as e:
            logging.error(f"Failed to extract groundwater level: {e}")
            return None
```

### 步骤4：实现ASCII格式解析辅助方法

```python
    @staticmethod
    def _extract_lai_from_ascii(result: 'Result') -> Optional[float]:
        """从ASCII输出中提取LAI"""
        if not hasattr(result, 'ascii') or not result.ascii:
            return None
        
        try:
            for file_ext, content in result.ascii.items():
                if any(keyword in file_ext.lower() for keyword in ['crop', 'lai', 'canopy']):
                    # 解析LAI值
                    lai_value = StateExtractor._parse_numeric_from_ascii(content, ['lai', 'leaf area'])
                    if lai_value is not None:
                        return lai_value
            return None
        except Exception as e:
            logging.error(f"Failed to extract LAI from ASCII: {e}")
            return None
    
    @staticmethod
    def _extract_biomass_from_ascii(result: 'Result') -> Optional[float]:
        """从ASCII输出中提取生物量"""
        if not hasattr(result, 'ascii') or not result.ascii:
            return None
        
        try:
            for file_ext, content in result.ascii.items():
                if any(keyword in file_ext.lower() for keyword in ['crop', 'biomass', 'yield']):
                    # 解析生物量值
                    biomass_value = StateExtractor._parse_numeric_from_ascii(content, ['biomass', 'dry matter'])
                    if biomass_value is not None:
                        return biomass_value
            return None
        except Exception as e:
            logging.error(f"Failed to extract biomass from ASCII: {e}")
            return None
    
    @staticmethod
    def _extract_root_from_ascii(result: 'Result') -> Optional[float]:
        """从ASCII输出中提取根深"""
        if not hasattr(result, 'ascii') or not result.ascii:
            return None
        
        try:
            for file_ext, content in result.ascii.items():
                if any(keyword in file_ext.lower() for keyword in ['root', 'crop']):
                    # 解析根深值
                    root_value = StateExtractor._parse_numeric_from_ascii(content, ['root depth', 'rooting depth'])
                    if root_value is not None:
                        return root_value
            return None
        except Exception as e:
            logging.error(f"Failed to extract root depth from ASCII: {e}")
            return None
    
    @staticmethod
    def _extract_groundwater_from_ascii(result: 'Result') -> Optional[float]:
        """从ASCII输出中提取地下水位"""
        if not hasattr(result, 'ascii') or not result.ascii:
            return None
        
        try:
            for file_ext, content in result.ascii.items():
                if any(keyword in file_ext.lower() for keyword in ['water', 'hydro']):
                    # 解析地下水位值
                    gw_value = StateExtractor._parse_numeric_from_ascii(content, ['groundwater', 'water table'])
                    if gw_value is not None:
                        return gw_value
            return None
        except Exception as e:
            logging.error(f"Failed to extract groundwater level from ASCII: {e}")
            return None
    
    @staticmethod
    def _parse_numeric_from_ascii(content: str, keywords: List[str]) -> Optional[float]:
        """从ASCII内容中解析数值"""
        try:
            lines = content.strip().split('\n')
            
            for line in lines:
                line_lower = line.lower()
                
                # 检查是否包含关键词
                if any(keyword in line_lower for keyword in keywords):
                    # 提取数值
                    numbers = re.findall(r'[-+]?\d*\.?\d+(?:[eE][-+]?\d+)?', line)
                    if numbers:
                        # 返回最后一个数值（通常是最终值）
                        return float(numbers[-1])
            
            return None
            
        except Exception as e:
            logging.error(f"Failed to parse numeric from ASCII: {e}")
            return None
```

### 步骤5：实现数据验证和质量控制

```python
    @staticmethod
    def validate_extracted_states(states: Dict[str, Any]) -> Dict[str, Any]:
        """验证提取的状态变量
        
        Args:
            states: 提取的状态变量字典
            
        Returns:
            验证后的状态变量字典
        """
        validated_states = {}
        
        for var_name, value in states.items():
            try:
                validated_value = StateExtractor._validate_single_variable(var_name, value)
                if validated_value is not None:
                    validated_states[var_name] = validated_value
                else:
                    logging.warning(f"Variable {var_name} failed validation")
            except Exception as e:
                logging.error(f"Failed to validate variable {var_name}: {e}")
        
        return validated_states
    
    @staticmethod
    def _validate_single_variable(var_name: str, value: Any) -> Optional[Any]:
        """验证单个变量"""
        if value is None:
            return None
        
        try:
            if var_name == 'soil_moisture':
                if isinstance(value, np.ndarray):
                    # 检查含水量范围 [0, 1]
                    if np.all((value >= 0) & (value <= 1)):
                        return value
                    else:
                        logging.warning(f"Soil moisture values out of range [0,1]: {np.min(value)}-{np.max(value)}")
                        return np.clip(value, 0, 1)
                
            elif var_name == 'lai':
                if isinstance(value, (int, float)):
                    # 检查LAI范围 [0, 15]
                    if 0 <= value <= 15:
                        return float(value)
                    else:
                        logging.warning(f"LAI value out of range [0,15]: {value}")
                        return max(0, min(15, float(value)))
                
            elif var_name == 'biomass':
                if isinstance(value, (int, float)):
                    # 检查生物量范围 [0, 50000]
                    if 0 <= value <= 50000:
                        return float(value)
                    else:
                        logging.warning(f"Biomass value out of range [0,50000]: {value}")
                        return max(0, min(50000, float(value)))
                
            elif var_name == 'root_depth':
                if isinstance(value, (int, float)):
                    # 检查根深范围 [0, 500]
                    if 0 <= value <= 500:
                        return float(value)
                    else:
                        logging.warning(f"Root depth value out of range [0,500]: {value}")
                        return max(0, min(500, float(value)))
                
            elif var_name == 'groundwater_level':
                if isinstance(value, (int, float)):
                    # 检查地下水位范围 [-1000, 0]
                    if -1000 <= value <= 0:
                        return float(value)
                    else:
                        logging.warning(f"Groundwater level out of range [-1000,0]: {value}")
                        return max(-1000, min(0, float(value)))
            
            return value
            
        except Exception as e:
            logging.error(f"Failed to validate {var_name}: {e}")
            return None
```

## 可交付成果

### 主要交付物
1. **完整的StateExtractor类**：包含所有状态变量提取功能
2. **单元测试文件**：test_state_extractor.py
3. **测试数据集**：包含各种格式的模拟结果数据
4. **使用示例**：example_state_extraction.py

### 功能特性
- 支持多种输出格式的数据提取
- 健壮的数据验证和质量控制
- 详细的错误处理和日志记录
- 标准化的数据返回格式

## 验收标准

### 功能验收
1. **提取精度**：能够准确提取各种状态变量
2. **格式支持**：支持CSV和ASCII等主要输出格式
3. **数据验证**：能够检测和处理异常数据
4. **错误处理**：对各种异常情况有适当的处理

### 性能验收
1. **提取速度**：单次提取时间小于0.1秒
2. **内存使用**：大文件处理时内存使用合理
3. **准确性**：提取结果与原始数据误差小于1%

### 质量验收
1. **代码覆盖率**：单元测试覆盖率达到90%以上
2. **文档完整性**：所有方法有完整的文档字符串
3. **类型安全**：所有方法有正确的类型注解

## 依赖关系
- **前置依赖**：任务01（HDF5基础环境搭建）
- **后续任务**：任务04（StateInjector实现）、任务05（工作流程集成）


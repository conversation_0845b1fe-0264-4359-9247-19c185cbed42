"""HDF5项目数据管理器"""
import h5py
import numpy as np
import pickle
from pathlib import Path
from typing import Dict, Any, Union, Optional, List
from datetime import date, datetime
import logging


class ProjectHDF5Manager:
    """基于项目-日期结构的HDF5数据管理核心类"""

    def __init__(self, filepath: Union[str, Path], mode: str = 'a'):
        """初始化HDF5管理器

        Args:
            filepath: HDF5文件路径
            mode: 文件打开模式 ('r', 'w', 'a')
        """
        self.filepath = Path(filepath)
        self.mode = mode
        self._file = None
        self.logger = logging.getLogger(__name__)

        # 确保目录存在
        self.filepath.parent.mkdir(parents=True, exist_ok=True)

    def __enter__(self):
        """上下文管理器入口"""
        self.open()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
        if exc_type is not None:
            self.logger.error(f"Exception in HDF5 context: {exc_type.__name__}: {exc_val}")

    def open(self):
        """打开HDF5文件"""
        if self._file is None:
            try:
                self._file = h5py.File(self.filepath, self.mode)
                self.logger.info(f"Opened HDF5 file: {self.filepath}")
            except Exception as e:
                self.logger.error(f"Failed to open HDF5 file {self.filepath}: {e}")
                raise

    def close(self):
        """关闭HDF5文件"""
        if self._file is not None:
            try:
                self._file.close()
                self._file = None
                self.logger.info("Closed HDF5 file")
            except Exception as e:
                self.logger.error(f"Failed to close HDF5 file: {e}")

    @property
    def is_open(self) -> bool:
        """检查文件是否打开"""
        return self._file is not None and bool(self._file)

    def create_project(self, project_name: str, metadata: Dict[str, Any]) -> None:
        """创建新项目

        Args:
            project_name: 项目名称，如 'corn_001-2013'
            metadata: 项目元数据字典
        """
        self.open()

        if project_name in self._file:
            raise ValueError(f"Project {project_name} already exists")

        try:
            # 创建项目组
            project_group = self._file.create_group(project_name)

            # 创建元数据组
            metadata_group = project_group.create_group('metadata')

            # 保存元数据
            self._save_metadata(metadata_group, metadata)

            # 创建summary组结构
            summary_group = project_group.create_group('summary')
            summary_group.create_group('timeseries')
            summary_group.create_group('statistics')

            self.logger.info(f"Created project: {project_name}")

        except Exception as e:
            self.logger.error(f"Failed to create project {project_name}: {e}")
            raise

    def _save_metadata(self, metadata_group: h5py.Group, metadata: Dict[str, Any]) -> None:
        """保存元数据到HDF5组"""
        for key, value in metadata.items():
            if isinstance(value, dict):
                # 创建子组保存嵌套字典
                subgroup = metadata_group.create_group(key)
                for subkey, subvalue in value.items():
                    if isinstance(subvalue, (str, int, float, bool)):
                        subgroup.attrs[subkey] = subvalue
                    elif isinstance(subvalue, datetime):
                        subgroup.attrs[subkey] = subvalue.isoformat()
                    elif isinstance(subvalue, date):
                        subgroup.attrs[subkey] = subvalue.isoformat()
                    else:
                        subgroup.attrs[subkey] = str(subvalue)
            else:
                # 直接保存为属性
                if isinstance(value, (str, int, float, bool)):
                    metadata_group.attrs[key] = value
                elif isinstance(value, (datetime, date)):
                    metadata_group.attrs[key] = value.isoformat()
                else:
                    metadata_group.attrs[key] = str(value)

    def list_projects(self) -> List[str]:
        """列出所有项目"""
        self.open()
        return list(self._file.keys())

    def project_exists(self, project_name: str) -> bool:
        """检查项目是否存在"""
        self.open()
        return project_name in self._file

    def get_project_metadata(self, project_name: str) -> Dict[str, Any]:
        """获取项目元数据"""
        self.open()

        if project_name not in self._file:
            raise ValueError(f"Project {project_name} does not exist")

        project_group = self._file[project_name]
        if 'metadata' not in project_group:
            return {}

        return self._load_metadata(project_group['metadata'])

    def _load_metadata(self, metadata_group: h5py.Group) -> Dict[str, Any]:
        """从HDF5组加载元数据"""
        metadata = {}

        # 加载属性
        for key, value in metadata_group.attrs.items():
            metadata[key] = value

        # 加载子组
        for key in metadata_group.keys():
            subgroup = metadata_group[key]
            metadata[key] = dict(subgroup.attrs)

        return metadata

    def save_daily_data(self, project_name: str, date_obj: Union[date, datetime],
                       model: Any, result: Any,
                       states: Dict[str, Any], parameters: Dict[str, Any] = None) -> None:
        """保存日数据（模型、结果、状态、参数）

        Args:
            project_name: 项目名称
            date_obj: 模拟日期
            model: 模型对象
            result: 结果对象
            states: 状态变量字典
            parameters: 参数变量字典
        """
        self.open()

        if project_name not in self._file:
            raise ValueError(f"Project {project_name} does not exist")

        # 格式化日期字符串
        if isinstance(date_obj, datetime):
            date_obj = date_obj.date()
        date_str = f"day_{date_obj.strftime('%m-%d')}"

        try:
            project_group = self._file[project_name]

            # 创建或获取日期组
            if date_str in project_group:
                day_group = project_group[date_str]
            else:
                day_group = project_group.create_group(date_str)

            # 保存模型输入对象
            self._save_model_object(day_group, model)

            # 保存模型输出对象
            self._save_result_object(day_group, result)

            # 保存状态-参数变量
            self._save_state_parameter_variables(day_group, states, parameters)

            self.logger.info(f"Saved daily data for {project_name}/{date_str}")

        except Exception as e:
            self.logger.error(f"Failed to save daily data for {project_name}/{date_str}: {e}")
            raise

    def _save_model_object(self, day_group: h5py.Group, model: Any) -> None:
        """保存Model对象"""
        # 创建input组
        if 'input' not in day_group:
            input_group = day_group.create_group('input')
        else:
            input_group = day_group['input']

        # 序列化Model对象
        model_bytes = pickle.dumps(model)

        # 保存为数据集
        if 'model_object' in input_group:
            del input_group['model_object']

        # Create dataset with compression for large data
        # Convert bytes to numpy array for compression support
        if len(model_bytes) > 1024:  # Only compress if larger than 1KB
            byte_array = np.frombuffer(model_bytes, dtype=np.uint8)
            dataset = input_group.create_dataset(
                'model_object',
                data=byte_array,
                compression='gzip'
            )
        else:
            dataset = input_group.create_dataset(
                'model_object',
                data=np.void(model_bytes)
            )
        dataset.attrs['type'] = 'pickled_model'
        dataset.attrs['class'] = 'pyahc.model.model.Model'
        dataset.attrs['timestamp'] = datetime.now().isoformat()

    def _save_result_object(self, day_group: h5py.Group, result: Any) -> None:
        """保存Result对象"""
        # 创建output组
        if 'output' not in day_group:
            output_group = day_group.create_group('output')
        else:
            output_group = day_group['output']

        # 序列化Result对象
        result_bytes = pickle.dumps(result)

        # 保存为数据集
        if 'result_object' in output_group:
            del output_group['result_object']

        # Create dataset with compression for large data
        # Convert bytes to numpy array for compression support
        if len(result_bytes) > 1024:  # Only compress if larger than 1KB
            byte_array = np.frombuffer(result_bytes, dtype=np.uint8)
            dataset = output_group.create_dataset(
                'result_object',
                data=byte_array,
                compression='gzip'
            )
        else:
            dataset = output_group.create_dataset(
                'result_object',
                data=np.void(result_bytes)
            )
        dataset.attrs['type'] = 'pickled_result'
        dataset.attrs['class'] = 'pyahc.model.result.Result'
        dataset.attrs['timestamp'] = datetime.now().isoformat()

    def _save_state_parameter_variables(self, day_group: h5py.Group,
                                      states: Dict[str, Any],
                                      parameters: Dict[str, Any] = None) -> None:
        """保存状态-参数变量"""
        # 创建state-parameter_variables组
        if 'state-parameter_variables' not in day_group:
            sp_group = day_group.create_group('state-parameter_variables')
        else:
            sp_group = day_group['state-parameter_variables']

        # 保存状态变量
        for var_name, value in states.items():
            self._save_variable(sp_group, var_name, value, 'state_variable')

        # 保存参数变量
        if parameters:
            for param_name, value in parameters.items():
                param_key = f"param_{param_name}"
                self._save_variable(sp_group, param_key, value, 'parameter_variable')

    def _save_variable(self, group: h5py.Group, var_name: str, value: Any, var_type: str) -> None:
        """保存单个变量"""
        if var_name in group:
            del group[var_name]

        if isinstance(value, np.ndarray):
            # Only use compression for arrays with more than one element
            if value.size > 1:
                dataset = group.create_dataset(var_name, data=value, compression='gzip')
            else:
                dataset = group.create_dataset(var_name, data=value)
        elif isinstance(value, (list, tuple)):
            arr_value = np.array(value)
            if arr_value.size > 1:
                dataset = group.create_dataset(var_name, data=arr_value, compression='gzip')
            else:
                dataset = group.create_dataset(var_name, data=arr_value)
        else:
            # Scalar values don't support compression
            dataset = group.create_dataset(var_name, data=value)

        dataset.attrs['type'] = var_type
        dataset.attrs['units'] = self._get_variable_units(var_name)
        dataset.attrs['timestamp'] = datetime.now().isoformat()

    def _get_variable_units(self, var_name: str) -> str:
        """获取变量单位"""
        from .utils import get_variable_units
        return get_variable_units(var_name)

    def load_daily_data(self, project_name: str, date_obj: Union[date, datetime]) -> Dict[str, Any]:
        """加载日数据

        Args:
            project_name: 项目名称
            date_obj: 模拟日期

        Returns:
            包含model、result、states、parameters的字典
        """
        self.open()

        if isinstance(date_obj, datetime):
            date_obj = date_obj.date()
        date_str = f"day_{date_obj.strftime('%m-%d')}"

        if project_name not in self._file:
            raise ValueError(f"Project {project_name} does not exist")

        project_group = self._file[project_name]
        if date_str not in project_group:
            raise ValueError(f"Date {date_str} not found in project {project_name}")

        try:
            day_group = project_group[date_str]

            # 加载各种数据
            data = {
                'model': self._load_model_object(day_group),
                'result': self._load_result_object(day_group),
                'states': self._load_state_variables(day_group),
                'parameters': self._load_parameter_variables(day_group)
            }

            self.logger.info(f"Loaded daily data for {project_name}/{date_str}")
            return data

        except Exception as e:
            self.logger.error(f"Failed to load daily data for {project_name}/{date_str}: {e}")
            raise

    def _load_model_object(self, day_group: h5py.Group) -> Optional[Any]:
        """加载Model对象"""
        if 'input' not in day_group or 'model_object' not in day_group['input']:
            return None

        try:
            dataset = day_group['input']['model_object']
            data = dataset[()]

            # Handle both compressed (uint8 array) and uncompressed (void) formats
            if isinstance(data, np.ndarray) and data.dtype == np.uint8:
                # Compressed format - convert back to bytes
                model_bytes = data.tobytes()
            else:
                # Uncompressed format - direct conversion
                model_bytes = data.tobytes()

            model = pickle.loads(model_bytes)
            return model
        except Exception as e:
            self.logger.error(f"Failed to load model object: {e}")
            return None

    def _load_result_object(self, day_group: h5py.Group) -> Optional[Any]:
        """加载Result对象"""
        if 'output' not in day_group or 'result_object' not in day_group['output']:
            return None

        try:
            dataset = day_group['output']['result_object']
            data = dataset[()]

            # Handle both compressed (uint8 array) and uncompressed (void) formats
            if isinstance(data, np.ndarray) and data.dtype == np.uint8:
                # Compressed format - convert back to bytes
                result_bytes = data.tobytes()
            else:
                # Uncompressed format - direct conversion
                result_bytes = data.tobytes()

            result = pickle.loads(result_bytes)
            return result
        except Exception as e:
            self.logger.error(f"Failed to load result object: {e}")
            return None

    def _load_state_variables(self, day_group: h5py.Group) -> Dict[str, Any]:
        """加载状态变量"""
        states = {}

        if 'state-parameter_variables' not in day_group:
            return states

        sp_group = day_group['state-parameter_variables']

        for key in sp_group.keys():
            dataset = sp_group[key]
            if dataset.attrs.get('type') == 'state_variable':
                states[key] = dataset[()]

        return states

    def _load_parameter_variables(self, day_group: h5py.Group) -> Dict[str, Any]:
        """加载参数变量"""
        parameters = {}

        if 'state-parameter_variables' not in day_group:
            return parameters

        sp_group = day_group['state-parameter_variables']

        for key in sp_group.keys():
            if key.startswith('param_'):
                dataset = sp_group[key]
                if dataset.attrs.get('type') == 'parameter_variable':
                    param_name = key[6:]  # 移除'param_'前缀
                    parameters[param_name] = dataset[()]

        return parameters

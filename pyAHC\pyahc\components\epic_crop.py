"""EPIC作物生长模型组件

此模块定义了用于处理AHC模型中EPIC作物生长模型输入文件(.CRE)的组件。
重构版本支持外部参数配置和条件验证。
"""

from __future__ import annotations

from pathlib import Path as _Path
from typing import Optional as _Optional, Dict as _Dict, Any as _Any, Literal as _Literal

from pydantic import Field as _Field, model_validator as _model_validator
from pandas import DataFrame as _DataFrame

from pyahc.core.configurable import ConfigurableComponent as _ConfigurableComponent
from pyahc.core.fields import PositiveFloat as _PositiveFloat, UnitFloat as _UnitFloat, TemperatureFloat as _TemperatureFloat
from pyahc.utils.mixins import FileMixin as _FileMixin, YAMLValidatorMixin as _YAMLValidatorMixin

# 常量定义
_DVS_FORMAT_SPECIAL = {0.5: "0.50", 0.8: "0.80"}
_DEFAULT_CFTB_VALUES = [
    "   0.00       1.2      ",
    "   1.00       1.2      "
]
_DEFAULT_CHTB_VALUES = [
    "   0.00       1.00     ",
    "   0.14       30.00    ",
    "   0.29       60.00    ",
    "   0.38       90.10    ",
    "   0.46       160.8    ",
    "   0.52       180.0    ",
    "   0.60       200.0    ",
    "   0.73       200.0    ",
    "   0.86       200.0    ",
    "   1.00       200.0    "
]
_DEFAULT_PDMXCTB_VALUES = [
    "   0.0        5.0      ",
    "   0.2        5.0      ",
    "   0.2        3.0      ",
    "   0.4        3.0      ",
    "   0.4        2.0      ",
    "   0.6        2.0      ",
    "   0.6        4.0      ",
    "   0.8        4.0      ",
    "   0.8        2.0      ",
    "   1.0        2.0      "
]


class EpicCrop(_ConfigurableComponent, _YAMLValidatorMixin, _FileMixin):
    """EPIC作物组件

    处理.CRE文件的生成和序列化，包含作物生长的各种参数设置。
    支持外部参数配置、条件验证和温度参数合理性验证。
    """

    _extension = "CRE"

    # Section 1: 作物因子或作物高度（外部化配置）
    swcf: _Literal[1, 2, 3] = _Field(description="作物因子或作物高度选择")
    cftb: _Optional[_DataFrame] = _Field(default=None, description="作物因子表")
    cfini: _Optional[float] = _Field(default=None, ge=0.0, description="初始作物高度")
    chtb: _Optional[_DataFrame] = _Field(default=None, description="作物高度表")

    # Section 2: 表面阻力参数（外部化配置）
    swrsc: _Literal[1, 2] = _Field(description="表面阻力参数选择")
    rsc: _PositiveFloat = _Field(description="最小冠层阻力")
    rsctb: _Optional[_DataFrame] = _Field(default=None, description="可变阻力表")
    frgmax: _UnitFloat = _Field(description="最大气孔导度分数")
    vpdfr: _PositiveFloat = _Field(description="蒸汽压差")
    
    # Section 3: Root water uptake compensation
    swcompen: _Literal[0, 1] = _Field(default=0, description="根系吸水补偿开关")
    omgs: _Optional[_UnitFloat] = _Field(default=0.8, description="吸水补偿容忍值")

    # Section 4: Root water uptake: water stress
    swaquc: _Literal[0, 1] = _Field(default=0, description="水生或半水生作物选择")
    swrootw: _Literal[1, 2] = _Field(default=1, description="根系吸水模型选择")
    # Feddes model parameters
    hlim1: float = _Field(default=-1, description="高压力头无吸水")
    hlim2u: float = _Field(default=-15.0, description="顶层最适吸水开始压力头")
    hlim2l: float = _Field(default=-15.0, description="底层最适吸水开始压力头")
    hlim3h: float = _Field(default=-400.0, description="高蒸腾时吸水减少开始压力头")
    hlim3l: float = _Field(default=-600.0, description="低蒸腾时吸水减少开始压力头")
    hlim4: float = _Field(default=-8000.0, description="低压力头无吸水")
    adcrh: _UnitFloat = _Field(default=0.5, description="高大气需求水平")
    adcrl: _UnitFloat = _Field(default=0.1, description="低大气需求水平")
    # VG model parameters
    p1: _Optional[_PositiveFloat] = _Field(default=2.5, description="VG模型参数P1")
    h50: _Optional[float] = _Field(default=-8000, description="VG模型参数H50")
    
    # Section 5: Root water uptake: salt stress
    swroots: _Literal[1, 2, 3] = _Field(default=3, description="盐胁迫模型选择")
    # Maas model parameters
    ecmax: _Optional[_PositiveFloat] = _Field(default=3.5, description="盐胁迫开始EC值")
    ecslop: _Optional[_PositiveFloat] = _Field(default=8.0, description="根系吸水下降率")
    # VG/Dirksen model parameters
    h50c: float = _Field(default=-6000, description="渗透压力头50%减少值")
    ci: float = _Field(default=-537, description="浓度转换系数")
    p2v: _Optional[_PositiveFloat] = _Field(default=3.0, description="VG模型指数")
    p2d: _PositiveFloat = _Field(default=1.86, description="Dirksen模型指数")
    hcritx: float = _Field(default=-2000, description="无盐胁迫阈值")

    # Section 6: Interception of rainfall
    cofab: _UnitFloat = _Field(default=0.25, description="降雨截留系数")
    fwcc: _UnitFloat = _Field(default=0.60, description="枯萎冠层覆盖对蒸发的影响系数")

    # Section 7: Root density distribution and root growth
    rdctb: _DataFrame = _Field(description="相对根密度分布表")
    frtb: _DataFrame = _Field(description="根系干物质分配表")
    swrtcr: _Literal[0, 1] = _Field(default=0, description="根系CO2通量计算开关")
    ratres: _PositiveFloat = _Field(default=26.18, description="平均呼吸速率")
    q12ptb: _Optional[_DataFrame] = _Field(default=None, description="根系呼吸温度敏感性表")
    
    # Section 8: Light extinction
    kgsrtb: _DataFrame = _Field(description="全球太阳辐射消光系数表")
    kpartb: _DataFrame = _Field(description="光合有效辐射消光系数表")

    # Section 9: EPIC crop growth module parameters（温度参数 - 移除硬编码）
    tba: _Optional[_TemperatureFloat] = _Field(default=None, description="基础温度")
    topt: _Optional[_TemperatureFloat] = _Field(default=None, description="最适温度")
    ruep: _Optional[_PositiveFloat] = _Field(default=None, description="辐射利用效率")
    dlap1: _Optional[_PositiveFloat] = _Field(default=None, description="叶面积参数1")
    dlap2: _Optional[_PositiveFloat] = _Field(default=None, description="叶面积参数2")
    fdlai: _UnitFloat = _Field(default=0.8, description="LAI下降生长季分数")
    rlad: _UnitFloat = _Field(default=0.8, description="LAI下降率参数")
    chmax: _Optional[_PositiveFloat] = _Field(default=None, description="最大冠层高度")
    lmax: _Optional[_PositiveFloat] = _Field(default=None, description="最大叶面积指数")
    rdmax: _Optional[_PositiveFloat] = _Field(default=None, description="最大根深度")
    bn1: _UnitFloat = _Field(default=0.044, description="幼苗期最适氮需求")
    bn2: _UnitFloat = _Field(default=0.015, description="中期生长最适氮需求")
    bn3: _UnitFloat = _Field(default=0.010, description="成熟期最适氮需求")
    hipot: _UnitFloat = _Field(default=0.52, description="潜在收获指数")
    himin: _UnitFloat = _Field(default=0.40, description="水胁迫下收获指数下限")
    phu: _Optional[_PositiveFloat] = _Field(default=None, description="潜在热单位")
    druevp: _PositiveFloat = _Field(default=8.0, description="RUE随VPD增加的下降率")
    
    # Section 10: Active root nitrogen uptake
    swactn: _Literal[0, 1] = _Field(default=0, description="主动根系氮吸收开关")
    pwrts: _Optional[int] = _Field(default=30000, description="每公顷植株数")
    cnup1: _Optional[_PositiveFloat] = _Field(default=5.5, description="最大氮吸收率")
    cnup2: _Optional[_PositiveFloat] = _Field(default=1.5, description="氮吸收效率系数")

    # Section 11: Maximum ponding depth control
    swpda: _Literal[0, 1] = _Field(default=0, description="地表积水深度调节开关")
    pdmxctb: _Optional[_DataFrame] = _Field(default=None, description="调节积水深度表")

    # Section 12: CO2 effects
    swco2: _Literal[0, 1] = _Field(default=1, description="CO2水平对作物影响开关")
    co2: _PositiveFloat = _Field(default=330.0, description="CO2浓度")
    pdlc: _UnitFloat = _Field(default=0.40, description="660ppm时叶导度下降百分比")
    pilm: _UnitFloat = _Field(default=0.40, description="660ppm时最大LAI增加百分比")
    ruecp2: _PositiveFloat = _Field(default=660.50, description="CO2对RUE影响曲线第二点")

    # Section 13: Initial values
    huiini: _UnitFloat = _Field(default=0.0, description="移栽作物初始HUI")
    laiini: float = _Field(default=0.0, ge=0.0, description="初始叶面积指数")
    rdini: _PositiveFloat = _Field(default=0.01, description="初始根深")
    bioini: float = _Field(default=0.0, ge=0.0, description="初始总作物干生物量")

    def __init__(self, **data):
        super().__init__(**data)
        self._load_default_data()
        self._setup_default_parameters()

    def _load_default_data(self):
        """一次性加载所有默认数据"""
        try:
            from pyahc.data.maize_default import DEFAULT_RSCTB, DEFAULT_Q12PTB
            self._default_rsctb = DEFAULT_RSCTB
            self._default_q12ptb = DEFAULT_Q12PTB
        except ImportError:
            # 如果数据文件不存在，使用None，在model_string中会使用硬编码默认值
            self._default_rsctb = None
            self._default_q12ptb = None

    def _setup_default_parameters(self):
        """设置默认参数（可被外部配置覆盖）"""
        try:
            from pyahc.data.maize_default import MAIZE_DEFAULT_PARAMS
            # 使用外部数据文件中的默认参数
            for param, default_value in MAIZE_DEFAULT_PARAMS.items():
                if hasattr(self, param) and getattr(self, param) is None:
                    self.set_param_default(param, default_value)
        except ImportError:
            # 如果数据文件不存在，使用硬编码的默认参数
            defaults = {
                'tba': 9.0,
                'topt': 25.0,
                'ruep': 47.0,
                'dlap1': 15.03,
                'dlap2': 60.95,
                'chmax': 265.0,
                'lmax': 5.0,
                'rdmax': 90.0,
                'phu': 1400.0
            }
            for param, default_value in defaults.items():
                if getattr(self, param) is None:
                    self.set_param_default(param, default_value)

    def _format_dvs_value(self, dvs_val: float) -> str:
        """格式化DVS值，处理特殊格式要求"""
        return _DVS_FORMAT_SPECIAL.get(dvs_val, f"{dvs_val}")

    @_model_validator(mode="after")
    def validate_crop_factor_parameters(self):
        """验证作物因子参数"""
        if not self._validation:
            return self

        if self.swcf == 1 and self.cftb is None:
            raise ValueError("swcf=1 requires cftb (crop factor table)")

        if self.swcf == 2:
            if self.chtb is None or self.cfini is None:
                raise ValueError("swcf=2 requires both chtb and cfini")

        if self.swcf == 3 and self.cfini is None:
            raise ValueError("swcf=3 requires cfini")

        return self

    @_model_validator(mode="after")
    def validate_surface_resistance_parameters(self):
        """验证表面阻力参数"""
        if not self._validation:
            return self

        if self.swrsc == 1:
            required = ['rsc', 'frgmax', 'vpdfr']
            missing = []
            for param in required:
                value = self.get_param_value(param)
                if value is None:
                    missing.append(param)
            if missing:
                raise ValueError(f"swrsc=1 requires: {missing}")

        if self.swrsc == 2:
            if self.rsctb is None:
                raise ValueError("swrsc=2 requires rsctb (variable resistance table)")

        return self

    @_model_validator(mode="after")
    def validate_temperature_parameters(self):
        """验证温度参数的合理性"""
        if not self._validation:
            return self

        tba = self.get_param_value('tba')
        topt = self.get_param_value('topt')

        if tba is not None and topt is not None:
            if not (tba < topt):
                raise ValueError(
                    f"Base temperature must be less than optimal temperature, "
                    f"got tba={tba}, topt={topt}"
                )

        return self

    @classmethod
    def from_crop_database(cls, crop_name: str, config_overrides: _Optional[_Dict[str, _Any]] = None) -> "EpicCrop":
        """从作物数据库创建实例"""
        try:
            from pyahc.data.epic_crop import get_crop_parameters
            base_params = get_crop_parameters(crop_name)
        except (ImportError, ModuleNotFoundError):
            # 如果数据库不存在，使用默认参数
            base_params = {}
        except Exception:
            # 其他错误也使用默认参数
            base_params = {}

        if config_overrides:
            base_params.update(config_overrides)

        return cls.from_external_config(base_params)

    def model_string(self) -> str:
        """生成.CRE文件的字符串表示"""
        lines = []

        # 文件头
        lines.extend([
            "*",
            "* Filename: Maize.CRE",
            "* Contents: AHC v2.0 - Data for EPIC crop growth model",
            "***********************************************************************************************",
            "* Section 1: Crop factor or crop height",
            "*",
            f"  SWCF = {self.swcf} ! choice between crop factor [=1] or crop height [=2], dev. is only related to Temp.",
            "*",
            "* If SWCF = 1, list crop factor [0.5..1.5, R],   as function of dev. stage [0..1 -,R]:",
            "* If SWCF = 2, using crop height which is calculated by AHC program automatically, ",
            "*              but need specify the first row for initial height, e.g. 0.00  1.0",
            "* If SWCF = 3, list crop height [0..1000 cm, R], as function of dev. stage [0..1 -,R]:",
            "*",
            "* If SWCF = 1, list crop factor  [0.5..1.5, R],   as function of dev. stage [0..2 -,R]:",
            "*   DVS    CF[-] (maximum 36 records)",
            "  CFTB = "
        ])
        
        # CFTB表
        if self.cftb is not None and not self.cftb.empty:
            for _, row in self.cftb.iterrows():
                lines.append(f"   {row.iloc[0]:<10} {row.iloc[1]:<10}")
        else:
            lines.extend(_DEFAULT_CFTB_VALUES)
        lines.append("* End of Table")
        
        lines.extend([
            "*",
            "* If SWCF = 2, specify initial crop height  ",
            f"  CFINI = {self.get_param_value('cfini', self.cfini)} ! initial crop height, [0..1000cm, R]",
            "*",
            "* If SWCF = 3, list crop height [0..1000 cm, R], as function of dev. stage [0..2 -,R]:",
            "*   DVS    CH [cm]  (maximum 36 records)",
            "  CHTB = "
        ])
        
        # CHTB表
        if self.chtb is not None and not self.chtb.empty:
            for _, row in self.chtb.iterrows():
                lines.append(f"   {row.iloc[0]:<10} {row.iloc[1]:<10}")
        else:
            lines.extend(_DEFAULT_CHTB_VALUES)
        lines.append("* End of Table")
        lines.append("***********************************************************************************************")
        
        # Section 2: Surface resistance parameters
        lines.extend([
            "",
            "***********************************************************************************************",
            "* Section 2: surface resistance parameters",
            "*",
            f"  SWRSC = {self.swrsc} ! choice between RSC [1=constant] or [2=variable as DVS] ",
            "* If SWRSC = 1, only specify RSC value and use this RSC as constant value in whole simulation.",
            f"  RSC = {self.get_param_value('rsc', self.rsc)} ! Minimum canopy resistance [0..1000 s/m, R]  ",
            "* If SWRSC = 2, using varaible RSC as DVS specified as follows:  ",
            "  RSCTB =   "
        ])
        
        # RSCTB表
        if self.rsctb is not None and not self.rsctb.empty:
            for _, row in self.rsctb.iterrows():
                dvs_str = self._format_dvs_value(row.iloc[0])
                lines.append(f"   {dvs_str:<10} {row.iloc[1]:<10.1f}")
        else:
            # 使用预加载的默认RSCTB表
            if self._default_rsctb is not None:
                for _, row in self._default_rsctb.iterrows():
                    dvs_str = self._format_dvs_value(row.iloc[0])
                    lines.append(f"   {dvs_str:<10} {row.iloc[1]:<10.1f}")
            else:
                # 如果数据文件不可用，使用硬编码默认值
                lines.extend([
                    "   0.0        80.0     ",
                    "   0.50       80.0     ",
                    "   0.51       80.0     ",
                    "   0.80       80.0     ",
                    "   0.81       150.0    ",
                    "   1.0        150.0    "
                ])
        
        lines.extend([
            "* Considering the effect of VPD on RSC",
            f"  frgmax = {self.get_param_value('frgmax', self.frgmax)} ! fraction of maximum stomatal conductance that is achieved at the vapor ",
            "*                       pressure deficit defined by VPDFR [0..1 -, R] frgmax=1.0时不考虑vpd ",
            f"  vpdfr  = {self.get_param_value('vpdfr', self.vpdfr):.2f} ! vapor pressure deficit at which FRGMAX is valid [0..10 kpa, R]",
            "***********************************************************************************************"
        ])
        
        # Section 3: Root water uptake compensation
        lines.extend([
            "",
            "***********************************************************************************************",
            "* Section 3: root water uptake compensation",
            "*",
            f"  swcompen = {self.swcompen} ! Switch, root water uptake compensation [Y=1, N=0]",
            "* If swcompen = 1, Specify omgs value:  可跳过",
            f"  omgs = {self.omgs} ! Omega_s, tolerance value for water uptake compensation (-)[0-1, R] ",
            "***********************************************************************************************"
        ])
        
        # Section 4: Root water uptake: water stress
        lines.extend([
            "",
            "***********************************************************************************************",
            "* Section 4: root water uptake: water stress",
            "*",
            f"  SWAQUC ={self.swaquc}! Choise, if crop is aquatic or semi-aquatic crop (e.g. rice or taro) [Y=1, N=0]",
            "*",
            f"  SWROOTW = {self.swrootw} ! Choise, choose different models for calc. root water uptake ",
            "*                 = 1, use Feddes(1978) model for water stress",
            "*                 = 2, use VG(1987) model for water stress       ",
            "* If SWROOTW = 1, list the below: Feddes model parameters for water stress",
            f"  HLIM1  = {int(self.hlim1) if self.hlim1 == int(self.hlim1) else self.hlim1} ! No water extraction at higher pressure heads, [-100..100 cm, R]",
            f"  HLIM2U = {self.hlim2u} ! h below which optimum water extr. starts for top layer, [-1000..100 cm, R]",
            f"  HLIM2L = {self.hlim2l} ! h below which optimum water extr. starts for sub layer, [-1000..100 cm, R]",
            f"  HLIM3H = {self.hlim3h} ! h below which water uptake red. starts at high Tpot, [-10000..100 cm, R]",
            f"  HLIM3L = {self.hlim3l} ! h below which water uptake red. starts at low Tpot, [-10000..100 cm, R]",
            f"  HLIM4  = {self.hlim4} ! No water extraction at lower pressure heads, [-16000..100 cm, R] ",
            f"  ADCRH  = {self.adcrh} ! Level of high atmospheric demand, [0..5 cm/d, R]",
            f"  ADCRL  = {self.adcrl} ! Level of low atmospheric demand,  [0..5 cm/d, R]",
            "",
            "* If SWROOTW = 2, list the below: VG model parameters for water stress",
            f"  P1     = {self.p1}  ! ",
            f"  H50    = {self.h50}  ! ",
            "************************************************************************************************"
        ])
        
        # Section 5: Root water uptake: salt stress
        lines.extend([
            "",
            "************************************************************************************************",
            "* Section 5: root water uptake: salt stress ",
            "*",
            f"  SWROOTS = {self.swroots}! Choise, choose different models for calc. root water uptake ",
            "*                 = 1, use maas (1990) model for salt stress",
            "*                 = 2, use VG (1987)model for salt stress",
            "*                 = 3, use Dirksen (1987) model for salt stress",
            "* If SWROOTS=1, list maas(1990) model parameters for salt stress  "
        ])
        # 始终输出ECMAX和ECSLOP参数
        lines.extend([
            f"  ECMAX  = {self.ecmax} ! ECsat level at which salt stress starts, [0..20 dS/m, R] ",
            f"  ECSLOP = {self.ecslop} ! Decline of rootwater uptake above E_CMAX [0..40 %/dS/m, R]"
        ])
        lines.extend([
            "*",
            "* If SWROOTS = 2 or 3, list VG or Dirksen(1987) model parameters for salt stress",
            f"  H50C   = {self.h50c} ! Value of the osmotic head at which the root water uptake is reduced by 50%",
            f"  CI     = {self.ci} ! Coefficient for converting solution concentration (g/L) to osmotic head (cm)",
            "* If SWROOTS = 2, list other VG (1987) model parameters",
            f"  P2V    = {self.p2v} ! Exponent in S-shaped (VG) root water uptake salinity stress response function",
            "* If SWROOTS = 3, list other Dirksen(1987) model parameters",
            f"  P2D    = {self.p2d} ! Exponent in Dirksen root water uptake salinity stress response function",
            f"  hcritx = {self.hcritx} ! Threshold value of osmotic head above which no salinity stress happens",
            "***********************************************************************************************"
        ])
        
        # Section 6: Interception of rainfall
        lines.extend([
            "",
            "***********************************************************************************************",
            "* Section 6: Interception of rainfall  and effects of withered canopy cover on Ep",
            "*",
            f"  COFAB  = {self.cofab} ! Interception coefficient Von Hoyningen-Hune and Braden, [0..1 cm, R]",
            f"  FWCC   = {self.fwcc}     ! Coefficient for sheltering effect of withered canopy cover on Ep [0..1.0, R]",
            "***********************************************************************************************"
        ])
        
        # Section 7: Root density distribution and root growth
        lines.extend([
            "",
            "***********************************************************************************************",
            "* Section 7: Root density distribution and root growth ",
            "*",
            "* List relative root density [0..1 -, R], as function of rel. rooting depth [0..1 -, R]:",
            "*    RD     RDC   (maximum 11 records)",
            "  RDCTB = "
        ])
        
        # RDCTB表
        for _, row in self.rdctb.iterrows():
            lines.append(f"   {row.iloc[0]:<10} {row.iloc[1]:<10}")
        lines.append("* End of table")
        
        lines.extend([
            "*",
            "* List fraction of total dry matter increase partitioned to the roots [kg/kg, R]",
            "* as function of development stage [0..1 -, R]",
            "*          DVS     FR    (maximum 15 records)",
            "  FRTB = "
        ])
        
        # FRTB表
        for _, row in self.frtb.iterrows():
            lines.append(f"   {row.iloc[0]:<10} {row.iloc[1]:<10}")
        lines.append("* End of table")
        
        lines.extend([
            "*",
            f"  SWRTCR = {self.swrtcr} ! If calculating root CO2 flux from respiration [Y=1, N=0]",
            "* If SWRTCR = 1, specify below values:",
            f"  RATRES = {self.ratres}! the average rate of respiration (mg CO2-C/g dry matter/d) [0..100.0 -, R]",
            "* List temperature sensitivity parameter of root respiration as function of DVS [0.0..1.0 -, R]",
            "*    DVS      Q12P   (maximum 15 records)",
            "  Q12PTB = "
        ])

        # 始终输出Q12PTB表
        if self.q12ptb is not None and not self.q12ptb.empty:
            for _, row in self.q12ptb.iterrows():
                lines.append(f"   {row.iloc[0]:<10} {row.iloc[1]:<10}")
        else:
            # 使用预加载的默认Q12PTB表
            if self._default_q12ptb is not None:
                for _, row in self._default_q12ptb.iterrows():
                    lines.append(f"   {row.iloc[0]:<10} {row.iloc[1]:<10}")
            else:
                # 如果数据文件不可用，使用硬编码默认值
                lines.extend([
                    "   0.0        5.0      ",
                    "   0.5        2.6      ",
                    "   1.0        1.6      "
                ])
        lines.append("* End of table")
        
        lines.append("***********************************************************************************************")
        
        # Section 8: Light extinction
        lines.extend([
            "",
            "***********************************************************************************************",
            "* Section 8: light extinction ",
            "*",
            "* List extinction coefficient for global solar radiation as function of DVS [0.3..1.0 -, R]",
            "*    DVS      KGSR   (maximum 15 records)",
            "  KGSRTB = "
        ])
        
        # KGSRTB表
        for _, row in self.kgsrtb.iterrows():
            lines.append(f"   {row.iloc[0]:<10} {row.iloc[1]:<10}")
        lines.append("* End of table")
        
        lines.extend([
            "* List extinction coefficient for photosynthetically-active radiation (PAR) as function of DVS [0.4..1.2 -, R]",
            "*    DVS      KPAR   (maximum 15 records)",
            "  KPARTB = "
        ])
        
        # KPARTB表
        for _, row in self.kpartb.iterrows():
            lines.append(f"   {row.iloc[0]:<10} {row.iloc[1]:<10}")
        lines.append("* End of table")
        lines.append("***********************************************************************************************")
        
        # Section 9: EPIC crop growth module
        lines.extend([
            "",
            "***********************************************************************************************",
            "* Section 9： read crop parameters in EPIC crop growth module",
            "*",
            f" TBA ={self.get_param_value('tba', self.tba)}! Base temperature for crop grwoth, [0..30.0 -, R]",
            f" TOPT ={self.get_param_value('topt', self.topt)}! Optimal temperature for crop growth, [0..60.0 -, R]",
            f" RUEP ={self.get_param_value('ruep', self.ruep)}! Potential plant radiation-use efficiency at 330ppm CO2, [0..120.0 -, R]",
            f" DLAP1 ={self.get_param_value('dlap1', self.dlap1)}! Combination parameters controlling the first point on optimal LAI development curve, [0..100&0..100, R], ",
            "*              Number before decimal is % of growing season, and number after decimal is % of maximum potential LAI",
            f" DLAP2={self.get_param_value('dlap2', self.dlap2)} ! Combination parameters controlling the first point on optimal LAI development curve, [0..100&0..100, R], ",
            "*              Number before decimal is % of growing season, and number after decimal is % of maximum potential LAI",
            f" FDLAI ={self.fdlai} ! Fraction of growing season when LAI delinces, [0..1.0 -, R]",
            f" RLAD ={self.rlad}  ! LAI decline rate parameter (1.0 is linear; >1,accelerates; <1 retards decline rate), [0..10.0 -, R]",
            f" CHmax ={self.get_param_value('chmax', self.chmax)} ! Maximum crop/canopy height, [0..5000 cm -, R]",
            f" Lmax ={self.get_param_value('lmax', self.lmax)} ! Potential maximum Leaf area index, [0..1.0 -, R]",
            f" RDmax ={self.get_param_value('rdmax', self.rdmax)}! Maximum root depth, [0..5000 cm -, R]",
            f" bn1 ={self.bn1:.3f}  ! Optimal nitrogen demand for seedling period, [0..1 kgN/kg biomass, R]",
            f" bn2 ={self.bn2:.3f}  ! Optimal nitrogen demand for mid-growth period, [0..1 kgN/kg biomass, R]",
            f" bn3 ={self.bn3:.3f}  ! Optimal nitrogen demand for maturity period, [0..1 kgN/kg biomass, R]",
            f" HIpot ={self.hipot:.2f}! Potential harvest index at crop maturity, [0..1.0 -, R]",
            f" HImin ={self.himin:.2f}! Lower limit of harvest index under water-stress stress, [0..1.0 -, R]",
            f" PHU ={int(self.get_param_value('phu', self.phu))}  ! Total potential heat units required for crop maturation, [0..3000 -, R]",
            f" DRUEVP ={self.druevp} ! Decline of RUE per unit increase in VPD [0..120 (kg/ha)/(MJ/m2*kPa), R]",
            "***********************************************************************************************"
        ])
        
        # Section 10: Active root nitrogen uptake
        lines.extend([
            "",
            "***********************************************************************************************",
            "* Section 10: active root nitrogen uptake, only used for nitrogen simulation M-M equation",
            "*",
            f"  SWACTN = {self.swactn} ! Switch, active root nitrogen uptake [Y=1, N=0]",
            "* If SWACTN = 1, specify below values:",
            f"  PWRTS  = {self.pwrts} ! Number of plant per hectare (plant/ha), [0..1.0E5 -, I]",
            f"  CNUP1  = {self.cnup1} ! Maximum nitrogen uptake rate (g/plant/day), [0..10.0 -, R]",
            f"  CNUP2  = {self.cnup2} ! Nitrogen uptake efficiency coefficient (ppm, ug N/cm3 water) [0..10.0 -, R]",
            "***********************************************************************************************"
        ])
        
        # Section 11: Maximum ponding depth control
        lines.extend([
            "",
            "***********************************************************************************************",
            "* Section 11: maximum ponding depth control during crop growth period",
            "*",
            f"  SWPDA =  {self.swpda} ! Switch, if adjust maximum depth of surface ponding [Y=1, N=0]",
            "* If SWPONDA = 1, specify the adjusted pondmxc [0..100 cm,R] as function of DVS [0..2 -,R]: ",
            "*  DVS  PDMXC    (maximum 36 records)  ",
            " PDMXCTB =  "
        ])
        
        if self.pdmxctb is not None and not self.pdmxctb.empty:
            for _, row in self.pdmxctb.iterrows():
                lines.append(f"   {row.iloc[0]:<10} {row.iloc[1]:<10}")
        else:
            lines.extend(_DEFAULT_PDMXCTB_VALUES)
        lines.append("* End of table")
        lines.append("***********************************************************************************************")
        
        # Section 12: CO2 effects
        lines.extend([
            "",
            "***********************************************************************************************",
            "* Section 12: the effects of CO2 level on leaf conductance(i.e. RSC) and max LAI",
            "*",
            f"  SWCO2 = {self.swco2} ! Switch, effects of CO2 level on crops[Y=1, N=0]",
            "* If SWACO2 = 1, specify below three parameters:",
            f"  CO2    = {self.co2}! CO2 concentration [0..1000 ppmv, R] C_O2=330C_O2",
            f"  PDLC   = {self.pdlc:.2f} ! Percentage decrease in leaf conductance at 660 ppm [0..0.5 -,R]",
            f"  PILM   = {self.pilm:.2f} ! Percentage increase in maximum LAI at 660 ppm [0..0.5 -,R]",
            f"  RUECP2 = {self.ruecp2:.2f} ! Combination parameters controlling the second point on C_O2 effect curve on RUE, [300..1500 & 0..120 -, R],",
            "*                       Number before decimal is a value of C_O2 concentration higher than ambient (e.g. 450 or 660 ppm or uL/L) ",
            "*                       above the ambient, and number after decimal is the corrsponding RUE value.",
            "***********************************************************************************************"
        ])
        
        # Section 13: Initial values
        lines.extend([
            "",
            "***********************************************************************************************",
            "* Section 13: Initial values ",
            "*",
            f"  HUIINI = {self.huiini} ! initial HUI for the transplanted crops [0.0..1.0, R]",
            f"  LAIINI = {self.laiini} ! Initial leaf area index [0..10 -, R]",
            f"  RDINI  = {self.rdini} ! Initial root depth, [0..1000 cm, R]",
            f"  BIOINI = {self.bioini} ! Initial total crop dry biomass [0.0..10000 kg/ha, R]",
            "***********************************************************************************************",
            "* End of file",
            "**********************************",
            "* End of file"
        ])
        
        return "\n".join(lines)
    
    def write_epic_crop(self, path: str | _Path, filename: str = "Maize") -> None:
        """写入.CRE文件

        参数:
            path: 输出路径（可以是目录路径或完整文件路径）
            filename: 文件名（不含扩展名），当path是完整文件路径时忽略此参数
        """
        path_obj = _Path(path)
        
        # 如果path是完整的文件路径（包含.CRE扩展名）
        if path_obj.suffix.lower() == '.cre':
            directory = str(path_obj.parent)
            filename = path_obj.stem
        else:
            # path是目录路径
            directory = str(path_obj)
            
        self.save_file(string=self.model_string(), path=directory, fname=filename)
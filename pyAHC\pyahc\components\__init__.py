"""AHC 模型组件。

此模块包含 pyAHC 模型的组件。每个组件都实现为一个继承自 Pydantic BaseModel 的类。
这些组件用于存储模型的输入数据，并提供将数据转换为 AHC 模型所需格式的方法。
"""

from pyahc.components import (
    boundary,
    crop,
    drainage,
    irrigation,
    meteorology,
    simsettings,
    soilwater,
    transport,
)
from .epic_crop import EpicCrop
from .observation import ObservationPoints
from .metadata import Metadata

__all__ = [
    "boundary",
    "crop",
    "drainage",
    "irrigation",
    "meteorology",
    "simsettings",
    "soilwater",
    "transport",
    "metadata",
    "Metadata",
    "EpicCrop",
    "ObservationPoints",
]

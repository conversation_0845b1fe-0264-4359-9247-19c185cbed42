"""AHC 模型和 AHC 4.2 可执行文件的外部资源。

此模块包含 AHC 模型和 AHC 4.2 可执行文件以及外部资源的路径，
方便在整个包中轻松访问它们。以下资源可用：

模块：
    libs: `libs` 模块的根目录。
    ahc_linux: AHC420 Linux 可执行文件目录。
    ahc_windows: AHC420 Windows 可执行文件目录。
    crop_params: WOFOST 作物参数目录。

Linux 和 Windows 可执行文件是通过 AHC 模型主网站
(https://www.ahc.alterra.nl/) 随 AHC 4.2 模型分发的编译可执行文件。
预编译可执行文件随附的源代码许可证包含在 `libs` 根目录中。

随包作为子模块提供的 WOFOST 作物参数来自 Allard de Wit 的 GitHub 仓库
(https://github.com/ajwdewit/WOFOST_crop_parameters)
"""

from importlib import resources
from importlib.abc import Traversable

libs: Traversable = resources.files(__name__)
"""Libs 模块目录。"""

ahc_linux: Traversable = libs / "ahc201-linux" / "ahc201"
"""AHC420 Linux 可执行文件目录。"""

ahc_windows: Traversable = libs / "ahc201-exe" / "ahc-v201.exe"
"""AHC V201 Windows 可执行文件目录。"""

crop_params: Traversable = libs / "WOFOST_crop_parameters"
"""WOFOST 作物参数目录。"""

plotting: Traversable = libs / "plotting"
"""绘图资源目录。"""

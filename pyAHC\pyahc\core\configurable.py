"""可配置组件基类模块

该模块定义了支持外部参数配置的组件基类，用于实现参数外部化配置功能。

类：
    ConfigurableComponent: 支持外部参数配置的组件基类
"""

# 使用 pySWAP-main 导入别名约定，带下划线前缀
from typing import Dict as _Dict, Any as _Any, Optional as _Optional
from pydantic import PrivateAttr as _PrivateAttr

from pyahc.core.basemodel import PyAHCBaseModel as _PyAHCBaseModel
from pyahc.log import logging

logger = logging.getLogger(__name__)


class ConfigurableComponent(_PyAHCBaseModel):
    """支持外部参数配置的组件基类
    
    该基类为 pyAHC 组件提供外部参数配置功能，允许组件在运行时
    接收和管理外部配置的参数，实现参数的外部化配置。
    
    主要功能：
    - 外部参数设置和获取
    - 参数默认值管理
    - 参数元数据管理
    - 从外部配置创建实例
    """
    
    _external_params: _Dict[str, _Any] = _PrivateAttr(default_factory=dict)
    _param_metadata: _Dict[str, _Dict[str, _Any]] = _PrivateAttr(default_factory=dict)
    _param_defaults: _Dict[str, _Any] = _PrivateAttr(default_factory=dict)

    def set_external_param(self, param_name: str, value: _Any,
                          metadata: _Optional[_Dict[str, _Any]] = None) -> None:
        """设置外部参数
        
        Args:
            param_name: 参数名称
            value: 参数值
            metadata: 参数元数据（可选）
        """
        self._external_params[param_name] = value
        if metadata:
            self._param_metadata[param_name] = metadata
        logger.debug(f"Set external param {param_name}={value} for {self.__class__.__name__}")

    def get_param_value(self, param_name: str, default: _Any = None) -> _Any:
        """获取参数值（优先使用外部配置）
        
        Args:
            param_name: 参数名称
            default: 默认值
            
        Returns:
            参数值，优先级：外部参数 > 实例属性 > 默认值 > 传入的default
        """
        # 1. 优先使用外部参数
        if param_name in self._external_params:
            return self._external_params[param_name]
        
        # 2. 使用实例属性
        if hasattr(self, param_name):
            value = getattr(self, param_name)
            if value is not None:
                return value
        
        # 3. 使用设置的默认值
        if param_name in self._param_defaults:
            return self._param_defaults[param_name]
        
        # 4. 使用传入的默认值
        return default

    def set_param_default(self, param_name: str, default_value: _Any) -> None:
        """设置参数默认值
        
        Args:
            param_name: 参数名称
            default_value: 默认值
        """
        self._param_defaults[param_name] = default_value
        logger.debug(f"Set default {param_name}={default_value} for {self.__class__.__name__}")

    def get_param_metadata(self, param_name: str) -> _Optional[_Dict[str, _Any]]:
        """获取参数元数据
        
        Args:
            param_name: 参数名称
            
        Returns:
            参数元数据字典，如果不存在则返回None
        """
        return self._param_metadata.get(param_name)

    def list_external_params(self) -> _Dict[str, _Any]:
        """列出所有外部参数
        
        Returns:
            外部参数字典的副本
        """
        return self._external_params.copy()

    def list_param_defaults(self) -> _Dict[str, _Any]:
        """列出所有参数默认值
        
        Returns:
            参数默认值字典的副本
        """
        return self._param_defaults.copy()

    def clear_external_params(self) -> None:
        """清除所有外部参数"""
        self._external_params.clear()
        self._param_metadata.clear()
        logger.debug(f"Cleared external params for {self.__class__.__name__}")

    @classmethod
    def from_external_config(cls, config: _Dict[str, _Any]) -> "ConfigurableComponent":
        """从外部配置创建实例
        
        Args:
            config: 外部配置字典
            
        Returns:
            配置好的组件实例
        """
        # 分离模型字段和外部参数
        model_fields = {}
        external_params = {}

        for key, value in config.items():
            if hasattr(cls, 'model_fields') and key in cls.model_fields:
                model_fields[key] = value
            else:
                external_params[key] = value

        # 创建实例
        instance = cls(**model_fields)

        # 设置外部参数
        for key, value in external_params.items():
            instance.set_external_param(key, value)

        logger.debug(f"Created {cls.__name__} from external config with {len(external_params)} external params")
        return instance

    @classmethod
    def from_config(cls, config: _Dict[str, _Any]) -> "ConfigurableComponent":
        """从外部配置创建实例（别名方法）
        
        Args:
            config: 外部配置字典
            
        Returns:
            配置好的组件实例
        """
        return cls.from_external_config(config)

    def get_effective_param_value(self, param_name: str) -> _Any:
        """获取有效参数值（用于序列化）
        
        该方法用于在序列化时获取参数的有效值，
        确保外部配置的参数能够正确地用于输出生成。
        
        Args:
            param_name: 参数名称
            
        Returns:
            参数的有效值
        """
        return self.get_param_value(param_name)

    def update_external_config(self, config: _Dict[str, _Any]) -> None:
        """更新外部配置
        
        Args:
            config: 要更新的配置字典
        """
        for key, value in config.items():
            self.set_external_param(key, value)
        logger.debug(f"Updated external config for {self.__class__.__name__}")

    def has_external_param(self, param_name: str) -> bool:
        """检查是否存在外部参数
        
        Args:
            param_name: 参数名称
            
        Returns:
            如果存在外部参数则返回True
        """
        return param_name in self._external_params

    def remove_external_param(self, param_name: str) -> bool:
        """移除外部参数
        
        Args:
            param_name: 参数名称
            
        Returns:
            如果成功移除则返回True，如果参数不存在则返回False
        """
        if param_name in self._external_params:
            del self._external_params[param_name]
            if param_name in self._param_metadata:
                del self._param_metadata[param_name]
            logger.debug(f"Removed external param {param_name} from {self.__class__.__name__}")
            return True
        return False

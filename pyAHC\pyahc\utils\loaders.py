# ruff: noqa: SIM210
# mypy: disable-error-code="operator"
# operator 错误是在 _parse_ascii_file 中匹配字典的方法上引发的。这不是一个优先修复的问题。

import re
from pathlib import Path
from typing import Literal as _Literal

from pyahc import Model
from pyahc.components.boundary import BottomBoundary
from pyahc.components.crop import (
    CO2Correction,
    CompensateRWUStress,
    Crop,
    CropDevelopmentSettingsFixed,
    CropDevelopmentSettingsGrass,
    CropDevelopmentSettingsWOFOST,
    CropFile,
    DroughtStress,
    GrasslandManagement,
    Interception,
    OxygenStress,
    Preparation,
    SaltStress,
)
from pyahc.components.drainage import DraFile, Drainage, Flux
from pyahc.components.irrigation import FixedIrrigation, ScheduledIrrigation
from pyahc.components.meteorology import Meteorology
from pyahc.components.simsettings import GeneralSettings, RichardsSettings
from pyahc.components.soilwater import (
    Evaporation,
    SnowAndFrost,
    SoilMoisture,
    SoilProfile,
    SurfaceFlow,
)
from pyahc.components.transport import HeatFlow, SoluteTransport
from pyahc.core.basemodel import PyAHCBaseModel
from pyahc.core.defaults import EXTENSION_SWITCHES
from pyahc.core.io.io_ascii import open_ascii
# ASCII 文件解析功能已重新实现，不再依赖外部模块

__all__ = ["load_swp"]


def _parse_ascii_file(path: Path, grass_crp: bool = False):
    """解析 .swp 文件并返回参数。

    注意：此函数需要重新实现以支持 ASCII 文件解析。
    """
    raise NotImplementedError(
        "ASCII 文件解析功能需要重新实现。"
        "如需使用此功能，请实现相关的解析逻辑。"
    )


def load_swp(path: Path, metadata: PyAHCBaseModel) -> Model:
    """Load a AHC model from a .swp file.

    Parameters:
        path (Path): Path to the .swp file.

    Returns:
        PyAHCBaseModel: The loaded model.
    """
    # 完成这个。本质上你需要从主字典中弹出扩展开关，然后创建扩展列表。名称必须正确处理。
    params = _parse_ascii_file(path)
    # from among the parameters parsed from the ascii file, pop the switches
    extension_switches = {
        key: params.pop(key) for key in EXTENSION_SWITCHES if key in params
    }
    # 创建只包含值为 1 的开关的扩展列表。去除 "sw" 前缀

    extension_list = [
        key[2:] for key, value in extension_switches.items() if int(value) == 1
    ]

    # 模型定义
    model_setup = {
        "generalsettings": GeneralSettings(extensions=extension_list),
        "meteorology": Meteorology(),
        "crop": Crop(),
        "fixedirrigation": FixedIrrigation(),
        "soilmoisture": SoilMoisture(),
        "surfaceflow": SurfaceFlow(),
        "evaporation": Evaporation(),
        "soilprofile": SoilProfile(),
        "snowandfrost": SnowAndFrost(),
        "richards": RichardsSettings(),
        "lateraldrainage": Drainage(),
        "bottomboundary": BottomBoundary(),
        "heatflow": HeatFlow(),
        "solutetransport": SoluteTransport(),
    }

    for value in model_setup.values():
        value.update(params, inplace=True)

    ml = Model(metadata=metadata, **model_setup)

    return ml


def load_dra(path: Path):
    params = _parse_ascii_file(path)

    flux_objects_startwith = [
        "drares",
        "infres",
        "swallo",
        "l",
        "zbotdr",
        "swdtyp",
        "datowltb",
    ]

    flux_objects = {
        k: v
        for k, v in params.items()
        if any(re.match(f"{prefix}[1-5]$", k) for prefix in flux_objects_startwith)
    }
    other_params = {k: v for k, v in params.items() if k not in flux_objects}

    flux = Flux(**flux_objects)
    dra = DraFile(**other_params, fluxes=flux)

    return dra


def load_crp(path: Path, crptype: _Literal["fixed", "wofost", "grass"], name: str):
    params = _parse_ascii_file(path, grass_crp=True if crptype == "grass" else False)

    cropfile_setup = {
        "name": name,
        "prep": Preparation(),
        "cropdev_settings": CropDevelopmentSettingsFixed()
        if crptype == "fixed"
        else CropDevelopmentSettingsWOFOST()
        if crptype == "wofost"
        else CropDevelopmentSettingsGrass(),
        "oxygenstress": OxygenStress(),
        "droughtstress": DroughtStress(),
        "saltstress": SaltStress(),
        "compensaterwu": CompensateRWUStress(),
        "interception": Interception(),
        "scheduledirrigation": ScheduledIrrigation(),
        "grasslandmanagement": GrasslandManagement(),
        "co2correction": CO2Correction(),
    }

    for value in cropfile_setup.values():
        if isinstance(value, str):
            continue
        if isinstance(value, PyAHCBaseModel):
            value.update(new=params, inplace=True)
        else:
            continue

    crp = CropFile(**cropfile_setup)

    return crp


def load_bbc(path: Path, bottomboundary: BottomBoundary | None = None):
    """Load the bottom boundary conditions from a .bbc file.

    Bottom boundary conditions are stored in the same class. Therefore this
    function can either return a new instance of the class or update an existing
    one.
    """
    params = _parse_ascii_file(path)

    if bottomboundary is None:
        bottomboundary = BottomBoundary()

    botbound = bottomboundary.update(params)

    return botbound

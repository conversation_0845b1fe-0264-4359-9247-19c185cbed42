# -*- coding: utf-8 -*-
# pylint: disable=invalid-name, too-many-arguments, too-many-instance-attributes
# pylint: disable=attribute-defined-outside-init

"""版权所有 2015 <PERSON> Jr.

FilterPy 库.
http://github.com/rlabbe/filterpy

文档地址:
https://filterpy.readthedocs.org

配套书籍:
https://github.com/rlabbe/Kalman-and-Bayesian-Filters-in-Python

本代码基于 MIT 许可证授权。更多信息请参见 readme.MD 文件。
"""


from __future__ import (absolute_import, division, print_function,
                        unicode_literals)

from copy import deepcopy
import numpy as np
from numpy import array, zeros, eye, dot
from numpy.random import multivariate_normal
from KFs.common import pretty_str, outer_product_sum
import datetime

class EnsembleKalmanFilter(object):
    """
    这是集合卡尔曼滤波器 (EnKF) 的实现。EnKF 使用数百到数千个状态向量的集合，
    这些向量在估计值周围随机采样，并在每个更新和预测步骤中添加扰动。
    它对于水动力学等极大型系统非常有用。因此，这个类实际上是一个玩具实现，
    因为对于大的 N 值来说太慢了。

    这种滤波器有很多版本。这个公式来自 Crassidis 和 Junkins [1]。
    它适用于线性和非线性系统。

    参数
    ----------

    x : np.array(dim_x)
        状态均值

    P : np.array((dim_x, dim_x))
        状态协方差

    dim_z : int
        测量输入的数量。例如，如果传感器提供 (x,y) 位置，dim_z 就是 2。

    N : int
        sigma 点（集合）的数量。必须大于 1。

    K : np.array
        卡尔曼增益

    hx : function hx(x)
        测量函数。可以是线性或非线性的 - 将状态 x 转换为测量值。
        返回值必须是与测量向量相同维度的 np.array。

    fx : function fx(x, dt)
        状态转移函数。可以是线性或非线性的。将状态 x 投影到下一个时间段。
        返回投影后的状态 x。


    属性
    ----------
    x : numpy.array(dim_x, 1)
        状态估计

    P : numpy.array(dim_x, dim_x)
        状态协方差矩阵

    x_prior : numpy.array(dim_x, 1)
        先验（预测）状态估计。*_prior 和 *_post 属性是为了方便使用；
        它们存储当前时期的先验和后验。只读。

    P_prior : numpy.array(dim_x, dim_x)
        先验（预测）状态协方差矩阵。只读。

    x_post : numpy.array(dim_x, 1)
        后验（更新）状态估计。只读。

    P_post : numpy.array(dim_x, dim_x)
        后验（更新）状态协方差矩阵。只读。

    z : numpy.array
        在 update() 中使用的最后一次测量。只读。

    R : numpy.array(dim_z, dim_z)
        测量噪声矩阵

    Q : numpy.array(dim_x, dim_x)
        过程噪声矩阵

    fx : callable (x, dt)
        状态转移函数

    hx : callable (x)
        测量函数。将状态 `x` 转换为测量值

    K : numpy.array(dim_x, dim_z)
        更新步骤的卡尔曼增益。只读。

    inv : function, 默认 numpy.linalg.inv
        如果你更喜欢其他逆函数，比如 Moore-Penrose 伪逆，
        可以设置为那个函数：kf.inv = np.linalg.pinv

    """

    def __init__(self, x, P, dim_z, N, hx, fx, x_paraLoc = None):
        if dim_z <= 0:
            raise ValueError('dim_z must be greater than zero')

        if N <= 0:
            raise ValueError('N must be greater than zero')

        dim_x = len(x)
        self.dim_x = dim_x
        self.dim_z = dim_z
        self.N = N
        self.hx = hx
        self.fx = fx
        self.K = zeros((dim_x, dim_z))
        self.z = array([[None] * self.dim_z]).T
        self.S = zeros((dim_z, dim_z))   # 系统不确定性
        self.SI = zeros((dim_z, dim_z))  # 逆系统不确定性
        self.x_paraLoc = x_paraLoc
        self.initialize(x, P)
        # self.Q = eye(dim_x)       # 过程不确定性 # 由 Qi 废弃
        self.R = eye(dim_z)       # 状态不确定性
        self.inv = np.linalg.inv

        # 用于创建以 0 为均值的误差项
        # 用于状态和测量
        self._mean = zeros(dim_x)
        self._mean_z = zeros(dim_z)
        self.allState = [''] * self.N  # 由 Qi 于 2020/8/31 添加
        self.modelDone = [False] * self.N  # 由 Qi 于 2020/8/31 添加
        self.allModelDone = False  # 由 Qi 于 2020/8/31 添加
        self.nextObsDone = [False] * self.N 
        self.allnextObsDone = False 
        self.restartDone = [False] * self.N 
        self.allRestartDone = False 
        
    def initialize(self, x, P):
        """
        用指定的均值和协方差初始化滤波器。
        只有在使用滤波器过滤多组数据时才需要调用此函数；
        这个函数由 __init__ 调用

        参数
        ----------

        x : np.array(dim_z)
            状态均值

        P : np.array((dim_x, dim_x))
            状态协方差
        """

        if x.ndim != 1:
            raise ValueError('x must be a 1D array')
            
        if self.x_paraLoc == None:  # 由 Qi 于 2020/9/11 添加
            self.sigmas = np.tile(x, (self.N, 1))  # 由 Qi 于 2020/8/31 添加
        else:
            CV = 0.1
            state_loc = [t for t in range(len(x)) if not t in self.x_paraLoc]  # 由 Qi 于 2020/9/11 添加
            self.sigmas = np.tile(x[state_loc], (self.N, 1))
            Pt = np.diag(list((x[self.x_paraLoc] * CV)**2))
            statePara = multivariate_normal(mean=x[self.x_paraLoc], cov=Pt, size=self.N)
            self.sigmas = np.hstack((self.sigmas, statePara))
            
        self.x = x
        self.P = P
        self.P_z = P  # 由 Qi 于 2021/8/13 添加，测量空间中的 P
        
        # 这些将始终是调用 predict() 后 x,P 的副本
        self.x_prior = self.x.copy()
        self.P_prior = self.P.copy()

        # 这些将始终是调用 update() 后 x,P 的副本
        self.x_post = self.x.copy()
        self.P_post = self.P.copy()

    def update(self, z, R=None, Q = None):  # 'Q': 过程误差，由 Qi 于 2020/10/9 添加
        """
        向卡尔曼滤波器添加新的测量值 (z)。如果 z 为 None，则不做任何改变。

        参数
        ----------

        z : np.array
            此次更新的测量值。

        R : np.array, scalar, or None
            可选择提供 R 来覆盖此次调用的测量噪声，
            否则将使用 self.R。
        """

        if z is None:
            self.z = array([[None]*self.dim_z]).T
            self.x_post = self.x.copy()
            self.P_post = self.P.copy()
            return

        if R is None:
            R = self.R
        if np.isscalar(R):
            R = eye(self.dim_z) * R
        if Q is None:  # 'Q': 过程误差，由 Qi 于 2020/10/9 添加
            Q = R.copy()
        N = self.N
        dim_z = len(z)
        sigmas_h = zeros((N, dim_z))

        # 将 sigma 点转换到测量空间
        for i in range(N):
            sigmas_h[i] = self.hx(self.sigmas[i])

        z_mean = np.mean(sigmas_h, axis=0)

        P_zz = (outer_product_sum(sigmas_h - z_mean) / (N-1)) + R
        P_xz = outer_product_sum(
                self.sigmas - self.x, sigmas_h - z_mean) / (N - 1)

        self.S = P_zz
        self.SI = self.inv(self.S)
        self.K = dot(P_xz, self.SI)
        
        # tmp = self.sigmas[0].copy()
        # print ('更新 sigmas0:{}'.format(self.sigmas[2]))
        e_r = multivariate_normal(self._mean_z, Q, N)
        for i in range(N):
            self.sigmas[i] += dot(self.K, z + e_r[i] - sigmas_h[i])
        # print('sigmas:{},K:{},Q:{},er:{},delta:{}'.format(self.sigmas[0], self.K,Q,e_r, z + e_r[0] - sigmas_h[0]))
        # print ('原始 sigmas:{}, 更新后:{}, K:{}, z:{}, delta :{}'.format(tmp, self.sigmas[0], self.K, z, z + e_r[0] - sigmas_h[0]))
        self.x = np.mean(self.sigmas, axis=0)
        self.P = self.P - dot(dot(self.K, self.S), self.K.T)

        # 保存测量值和后验状态
        self.z = deepcopy(z)
        self.x_post = self.x.copy()
        self.P_post = self.P.copy()

    def predict(self,dt):
        """ 预测下一个位置。 """

        N = self.N
        for i, s in enumerate(self.sigmas):
            self.sigmas[i],self.allState[i] = self.fx(s, dt, i)
            self.modelDone[i] = self.allState[i]['Done']  # 由 Qi 于 2020/8/31 添加
        if not (False in self.modelDone):  # 由 Qi 于 2020/8/31 添加
            self.allModelDone = True  # 由 Qi 于 2020/8/31 添加
        
        # e = multivariate_normal(self._mean, self.Q, N)  # 由 Qi 于 2020/8/31 废弃
        # self.sigmas += e

        self.x = np.mean(self.sigmas, axis=0)
        self.P = outer_product_sum(self.sigmas - self.x) / (N - 1)
        sigmas_h = np.array([self.hx(self.sigmas[i]) for i in range(N)])
        self.P_z = outer_product_sum(sigmas_h - np.mean(sigmas_h, axis=0)) / (N - 1)  # 由 Qi 于 2021/8/13 添加，测量空间中的 P
        
        # 保存先验
        self.x_prior = np.copy(self.x)
        self.P_prior = np.copy(self.P)
        
    def resetNextDone(self):
        self.nextObsDone = [False] * self.N 
        self.allnextObsDone = False 
        
    def resetRestartDone(self):
        self.restartDone = [False] * self.N 
        self.allRestartDone = False 
        
    def restartPredict(self,dt,nextObsDateNum,targetDateNum):   # 由 Qi 于 2020/10/6 添加
        """ 预测下一个位置。 """

        N = self.N
        for i, s in enumerate(self.sigmas):
            if not self.restartDone[i]:
                if not self.nextObsDone[i]:
                    self.sigmas[i],self.allState[i] = self.fx(s, dt, i)
                    currentDate = self.allState[i]['currentDate']
                    currentDateNum = datetime.date(int(currentDate.split('-')[0]),int(currentDate.split('-')[1]),
                                           int(currentDate.split('-')[2])).toordinal()
                    if currentDateNum >= nextObsDateNum:
                        self.nextObsDone[i] = True 
                    if currentDateNum >= targetDateNum:
                        self.restartDone[i] = True
        if not (False in self.restartDone): 
            self.allRestartDone = True 
        if not (False in self.nextObsDone): 
            self.allnextObsDone = True         
        # e = multivariate_normal(self._mean, self.Q, N)  # 由 Qi 于 2020/8/31 废弃
        # self.sigmas += e

        self.x = np.mean(self.sigmas, axis=0)
        self.P = outer_product_sum(self.sigmas - self.x) / (N - 1)

        # 保存先验
        self.x_prior = np.copy(self.x)
        self.P_prior = np.copy(self.P)
       
    def __repr__(self):
        return '\n'.join([
            'EnsembleKalmanFilter object',
            pretty_str('dim_x', self.dim_x),
            pretty_str('dim_z', self.dim_z),
            pretty_str('x', self.x),
            pretty_str('P', self.P),
            pretty_str('x_prior', self.x_prior),
            pretty_str('P_prior', self.P_prior),
            # pretty_str('Q', self.Q),
            pretty_str('R', self.R),
            pretty_str('K', self.K),
            pretty_str('S', self.S),
            pretty_str('sigmas', self.sigmas),
            pretty_str('hx', self.hx),
            pretty_str('fx', self.fx)
            ])

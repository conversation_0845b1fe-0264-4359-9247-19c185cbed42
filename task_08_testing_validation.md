# 任务08：全面测试与验证

## 任务概述
为整个HDF5集成系统建立全面的测试框架，包括单元测试、集成测试、性能测试和验证测试。确保系统的可靠性、稳定性和正确性。


特别注意:测试验证成功后,删除掉相关测试验证代码文件

## 任务需求

### 核心目标
1. 建立完整的测试框架和测试套件
2. 实现自动化测试和持续集成
3. 进行系统性能基准测试
4. 验证数据完整性和一致性
5. 提供测试报告和质量评估

### 具体要求
- 覆盖所有核心功能的单元测试
- 完整的端到端集成测试
- 性能和压力测试
- 数据验证和一致性检查
- 错误处理和边界条件测试

## 实现步骤

### 步骤1：建立测试框架

```python
"""HDF5集成系统测试框架"""
import pytest
import numpy as np
import tempfile
import shutil
from pathlib import Path
from datetime import date, timedelta
import logging
import time
import psutil
from typing import Dict, Any, List, Optional

# 测试配置
TEST_CONFIG = {
    'test_data_dir': Path('./test_data'),
    'temp_dir': Path('./temp_test'),
    'performance_thresholds': {
        'single_day_simulation': 1.0,  # 秒
        'state_extraction': 0.1,  # 秒
        'state_injection': 0.1,  # 秒
        'hdf5_save': 0.5,  # 秒
        'hdf5_load': 0.3,  # 秒
    },
    'memory_thresholds': {
        'max_memory_mb': 1024,  # MB
        'memory_leak_tolerance': 50,  # MB
    }
}

class TestFixtures:
    """测试夹具类"""
    
    @staticmethod
    def create_test_model():
        """创建测试用模型对象"""
        # 这里需要根据实际的pyAHC模型结构来实现
        # 创建一个简化的模型对象用于测试
        class MockModel:
            def __init__(self):
                self.generalsettings = MockGeneralSettings()
                self.soilmoisture = MockSoilMoisture()
                self.crop = MockCrop()
                self.bottomboundary = MockBottomBoundary()
            
            def model_copy(self, deep=True):
                return MockModel()
            
            def run(self):
                return TestFixtures.create_test_result()
        
        return MockModel()
    
    @staticmethod
    def create_test_result():
        """创建测试用结果对象"""
        class MockResult:
            def __init__(self):
                self.csv = TestFixtures.create_test_csv_data()
                self.ascii = TestFixtures.create_test_ascii_data()
        
        return MockResult()
    
    @staticmethod
    def create_test_csv_data():
        """创建测试用CSV数据"""
        import pandas as pd
        
        data = {
            'date': [date(2023, 5, 1)],
            'lai': [2.5],
            'biomass': [3500.0],
            'soil_moisture_layer1': [0.25],
            'soil_moisture_layer2': [0.23],
            'soil_moisture_layer3': [0.21],
            'root_depth': [45.0],
            'groundwater_level': [-150.0]
        }
        
        return pd.DataFrame(data)
    
    @staticmethod
    def create_test_ascii_data():
        """创建测试用ASCII数据"""
        return {
            'sba': """# Soil moisture data
Layer1  0.25
Layer2  0.23
Layer3  0.21
""",
            'crop': """# Crop data
LAI  2.5
Biomass  3500.0
Root_depth  45.0
"""
        }
    
    @staticmethod
    def create_test_states():
        """创建测试用状态变量"""
        return {
            'soil_moisture': np.array([0.25, 0.23, 0.21]),
            'lai': 2.5,
            'biomass': 3500.0,
            'root_depth': 45.0,
            'groundwater_level': -150.0
        }
    
    @staticmethod
    def create_test_observations():
        """创建测试用观测数据"""
        return {
            'lai': 2.8,
            'biomass': 3200.0,
            'soil_moisture': np.array([0.27, 0.25, 0.23]),
            'lai_error': 0.2,
            'biomass_error': 200.0,
            'soil_moisture_error': 0.02
        }

class MockGeneralSettings:
    def __init__(self):
        self.tstart = date(2023, 5, 1)
        self.tend = date(2023, 5, 1)

class MockSoilMoisture:
    def __init__(self):
        self.thetai = [0.3, 0.28, 0.26]
        self.swinco = 0.84

class MockCrop:
    def __init__(self):
        self.lai = 2.0
        self.biomass = 3000.0
        self.root_depth = 40.0

class MockBottomBoundary:
    def __init__(self):
        self.groundwater_level = -200.0
```

### 步骤2：单元测试套件

```python
"""单元测试套件"""

class TestProjectHDF5Manager:
    """ProjectHDF5Manager单元测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_file = Path(self.temp_dir) / "test.h5"
        self.test_config = {
            'project_name': 'test_project',
            'start_date': date(2023, 5, 1),
            'end_date': date(2023, 5, 10),
            'field_id': 'test_field',
            'crop_type': 'corn'
        }
    
    def teardown_method(self):
        """测试后清理"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_manager_creation(self):
        """测试管理器创建"""
        from pyahc.db.hdf5 import ProjectHDF5Manager
        
        manager = ProjectHDF5Manager(self.test_file)
        assert manager.filepath == self.test_file
        assert manager.mode == 'a'
        assert not manager.is_open
    
    def test_context_manager(self):
        """测试上下文管理器"""
        from pyahc.db.hdf5 import ProjectHDF5Manager
        
        with ProjectHDF5Manager(self.test_file) as manager:
            assert manager.is_open
        
        assert not manager.is_open
    
    def test_project_creation(self):
        """测试项目创建"""
        from pyahc.db.hdf5 import ProjectHDF5Manager
        
        metadata = {
            'model_info': {'version': '1.0'},
            'location_info': {'latitude': 45.0}
        }
        
        with ProjectHDF5Manager(self.test_file) as manager:
            manager.create_project('test_project', metadata)
            
            # 验证项目是否创建
            assert manager.project_exists('test_project')
            
            # 验证元数据
            loaded_metadata = manager.get_project_metadata('test_project')
            assert 'model_info' in loaded_metadata
            assert loaded_metadata['model_info']['version'] == '1.0'
    
    def test_daily_data_save_load(self):
        """测试日数据保存和加载"""
        from pyahc.db.hdf5 import ProjectHDF5Manager
        
        # 创建测试数据
        model = TestFixtures.create_test_model()
        result = TestFixtures.create_test_result()
        states = TestFixtures.create_test_states()
        parameters = {'test_param': 1.0}
        
        with ProjectHDF5Manager(self.test_file) as manager:
            # 创建项目
            manager.create_project('test_project', {})
            
            # 保存数据
            test_date = date(2023, 5, 1)
            manager.save_daily_data('test_project', test_date, model, result, states, parameters)
            
            # 加载数据
            loaded_data = manager.load_daily_data('test_project', test_date)
            
            # 验证数据
            assert loaded_data is not None
            assert 'model' in loaded_data
            assert 'result' in loaded_data
            assert 'states' in loaded_data
            assert 'parameters' in loaded_data
            
            # 验证状态变量
            loaded_states = loaded_data['states']
            assert 'lai' in loaded_states
            assert abs(loaded_states['lai'] - states['lai']) < 1e-6
            
            # 验证参数
            loaded_params = loaded_data['parameters']
            assert 'test_param' in loaded_params
            assert abs(loaded_params['test_param'] - parameters['test_param']) < 1e-6

class TestStateExtractor:
    """StateExtractor单元测试"""
    
    def test_extract_all_states(self):
        """测试提取所有状态变量"""
        from pyahc.db.hdf5 import StateExtractor
        
        result = TestFixtures.create_test_result()
        states = StateExtractor.extract_all_states(result)
        
        assert isinstance(states, dict)
        assert 'lai' in states
        assert 'biomass' in states
        assert states['lai'] == 2.5
        assert states['biomass'] == 3500.0
    
    def test_extract_soil_moisture(self):
        """测试土壤含水量提取"""
        from pyahc.db.hdf5 import StateExtractor
        
        result = TestFixtures.create_test_result()
        moisture = StateExtractor.extract_soil_moisture(result)
        
        assert moisture is not None
        assert isinstance(moisture, np.ndarray)
        assert len(moisture) == 3
        assert np.allclose(moisture, [0.25, 0.23, 0.21])
    
    def test_extract_crop_variables(self):
        """测试作物变量提取"""
        from pyahc.db.hdf5 import StateExtractor
        
        result = TestFixtures.create_test_result()
        
        lai = StateExtractor.extract_crop_lai(result)
        assert lai == 2.5
        
        biomass = StateExtractor.extract_crop_biomass(result)
        assert biomass == 3500.0
        
        root_depth = StateExtractor.extract_root_depth(result)
        assert root_depth == 45.0
    
    def test_validate_extracted_states(self):
        """测试状态变量验证"""
        from pyahc.db.hdf5 import StateExtractor
        
        # 测试有效状态
        valid_states = {
            'lai': 2.5,
            'biomass': 3500.0,
            'soil_moisture': np.array([0.25, 0.23, 0.21])
        }
        
        validated = StateExtractor.validate_extracted_states(valid_states)
        assert len(validated) == len(valid_states)
        
        # 测试无效状态
        invalid_states = {
            'lai': -1.0,  # 无效LAI
            'biomass': 100000.0,  # 过大的生物量
            'soil_moisture': np.array([1.5, 0.23, 0.21])  # 无效含水量
        }
        
        validated = StateExtractor.validate_extracted_states(invalid_states)
        # 应该修正或过滤无效值
        assert validated['lai'] >= 0
        assert validated['biomass'] <= 50000
        assert np.all(validated['soil_moisture'] <= 1.0)

class TestStateInjector:
    """StateInjector单元测试"""
    
    def test_inject_all_states(self):
        """测试注入所有状态变量"""
        from pyahc.db.hdf5 import StateInjector
        
        model = TestFixtures.create_test_model()
        states = TestFixtures.create_test_states()
        
        updated_model = StateInjector.inject_all_states(model, states)
        
        # 验证模型已更新
        assert updated_model is not model  # 应该是新对象
        assert hasattr(updated_model, 'soilmoisture')
        assert hasattr(updated_model, 'crop')
    
    def test_inject_soil_moisture(self):
        """测试土壤含水量注入"""
        from pyahc.db.hdf5 import StateInjector
        
        model = TestFixtures.create_test_model()
        moisture = np.array([0.30, 0.28, 0.26])
        
        updated_model = StateInjector.inject_soil_moisture(model, moisture)
        
        # 验证注入结果
        assert updated_model.soilmoisture.thetai == moisture.tolist()
    
    def test_inject_crop_state(self):
        """测试作物状态注入"""
        from pyahc.db.hdf5 import StateInjector
        
        model = TestFixtures.create_test_model()
        
        updated_model = StateInjector.inject_crop_state(
            model, lai=3.0, biomass=4000.0, root_depth=50.0
        )
        
        # 验证注入结果
        assert updated_model.crop.lai == 3.0
        assert updated_model.crop.biomass == 4000.0
        assert updated_model.crop.root_depth == 50.0
```

### 步骤3：集成测试套件

```python
"""集成测试套件"""

class TestWorkflowIntegration:
    """工作流程集成测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_config = {
            'hdf5_path': Path(self.temp_dir) / 'integration_test.h5',
            'project_name': 'integration_test',
            'start_date': date(2023, 5, 1),
            'end_date': date(2023, 5, 5),
            'field_id': 'test_field',
            'crop_type': 'corn',
            'enable_assimilation': False,
            'stop_on_error': True
        }
    
    def teardown_method(self):
        """测试后清理"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_complete_simulation_workflow(self):
        """测试完整模拟工作流程"""
        from pyahc.db.hdf5.workflow import run_project_simulation
        
        # 运行完整模拟
        manager = run_project_simulation(self.test_config)
        
        # 验证项目创建
        assert manager.project_exists(self.test_config['project_name'])
        
        # 验证日数据
        current_date = self.test_config['start_date']
        end_date = self.test_config['end_date']
        
        with manager:
            while current_date <= end_date:
                daily_data = manager.load_daily_data(self.test_config['project_name'], current_date)
                
                assert daily_data is not None
                assert 'model' in daily_data
                assert 'result' in daily_data
                assert 'states' in daily_data
                
                current_date += timedelta(days=1)
        
        # 验证汇总数据
        with manager:
            project_group = manager._file[self.test_config['project_name']]
            assert 'summary' in project_group
            assert 'timeseries' in project_group['summary']
            assert 'statistics' in project_group['summary']
    
    def test_state_continuity(self):
        """测试状态连续性"""
        from pyahc.db.hdf5.workflow import run_project_simulation
        
        # 运行模拟
        manager = run_project_simulation(self.test_config)
        
        # 检查状态连续性
        previous_states = None
        current_date = self.test_config['start_date']
        
        with manager:
            while current_date <= self.test_config['end_date']:
                daily_data = manager.load_daily_data(self.test_config['project_name'], current_date)
                current_states = daily_data['states']
                
                if previous_states is not None:
                    # 验证状态变化的合理性
                    for var_name in ['lai', 'biomass']:
                        if var_name in previous_states and var_name in current_states:
                            # 状态应该有合理的变化
                            change = abs(current_states[var_name] - previous_states[var_name])
                            assert change < previous_states[var_name] * 0.5  # 变化不应超过50%
                
                previous_states = current_states
                current_date += timedelta(days=1)
    
    def test_data_assimilation_integration(self):
        """测试数据同化集成"""
        # 启用数据同化
        config = self.test_config.copy()
        config['enable_assimilation'] = True
        config['observation_dir'] = self.temp_dir / 'observations'
        
        # 创建观测数据
        self._create_test_observations(config['observation_dir'])
        
        from pyahc.db.hdf5.workflow import run_project_simulation
        
        # 运行带同化的模拟
        manager = run_project_simulation(config)
        
        # 验证同化结果
        with manager:
            project_group = manager._file[config['project_name']]
            
            # 检查是否有同化数据
            assimilation_found = False
            for day_key in project_group.keys():
                if day_key.startswith('day_'):
                    day_group = project_group[day_key]
                    if 'assimilation' in day_group:
                        assimilation_found = True
                        
                        # 验证同化数据结构
                        assim_group = day_group['assimilation']
                        assert 'algorithm' in assim_group.attrs
                        assert 'timestamp' in assim_group.attrs
            
            # 如果有观测数据，应该有同化结果
            if self._has_observations():
                assert assimilation_found
    
    def _create_test_observations(self, obs_dir: Path):
        """创建测试观测数据"""
        obs_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建CSV格式观测数据
        obs_data = {
            'date': [date(2023, 5, 2), date(2023, 5, 4)],
            'lai': [2.8, 3.2],
            'biomass': [3200.0, 4100.0]
        }
        
        import pandas as pd
        df = pd.DataFrame(obs_data)
        df.to_csv(obs_dir / 'observations.csv', index=False)
    
    def _has_observations(self) -> bool:
        """检查是否有观测数据"""
        obs_dir = Path(self.test_config.get('observation_dir', ''))
        return obs_dir.exists() and any(obs_dir.iterdir())

class TestPerformanceIntegration:
    """性能集成测试"""
    
    def test_simulation_performance(self):
        """测试模拟性能"""
        config = {
            'hdf5_path': tempfile.mktemp(suffix='.h5'),
            'project_name': 'performance_test',
            'start_date': date(2023, 5, 1),
            'end_date': date(2023, 5, 30),  # 30天模拟
            'field_id': 'perf_field',
            'crop_type': 'corn'
        }
        
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        try:
            from pyahc.db.hdf5.workflow import run_project_simulation
            manager = run_project_simulation(config)
            
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            # 性能验证
            total_time = end_time - start_time
            memory_increase = end_memory - start_memory
            
            # 30天模拟应该在合理时间内完成
            assert total_time < 300  # 5分钟
            
            # 内存增长应该合理
            assert memory_increase < TEST_CONFIG['memory_thresholds']['max_memory_mb']
            
            # 验证模拟速度
            days_per_second = 30 / total_time
            assert days_per_second > 0.1  # 至少每秒0.1天
            
            logging.info(f"Performance test: {total_time:.2f}s, {memory_increase:.1f}MB, {days_per_second:.2f} days/s")
            
        finally:
            # 清理
            Path(config['hdf5_path']).unlink(missing_ok=True)
    
    def test_memory_leak_detection(self):
        """测试内存泄漏检测"""
        config = {
            'hdf5_path': tempfile.mktemp(suffix='.h5'),
            'project_name': 'memory_test',
            'start_date': date(2023, 5, 1),
            'end_date': date(2023, 5, 10),
            'field_id': 'memory_field',
            'crop_type': 'corn'
        }
        
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        try:
            # 运行多次模拟
            for i in range(3):
                config['project_name'] = f'memory_test_{i}'
                
                from pyahc.db.hdf5.workflow import run_project_simulation
                manager = run_project_simulation(config)
                
                # 强制垃圾回收
                import gc
                gc.collect()
                
                current_memory = psutil.Process().memory_info().rss / 1024 / 1024
                memory_increase = current_memory - initial_memory
                
                # 内存增长应该在可接受范围内
                assert memory_increase < TEST_CONFIG['memory_thresholds']['memory_leak_tolerance'] * (i + 1)
        
        finally:
            # 清理
            Path(config['hdf5_path']).unlink(missing_ok=True)
```

### 步骤4：数据验证测试

```python
"""数据验证测试套件"""

class TestDataValidation:
    """数据验证测试"""
    
    def test_hdf5_data_integrity(self):
        """测试HDF5数据完整性"""
        temp_file = tempfile.mktemp(suffix='.h5')
        
        try:
            from pyahc.db.hdf5 import ProjectHDF5Manager
            
            # 创建测试数据
            test_data = {
                'states': TestFixtures.create_test_states(),
                'model': TestFixtures.create_test_model(),
                'result': TestFixtures.create_test_result()
            }
            
            # 保存数据
            with ProjectHDF5Manager(temp_file) as manager:
                manager.create_project('integrity_test', {})
                manager.save_daily_data(
                    'integrity_test', date(2023, 5, 1),
                    test_data['model'], test_data['result'], test_data['states']
                )
            
            # 加载数据并验证
            with ProjectHDF5Manager(temp_file) as manager:
                loaded_data = manager.load_daily_data('integrity_test', date(2023, 5, 1))
                
                # 验证状态变量
                loaded_states = loaded_data['states']
                original_states = test_data['states']
                
                for var_name, original_value in original_states.items():
                    assert var_name in loaded_states
                    loaded_value = loaded_states[var_name]
                    
                    if isinstance(original_value, np.ndarray):
                        assert np.allclose(loaded_value, original_value)
                    else:
                        assert abs(loaded_value - original_value) < 1e-10
        
        finally:
            Path(temp_file).unlink(missing_ok=True)
    
    def test_data_consistency_across_days(self):
        """测试跨日数据一致性"""
        temp_file = tempfile.mktemp(suffix='.h5')
        
        try:
            from pyahc.db.hdf5 import ProjectHDF5Manager, StateExtractor, StateInjector
            
            with ProjectHDF5Manager(temp_file) as manager:
                manager.create_project('consistency_test', {})
                
                # 模拟连续几天的数据
                base_model = TestFixtures.create_test_model()
                previous_states = None
                
                for day in range(5):
                    current_date = date(2023, 5, 1) + timedelta(days=day)
                    
                    # 准备当日模型
                    daily_model = base_model.model_copy(deep=True)
                    if previous_states:
                        daily_model = StateInjector.inject_all_states(daily_model, previous_states)
                    
                    # 运行模型
                    result = daily_model.run()
                    
                    # 提取状态
                    current_states = StateExtractor.extract_all_states(result)
                    
                    # 保存数据
                    manager.save_daily_data(
                        'consistency_test', current_date, daily_model, result, current_states
                    )
                    
                    # 验证状态连续性
                    if previous_states:
                        for var_name in ['lai', 'biomass']:
                            if var_name in previous_states and var_name in current_states:
                                # 状态变化应该合理
                                prev_val = previous_states[var_name]
                                curr_val = current_states[var_name]
                                change_rate = abs(curr_val - prev_val) / prev_val
                                assert change_rate < 0.3  # 日变化不应超过30%
                    
                    previous_states = current_states
        
        finally:
            Path(temp_file).unlink(missing_ok=True)
    
    def test_error_handling_robustness(self):
        """测试错误处理健壮性"""
        temp_file = tempfile.mktemp(suffix='.h5')
        
        try:
            from pyahc.db.hdf5 import ProjectHDF5Manager
            from pyahc.db.hdf5.exceptions import ProjectNotFoundError, DataNotFoundError
            
            with ProjectHDF5Manager(temp_file) as manager:
                # 测试访问不存在的项目
                with pytest.raises(ValueError):
                    manager.load_daily_data('nonexistent_project', date(2023, 5, 1))
                
                # 创建项目
                manager.create_project('error_test', {})
                
                # 测试访问不存在的日期
                with pytest.raises(ValueError):
                    manager.load_daily_data('error_test', date(2023, 5, 1))
                
                # 测试重复创建项目
                with pytest.raises(ValueError):
                    manager.create_project('error_test', {})
        
        finally:
            Path(temp_file).unlink(missing_ok=True)
```

## 可交付成果

### 主要交付物
1. **完整测试套件**：包含单元测试、集成测试、性能测试
2. **测试配置文件**：pytest.ini、test_config.yaml
3. **测试数据集**：test_data/目录，包含各种测试用例
4. **测试报告模板**：test_report_template.html
5. **持续集成配置**：.github/workflows/test.yml

### 测试覆盖范围
- 所有核心类和方法的单元测试
- 完整工作流程的集成测试
- 性能和内存使用测试
- 数据完整性和一致性验证
- 错误处理和边界条件测试

## 验收标准

### 测试覆盖率
1. **单元测试覆盖率**：≥90%
2. **集成测试覆盖率**：≥85%
3. **关键路径覆盖率**：100%

### 性能基准
1. **单日模拟时间**：<1秒
2. **状态提取时间**：<0.1秒
3. **HDF5读写时间**：<0.5秒
4. **内存使用**：<1GB（长期模拟）

### 质量指标
1. **测试通过率**：100%
2. **无内存泄漏**：连续运行无内存增长
3. **数据一致性**：100%准确性
4. **错误处理**：所有异常情况正确处理

## 依赖关系
- **前置依赖**：任务01-07（完整系统实现）
- **后续任务**：任务09（文档和示例）、任务10（部署和维护）


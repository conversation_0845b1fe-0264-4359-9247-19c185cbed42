#!/usr/bin/env python3
"""压力水头数据 - 从标准案例hetao.BBC文件中提取

这个文件包含了从标准案例hetao.BBC文件中提取的完整压力水头数据。
这些数据用于SWOPT5=1（压力水头选项）。

数据格式: (day, month, value)
- day: 日期（天）
- month: 月份
- value: 压力水头值（cm）
"""

# 从标准案例hetao.BBC文件中提取的完整压力水头数据（SWOPT5=1）
HBOT5_DATA = [
    (2, 5, 144.5), (3, 5, 144.5), (4, 5, 144.5), (5, 5, 144.5), (6, 5, 144.5),
    (7, 5, 144.5), (8, 5, 144.5), (9, 5, 144.5), (10, 5, 144.5), (11, 5, 176.2),
    (12, 5, 207.9), (13, 5, 239.5), (14, 5, 250.5), (15, 5, 254), (16, 5, 257.5),
    (17, 5, 261), (18, 5, 264.5), (19, 5, 260.83), (20, 5, 257.16), (21, 5, 253.5),
    (22, 5, 245), (23, 5, 236.5), (24, 5, 228), (25, 5, 219.5), (26, 5, 215.64),
    (27, 5, 211.78), (28, 5, 207.92), (29, 5, 204.06), (30, 5, 200.2), (31, 5, 196.34),
    (1, 6, 192.5), (2, 6, 181), (3, 6, 169.5), (4, 6, 166.5), (5, 6, 163.5),
    (6, 6, 160.5), (7, 6, 157.5), (8, 6, 154.5), (9, 6, 151.5), (10, 6, 148.5),
    (11, 6, 147.5), (12, 6, 146.5), (13, 6, 145.5), (14, 6, 144.5), (15, 6, 143.5),
    (16, 6, 142.5), (17, 6, 141.5), (18, 6, 140.9), (19, 6, 140.3), (20, 6, 139.7),
    (21, 6, 139.1), (22, 6, 138.5), (23, 6, 145.9), (24, 6, 153.3), (25, 6, 160.7),
    (26, 6, 168.1), (27, 6, 175.5), (28, 6, 183.5), (29, 6, 191.5), (30, 6, 209.5),
    (1, 7, 227.5), (2, 7, 223), (3, 7, 218.5), (4, 7, 208.07), (5, 7, 197.64),
    (6, 7, 187.21), (7, 7, 176.78), (8, 7, 166.35), (9, 7, 155.92), (10, 7, 145.5),
    (11, 7, 141.5), (12, 7, 137.5), (13, 7, 133.5), (14, 7, 129.5), (15, 7, 145.17),
    (16, 7, 160.84), (17, 7, 176.5), (18, 7, 219.5), (19, 7, 219.17), (20, 7, 218.84),
    (21, 7, 218.5), (22, 7, 211.25), (23, 7, 204), (24, 7, 196.75), (25, 7, 189.5),
    (26, 7, 182.25), (27, 7, 175), (28, 7, 167.75), (29, 7, 160.5), (30, 7, 153.25),
    (31, 7, 146), (1, 8, 138.75), (2, 8, 131.5), (3, 8, 124.25), (4, 8, 117),
    (5, 8, 109.75), (6, 8, 102.5), (7, 8, 143.5), (8, 8, 139.5), (9, 8, 159.5),
    (10, 8, 192.5), (11, 8, 186.71), (12, 8, 180.92), (13, 8, 175.13), (14, 8, 169.34),
    (15, 8, 163.55), (16, 8, 157.76), (17, 8, 151.97), (18, 8, 146.18), (19, 8, 140.39),
    (20, 8, 134.6), (21, 8, 128.81), (22, 8, 123.02), (23, 8, 117.23), (24, 8, 111.5),
    (25, 8, 109), (26, 8, 106.5), (27, 8, 104), (28, 8, 101.5), (29, 8, 99),
    (30, 8, 96.5), (31, 8, 94), (1, 9, 91.5), (2, 9, 89), (3, 9, 86.5),
    (4, 9, 85.5), (5, 9, 84.5), (6, 9, 83.5), (7, 9, 82.5), (8, 9, 82.07),
    (9, 9, 81.64), (10, 9, 81.21), (11, 9, 80.78), (12, 9, 80.35), (13, 9, 79.92),
    (14, 9, 79.49), (15, 9, 79.06), (16, 9, 78.63), (17, 9, 78.2), (18, 9, 77.77),
    (19, 9, 77.34), (20, 9, 76.91), (21, 9, 76.5)
]

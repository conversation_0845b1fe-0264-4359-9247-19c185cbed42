# 任务01：HDF5基础环境搭建与模块结构创建

## 任务概述
创建pyAHC项目的HDF5集成基础架构，包括目录结构、依赖管理、核心模块框架和基础配置。这是整个HDF5集成项目的基础任务。

## 任务需求

### 核心目标
1. 创建完整的HDF5模块目录结构
2. 配置必要的依赖项和环境
3. 建立核心类的框架代码
4. 实现基础的导入和配置功能
5. 确保与现有pyAHC架构的兼容性

### 具体要求
- 在pyahc/db/目录下创建hdf5子模块
- 配置h5py、numpy等必要依赖
- 创建核心类的框架定义
- 实现模块的__init__.py文件
- 添加基础的类型注解和文档字符串

## 实现步骤

### 步骤1：创建目录结构
```
pyahc/
├── db/
│   ├── __init__.py
│   └── hdf5/
│       ├── __init__.py
│       ├── manager.py
│       ├── state_extractor.py
│       ├── state_injector.py
│       ├── utils.py
│       └── exceptions.py
```

### 步骤2：配置依赖项
在项目根目录的requirements.txt或pyproject.toml中添加：
```
h5py>=3.7.0
numpy>=1.21.0
```

### 步骤3：创建核心类框架

#### manager.py - ProjectHDF5Manager类框架
```python
"""HDF5项目数据管理器"""
import h5py
import numpy as np
from pathlib import Path
from typing import Dict, Any, Union, Optional
from datetime import date, datetime
import logging

class ProjectHDF5Manager:
    """基于项目-日期结构的HDF5数据管理核心类"""
    
    def __init__(self, filepath: Path, mode: str = 'a'):
        self.filepath = filepath
        self.mode = mode
        self._file = None
        self.logger = logging.getLogger(__name__)
    
    def __enter__(self):
        """上下文管理器入口"""
        pass
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        pass
    
    def open(self):
        """打开HDF5文件"""
        pass
    
    def close(self):
        """关闭HDF5文件"""
        pass
    
    def create_project(self, project_name: str, metadata: Dict[str, Any]) -> None:
        """创建新项目"""
        pass
    
    def save_daily_data(self, project_name: str, date: Union[date, datetime],
                       model: 'Model', result: 'Result',
                       states: Dict[str, Any], parameters: Dict[str, Any] = None) -> None:
        """保存日数据"""
        pass
    
    def load_daily_data(self, project_name: str, date: Union[date, datetime]) -> Dict[str, Any]:
        """加载日数据"""
        pass
```

#### state_extractor.py - StateExtractor类框架
```python
"""状态变量提取器"""
import numpy as np
from typing import Dict, Any, Optional
import logging

class StateExtractor:
    """从模型结果中提取状态变量"""
    
    @staticmethod
    def extract_all_states(result: 'Result') -> Dict[str, Any]:
        """提取所有状态变量"""
        pass
    
    @staticmethod
    def extract_soil_moisture(result: 'Result') -> Optional[np.ndarray]:
        """提取土壤含水量剖面"""
        pass
    
    @staticmethod
    def extract_crop_lai(result: 'Result') -> Optional[float]:
        """提取作物叶面积指数"""
        pass
    
    @staticmethod
    def extract_crop_biomass(result: 'Result') -> Optional[float]:
        """提取作物生物量"""
        pass
    
    @staticmethod
    def extract_root_depth(result: 'Result') -> Optional[float]:
        """提取根深"""
        pass
    
    @staticmethod
    def extract_groundwater_level(result: 'Result') -> Optional[float]:
        """提取地下水位"""
        pass
```

#### state_injector.py - StateInjector类框架
```python
"""状态变量注入器"""
import numpy as np
from typing import Dict, Any, Optional
import logging

class StateInjector:
    """将状态变量注入到模型输入中"""
    
    @staticmethod
    def inject_all_states(model: 'Model', states: Dict[str, Any]) -> 'Model':
        """注入所有状态变量"""
        pass
    
    @staticmethod
    def inject_soil_moisture(model: 'Model', moisture: np.ndarray) -> 'Model':
        """注入土壤含水量"""
        pass
    
    @staticmethod
    def inject_crop_state(model: 'Model', lai: Optional[float] = None,
                         biomass: Optional[float] = None,
                         root_depth: Optional[float] = None) -> 'Model':
        """注入作物状态变量"""
        pass
    
    @staticmethod
    def inject_groundwater_level(model: 'Model', gw_level: float) -> 'Model':
        """注入地下水位"""
        pass
```

### 步骤4：创建工具模块

#### utils.py - 工具函数
```python
"""HDF5工具函数"""
from typing import Any, Dict
import numpy as np

def get_variable_units(var_name: str) -> str:
    """获取变量单位"""
    units_map = {
        'soil_moisture': 'cm3/cm3',
        'lai': 'm2/m2',
        'biomass': 'kg/ha',
        'root_depth': 'cm',
        'groundwater_level': 'cm'
    }
    return units_map.get(var_name, 'unknown')

def validate_date_format(date_obj) -> bool:
    """验证日期格式"""
    pass

def create_hdf5_structure_info() -> Dict[str, Any]:
    """创建HDF5结构信息"""
    pass
```

#### exceptions.py - 异常类
```python
"""HDF5模块异常类"""

class HDF5Error(Exception):
    """HDF5模块基础异常"""
    pass

class ProjectNotFoundError(HDF5Error):
    """项目未找到异常"""
    pass

class DataNotFoundError(HDF5Error):
    """数据未找到异常"""
    pass

class InvalidDataFormatError(HDF5Error):
    """无效数据格式异常"""
    pass
```

### 步骤5：创建模块初始化文件

#### pyahc/db/hdf5/__init__.py
```python
"""pyAHC HDF5数据管理模块

提供基于HDF5的项目数据存储和管理功能，支持：
- 项目级别的数据组织
- 日循环模拟的状态传递
- 状态变量的提取和注入
- 数据同化的基础架构
"""

from .manager import ProjectHDF5Manager
from .state_extractor import StateExtractor
from .state_injector import StateInjector
from .utils import get_variable_units, validate_date_format
from .exceptions import (
    HDF5Error,
    ProjectNotFoundError,
    DataNotFoundError,
    InvalidDataFormatError
)

__version__ = "0.1.0"
__all__ = [
    "ProjectHDF5Manager",
    "StateExtractor", 
    "StateInjector",
    "get_variable_units",
    "validate_date_format",
    "HDF5Error",
    "ProjectNotFoundError",
    "DataNotFoundError",
    "InvalidDataFormatError"
]
```

## 可交付成果

### 主要交付物
1. **完整的目录结构**：pyahc/db/hdf5/模块目录
2. **核心类框架**：ProjectHDF5Manager、StateExtractor、StateInjector类定义
3. **工具模块**：utils.py和exceptions.py
4. **模块初始化**：完整的__init__.py文件
5. **依赖配置**：更新的requirements.txt或pyproject.toml

### 代码文件清单
- `pyahc/db/hdf5/__init__.py` - 模块初始化和导出
- `pyahc/db/hdf5/manager.py` - ProjectHDF5Manager类框架
- `pyahc/db/hdf5/state_extractor.py` - StateExtractor类框架
- `pyahc/db/hdf5/state_injector.py` - StateInjector类框架
- `pyahc/db/hdf5/utils.py` - 工具函数
- `pyahc/db/hdf5/exceptions.py` - 异常类定义

## 验收标准

### 功能验收
1. **导入测试**：能够成功导入所有核心类
   ```python
   from pyahc.db.hdf5 import ProjectHDF5Manager, StateExtractor, StateInjector
   ```

2. **类实例化**：能够创建核心类的实例
   ```python
   manager = ProjectHDF5Manager("test.h5")
   extractor = StateExtractor()
   injector = StateInjector()
   ```

3. **依赖检查**：确认h5py和numpy等依赖正确安装

### 质量验收
1. **代码规范**：所有代码符合PEP 8规范
2. **类型注解**：所有公共方法包含完整的类型注解
3. **文档字符串**：所有类和方法包含清晰的文档字符串
4. **异常处理**：定义了完整的异常类层次结构

### 集成验收
1. **兼容性**：与现有pyAHC架构无冲突
2. **模块结构**：符合Python包的标准结构
3. **版本控制**：所有文件正确添加到版本控制系统

## 依赖关系
- **前置依赖**：无（基础任务）
- **后续任务**：任务02（ProjectHDF5Manager实现）、任务03（StateExtractor实现）

## 风险与注意事项

### 主要风险
1. **依赖冲突**：h5py版本与现有依赖可能冲突
2. **路径问题**：模块路径可能与现有结构冲突
3. **导入问题**：循环导入或命名空间冲突

### 缓解措施
1. **版本锁定**：明确指定依赖版本范围
2. **路径测试**：在多个环境中测试导入
3. **命名规范**：使用清晰的命名避免冲突

### 注意事项
1. 保持代码简洁，框架阶段不实现具体功能
2. 确保所有类型注解正确，便于后续开发
3. 文档字符串要详细，说明每个类和方法的用途
4. 异常类要有清晰的继承关系和用途说明

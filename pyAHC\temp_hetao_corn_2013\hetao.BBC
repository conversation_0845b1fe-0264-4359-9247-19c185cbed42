********************************************************************************
* Filename: 
* Contents: AHC 1.0 - Bottom Boundary Condition 
********************************************************************************
* cGroundwater level is given
********************************************************************************
* Section 1 choose bottom boundary condition
*
* Choose one of 8 options
*
*
  SWOPT1 = 0 !  Switch, use groundwater level, [Y=1, N=0]
* 
* If SWOPT1 = 1 specify date [day month] and groundwater level [-1.E5..100 cm]:
* d1 m1  GWlevel        (maximum 366 records)
 2      5      -151.0    
 3      5      -151.0    
 4      5      -151.0    
 5      5      -151.0    
 6      5      -151.0    
 7      5      -151.0    
 8      5      -151.0    
 9      5      -151.0    
 10     5      -151.0    
 11     5      -119.3    
 12     5      -87.6     
 13     5      -56.0     
 14     5      -45.0     
 15     5      -41.5     
 16     5      -38.0     
 17     5      -34.5     
 18     5      -31.0     
 19     5      -34.67    
 20     5      -38.34    
 21     5      -42.0     
 22     5      -50.5     
 23     5      -59.0     
 24     5      -67.5     
 25     5      -76.0     
 26     5      -79.86    
 27     5      -83.72    
 28     5      -87.58    
 29     5      -91.44    
 30     5      -95.3     
 31     5      -99.16    
 1      6      -103.0    
 2      6      -114.5    
 3      6      -126.0    
 4      6      -129.0    
 5      6      -132.0    
 6      6      -135.0    
 7      6      -138.0    
 8      6      -141.0    
 9      6      -144.0    
 10     6      -147.0    
 11     6      -148.0    
 12     6      -149.0    
 13     6      -150.0    
 14     6      -151.0    
 15     6      -152.0    
 16     6      -153.0    
 17     6      -154.0    
 18     6      -154.6    
 19     6      -155.2    
 20     6      -155.8    
 21     6      -156.4    
 22     6      -157.0    
 23     6      -149.6    
 24     6      -142.2    
 25     6      -134.8    
 26     6      -127.4    
 27     6      -120.0    
 28     6      -112.0    
 29     6      -104.0    
 30     6      -86.0     
 1      7      -68.0     
 2      7      -72.5     
 3      7      -77.0     
 4      7      -87.43    
 5      7      -97.86    
 6      7      -108.29   
 7      7      -118.72   
 8      7      -129.15   
 9      7      -139.58   
 10     7      -150.0    
 11     7      -154.0    
 12     7      -158.0    
 13     7      -162.0    
 14     7      -166.0    
 15     7      -150.33   
 16     7      -134.66   
 17     7      -119.0    
 18     7      -76.0     
 19     7      -76.33    
 20     7      -76.66    
 21     7      -77.0     
 22     7      -84.25    
 23     7      -91.5     
 24     7      -98.75    
 25     7      -106.0    
 26     7      -113.25   
 27     7      -120.5    
 28     7      -127.75   
 29     7      -135.0    
 30     7      -142.25   
 31     7      -149.5    
 1      8      -156.75   
 2      8      -164.0    
 3      8      -171.25   
 4      8      -178.5    
 5      8      -185.75   
 6      8      -193.0    
 7      8      -152.0    
 8      8      -156.0    
 9      8      -136.0    
 10     8      -103.0    
 11     8      -108.79   
 12     8      -114.58   
 13     8      -120.37   
 14     8      -126.16   
 15     8      -131.95   
 16     8      -137.74   
 17     8      -143.53   
 18     8      -149.32   
 19     8      -155.11   
 20     8      -160.9    
 21     8      -166.69   
 22     8      -172.48   
 23     8      -178.27   
 24     8      -184.0    
 25     8      -186.5    
 26     8      -189.0    
 27     8      -191.5    
 28     8      -194.0    
 29     8      -196.5    
 30     8      -199.0    
 31     8      -201.5    
 1      9      -204.0    
 2      9      -206.5    
 3      9      -209.0    
 4      9      -210.0    
 5      9      -211.0    
 6      9      -212.0    
 7      9      -213.0    
 8      9      -213.43   
 9      9      -213.86   
 10     9      -214.29   
 11     9      -214.72   
 12     9      -215.15   
 13     9      -215.58   
 14     9      -216.01   
 15     9      -216.44   
 16     9      -216.87   
 17     9      -217.3    
 18     9      -217.73   
 19     9      -218.16   
 20     9      -218.59   
 21     9      -219.0    
* End of table
*
*
  SWOPT2 = 0 ! Switch, use regional bottom flux [Y=1, N=0]
*
* If SWOPT2 = 1 specify whether a sine or a table are used to prescribe the flux:
  SWC2   = 2 ! Sine function = 1, table = 2
*
* In case of a sine function, specify:
  C2AVE = 0.0 ! Average value of bottom flux [-10..10 cm/d, R, + = upwards]
  C2AMP = 0.0 ! Amplitude of bottom flux sine function [-10..10 cm/d, R]
  C2MAX = 1 ! Daynumber with maximum bottom flux, [1..366 d, I]
*
* Table - specify date [day month] and bottom flux [-10..10 cm/d, R,+ = upwards]
* d2 m2  QBottom        (maximum 366 records)
* End of table
*
*
  SWOPT3 = 0 ! Switch, calculate bottom flux from deep aquifer [Y=1, N=0]
*
  SHAPE  =  ! Shape factor to derive average groundwater level, [0..1 -, R]
  HDRAIN =  ! Mean drain base to correct for average groundwater level, [-1.E4..0 cm, R]
  RIMLAY =  ! Vertical resistance of acquitard, [0..10000 d, R]
  AQAVE  =  ! Average hydraulic head difference, [-1000..1000 cm, R]
  AQAMP  =  ! Amplitude of hydraulic head sine wave [0..1000 cm, R]
  AQTAMX =  ! first daynumber (Jan 1 = 1) with max. hydraulic head, [1..366 d, I]
  AQPER  =  ! Period of hydraulic head sine wave, [1..366 d, I]
*
*
  SWOPT4 = 0 ! Switch, calc. bottom flux as function of groundw. level, [Y=1, N=0]
*
* If SWOPT4 = 1 specify of q = A exp(Bh) relation:
  COFQHA =  ! coefficient A, [-100..100 cm/d, R]
  COFQHB =  ! coefficient B, [-1..1 /cm, R]
*
*
  SWOPT5 = 1 ! Switch, use pressure head of bottom compartment, [Y=1, N=0]
*
* If SWOPT5 = 1, specify date [day month] and bottom compartment pressure
*               head [-1.E10..1.E5 cm, negative if unsaturated]:
* d5 m5  HGIVEN       (maximum 366 records)
 2      5      144.5     
 3      5      144.5     
 4      5      144.5     
 5      5      144.5     
 6      5      144.5     
 7      5      144.5     
 8      5      144.5     
 9      5      144.5     
 10     5      144.5     
 11     5      176.2     
 12     5      207.9     
 13     5      239.5     
 14     5      250.5     
 15     5      254.0     
 16     5      257.5     
 17     5      261.0     
 18     5      264.5     
 19     5      260.83    
 20     5      257.16    
 21     5      253.5     
 22     5      245.0     
 23     5      236.5     
 24     5      228.0     
 25     5      219.5     
 26     5      215.64    
 27     5      211.78    
 28     5      207.92    
 29     5      204.06    
 30     5      200.2     
 31     5      196.34    
 1      6      192.5     
 2      6      181.0     
 3      6      169.5     
 4      6      166.5     
 5      6      163.5     
 6      6      160.5     
 7      6      157.5     
 8      6      154.5     
 9      6      151.5     
 10     6      148.5     
 11     6      147.5     
 12     6      146.5     
 13     6      145.5     
 14     6      144.5     
 15     6      143.5     
 16     6      142.5     
 17     6      141.5     
 18     6      140.9     
 19     6      140.3     
 20     6      139.7     
 21     6      139.1     
 22     6      138.5     
 23     6      145.9     
 24     6      153.3     
 25     6      160.7     
 26     6      168.1     
 27     6      175.5     
 28     6      183.5     
 29     6      191.5     
 30     6      209.5     
 1      7      227.5     
 2      7      223.0     
 3      7      218.5     
 4      7      208.07    
 5      7      197.64    
 6      7      187.21    
 7      7      176.78    
 8      7      166.35    
 9      7      155.92    
 10     7      145.5     
 11     7      141.5     
 12     7      137.5     
 13     7      133.5     
 14     7      129.5     
 15     7      145.17    
 16     7      160.84    
 17     7      176.5     
 18     7      219.5     
 19     7      219.17    
 20     7      218.84    
 21     7      218.5     
 22     7      211.25    
 23     7      204.0     
 24     7      196.75    
 25     7      189.5     
 26     7      182.25    
 27     7      175.0     
 28     7      167.75    
 29     7      160.5     
 30     7      153.25    
 31     7      146.0     
 1      8      138.75    
 2      8      131.5     
 3      8      124.25    
 4      8      117.0     
 5      8      109.75    
 6      8      102.5     
 7      8      143.5     
 8      8      139.5     
 9      8      159.5     
 10     8      192.5     
 11     8      186.71    
 12     8      180.92    
 13     8      175.13    
 14     8      169.34    
 15     8      163.55    
 16     8      157.76    
 17     8      151.97    
 18     8      146.18    
 19     8      140.39    
 20     8      134.6     
 21     8      128.81    
 22     8      123.02    
 23     8      117.23    
 24     8      111.5     
 25     8      109.0     
 26     8      106.5     
 27     8      104.0     
 28     8      101.5     
 29     8      99.0      
 30     8      96.5      
 31     8      94.0      
 1      9      91.5      
 2      9      89.0      
 3      9      86.5      
 4      9      85.5      
 5      9      84.5      
 6      9      83.5      
 7      9      82.5      
 8      9      82.07     
 9      9      81.64     
 10     9      81.21     
 11     9      80.78     
 12     9      80.35     
 13     9      79.92     
 14     9      79.49     
 15     9      79.06     
 16     9      78.63     
 17     9      78.2      
 18     9      77.77     
 19     9      77.34     
 20     9      76.91     
 21     9      76.5      
* End of table
*
*
  SWOPT6 = 0 ! Switch, bottom flux equals zero [Y=1, N=0]
*
*
  SWOPT7 = 0 ! Switch, free drainage of soil profile [Y=1, N=0]
*
*
  SWOPT8 = 0 ! Switch, free outflow at soil-air interface [Y=1, N=0]
*
********************************************************************************

********************************************************************************
* Section 2  地下水浓度变化条件下的 (cxu)
  SWVGWC = 1 !   =1, using variable gw concentration; =0, constant gw concentration.
* d1 m1  GWCTAB (g/L)        (maximum 366 records)
 2      5      2.02      
 3      5      2.02      
 4      5      2.02      
 5      5      2.02      
 6      5      2.02      
 7      5      2.02      
 8      5      2.02      
 9      5      2.02      
 10     5      2.02      
 11     5      2.23      
 12     5      2.44      
 13     5      2.65      
 14     5      2.57      
 15     5      2.49      
 16     5      2.41      
 17     5      2.33      
 18     5      2.25      
 19     5      2.17      
 20     5      2.09      
 21     5      2.04      
 22     5      2.05      
 23     5      2.05      
 24     5      2.05      
 25     5      2.05      
 26     5      2.06      
 27     5      2.06      
 28     5      2.06      
 29     5      2.07      
 30     5      2.07      
 31     5      2.07      
 1      6      2.10      
 2      6      2.09      
 3      6      2.07      
 4      6      2.07      
 5      6      2.06      
 6      6      2.06      
 7      6      2.05      
 8      6      2.05      
 9      6      2.04      
 10     6      2.04      
 11     6      2.04      
 12     6      2.04      
 13     6      2.04      
 14     6      2.03      
 15     6      2.03      
 16     6      2.03      
 17     6      2.03      
 18     6      2.03      
 19     6      2.02      
 20     6      2.02      
 21     6      2.02      
 22     6      2.02      
 23     6      2.02      
 24     6      2.01      
 25     6      2.01      
 26     6      2.01      
 27     6      2.01      
 28     6      2.01      
 29     6      2.00      
 30     6      2.00      
 1      7      2.00      
 2      7      2.00      
 3      7      1.99      
 4      7      1.99      
 5      7      1.99      
 6      7      1.99      
 7      7      1.99      
 8      7      1.98      
 9      7      1.98      
 10     7      1.98      
 11     7      1.97      
 12     7      1.97      
 13     7      1.97      
 14     7      1.97      
 15     7      1.97      
 16     7      1.97      
 17     7      1.97      
 18     7      1.97      
 19     7      1.97      
 20     7      1.96      
 21     7      1.96      
 22     7      1.96      
 23     7      1.96      
 24     7      1.96      
 25     7      1.96      
 26     7      1.96      
 27     7      1.96      
 28     7      1.96      
 29     7      1.96      
 30     7      1.95      
 31     7      1.95      
 1      8      1.95      
 2      8      1.95      
 3      8      1.95      
 4      8      1.95      
 5      8      1.95      
 6      8      1.94      
 7      8      1.96      
 8      8      1.98      
 9      8      1.99      
 10     8      2.01      
 11     8      2.03      
 12     8      2.04      
 13     8      2.03      
 14     8      2.02      
 15     8      2.01      
 16     8      2.00      
 17     8      1.99      
 18     8      1.98      
 19     8      1.97      
 20     8      1.96      
 21     8      1.95      
 22     8      1.94      
 23     8      1.93      
 24     8      1.93      
 25     8      1.93      
 26     8      1.94      
 27     8      1.94      
 28     8      1.95      
 29     8      1.96      
 30     8      1.96      
 31     8      1.97      
 1      9      1.97      
 2      9      1.98      
 3      9      1.98      
 4      9      1.97      
 5      9      1.95      
 6      9      1.93      
 7      9      1.91      
 8      9      1.90      
 9      9      1.88      
 10     9      1.86      
 11     9      1.84      
 12     9      1.83      
 13     9      1.81      
 14     9      1.79      
 15     9      1.77      
 16     9      1.76      
 17     9      1.74      
 18     9      1.72      
 19     9      1.70      
 20     9      1.69      
 21     9      1.68      
* End of table
********************************************************************************

********************************************************************************
* Section 3   choose bottom boundary condition for heat simulation
*
  SWHOPT1 = 0 !   =1, Switch, bottom flux equals zero [Y=1, N=0]
*
*
  SWHOPT2 = 1 !   =1, Switch, use Temp. of bottom compartment, [Y=1, N=0]
*
* If SWHOPT2 = 1, specify date [day month] and bottom compartment Temp
*                 [-50..50 C]:
* d6 m6  TGIVEN       (maximum 366 records)
* End of table 
********************************************************************************

********************************************************************************
* Section 4  可变热导率
*
  SWVHCS = 0 ! Switch, use variable heat conductivity of soil [Y=1, N=0]
*
* If SWVHCS = 1, specify date [day month] and heat conductivity [0..10 J/(cm.d.C)]:
* d1 m1  HCSTAB        (maximum 366 records)
* End of table
********************************************************************************

********************************************************************************
* Section 5 adjust hydrological properties for surface soil
*
  SWHPadj = 0 ! Switch, if adjust hydrological properties for surface soil [Y=1, N=0]
* 
  DEPadj =  ! depth of surface soil layer to be adjusted , [-20000 - 0 cm, R]
* If SWHPadj = 1, specify adjust coefficents [0..3.0, R] at user-specified date, maximum 20 records:
* dd mm  coethe (-)  coeks (-)        (maximum 20  records)
* End of table 
* End of file **********************************************************************
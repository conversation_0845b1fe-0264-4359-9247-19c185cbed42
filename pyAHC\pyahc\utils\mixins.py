# mypy: disable-error-code="attr-defined, no-any-return, index"
# attr-defined 错误在 wofost mixin 中调用 .update 方法时出现。
# 最简单的修复方法是检查 PyAHCBaseModel 实例，但这会创建循环导入问题，我不想处理。目前这不是一个优先修复的问题。
# no-any-return 错误在 get_origin 方法中引发。这不是一个优先修复的问题。
# 索引错误也一样；它抱怨 FieldInfo 对象不可索引，但实际上在这种情况下它应该是可索引的，因为有 Optional 或 Annotated 类型。

"""可重用的混入，增强特定 PyAHCBaseModel 的功能。

为了保持主要的 PyAHCBaseModel 类和组件库的整洁和专注，混入用于向需要额外功能的类添加功能。
混入的概念受到 Django 框架的启发，它确实有助于保持代码的整洁和组织。

如果将来一个或多个类需要更多功能，则应将其实现为混入，然后由需要它的类继承。

类：

    FileMixin: 为需要文件 I/O 的模型提供自定义保存功能。
    SerializableMixin: 将模型转换为 AHC 格式的字符串。
    YAMLValidatorMixin: YAML 验证混入类 - 参考 pySWAP-main 模式。
    ValidationDecoratorMixin: 验证装饰器混入类。
    ComprehensiveMixin: 综合混入类 - 包含所有功能。
    WOFOSTUpdateMixin: pyAHC 的 WOFOST 作物参数数据库接口。
"""

# 使用 pySWAP-main 导入别名约定，带下划线前缀
from pathlib import Path as _Path
from typing import Any as _Any, Literal as _Literal, Self as _Self, Union as _Union, get_args as _get_args, get_origin as _get_origin
from typing import Dict as _Dict, List as _List, Optional as _Optional, Callable as _Callable
from functools import wraps as _wraps
import yaml as _yaml
import logging as _logging

from pydantic import BaseModel as _BaseModel, PrivateAttr as _PrivateAttr, model_serializer as _model_serializer, model_validator as _model_validator
from pydantic.fields import FieldInfo as _FieldInfo

from pyahc.core.defaults import VALIDATIONRULES
from pyahc.log import logging

logger = logging.getLogger(__name__)


class FileMixin:
    """为需要文件 I/O 的模型提供自定义保存功能。
    
    !!! 注意：
    
            _extension 属性应在继承此混入的类中设置。建议使用 pydantic 的 PrivateAttr 来向用户隐藏此属性。
    
        方法：
            save_file: 将字符串保存到文件。
    """

    def save_file(
        self,
        string: str,
        fname: str,
        path: _Path,
    ) -> None:
        """将字符串保存到文件。
        
                现在，每个继承此混入的类都应将扩展名作为私有属性提供。
        
                参数：
                    string: 要保存到文件的字符串。
                    fname: 文件名。
                    path: 文件应保存的路径。
        """

        if not hasattr(self, "_extension"):
            msg = "必须设置 _extension 属性。"
            raise AttributeError(msg)

        ext = self._extension
        fname = f"{fname}.{ext}" if ext else fname

        with open(f"{path}/{fname}", "w", encoding="utf-8") as f:
            f.write(string)

        logger.info(f"{fname} saved successfully.")

        return None


class SerializableMixin(_BaseModel):
    """将模型转换为 AHC 格式的字符串。
    
        此混入仅由直接序列化为 AHC 格式字符串的类继承。假设继承类：
    
        - 不包含嵌套类。
        - 如果类包含嵌套类，则应使用 Subsection 字段类型或覆盖 `model_string()` 方法。
    
        方法：
            if_is_union_type: 检查字段类型是否为 Union 类型。
            is_annotated_exception_type: 检查属性类型是否为 Table、Arrays 或 ObjectList。
            serialize_model: 覆盖默认的序列化方法。
            model_string: 将字典中的格式化字符串连接成一个字符串。
    """

    def if_is_union_type(self, field_info: _FieldInfo) -> dict | None:
        """检查字段类型是否为 Union 类型。
        
                如果是，则在 Union 类型的第一个参数的 field_info 中查找 json_schema_extra 属性。
                如果未找到，则返回 None。这在例如可选类（如 Union[Table, None]）的情况下是必需的。
        
                ��数：
                    field_info (FieldInfo): 字段的 FieldInfo 对象。
        """

        field_type = field_info.annotation

        if _get_origin(field_type) is _Union:
            union_args = _get_args(field_type)
            args = _get_args(union_args[0])

            field_info = [item for item in args if isinstance(item, _FieldInfo)]

            if not field_info:
                return None

            # 只返回 json_schema_extra 属性。这在某些情况下用于从 pyahc.core.fields 模块中的序列化器向 model_dump 传递附加信息。
            return field_info[0].json_schema_extra
        return None

    def is_annotated_exception_type(self, field_name: str) -> bool:
        """检查属性类型是否为 Table、Arrays 或 ObjectList。
        
                对于 Table、Arrays 和 ObjectList 类型，返回 True，确保单独的序列化路径。
        
                首先尝试从 Union 类型分配 json_schema_extra。如果失败，则从 field_info 分配 json_schema_extra。
                如果 json_schema_extra 为 None，则返回 False。
        """
        # 每个特殊字段都将有一个 FieldInfo 对象
        field_info = self.model_fields.get(field_name, None)

        if field_info is None:
            return False

        json_schema_extra = (
            self.if_is_union_type(field_info) or field_info.json_schema_extra
        )

        if json_schema_extra is None:
            return False

        return json_schema_extra.get("is_annotated_exception_type", False)

    @_model_serializer(when_used="json", mode="wrap")
    def serialize_model(self, handler: _Any):
        """覆盖默认的序列化方法。
        
                在中间步骤中，创建一个包含 AHC 格式字符串的字典。
        """
        result = {}
        validated_self = handler(self)
        for field_name, field_value in validated_self.items():
            if self.is_annotated_exception_type(field_name):
                result[field_name] = field_value
            else:
                result[field_name] = f"{field_name.upper()} = {field_value}"
        return result

    def model_string(
        self, mode: _Literal["str", "list"] = "string", **kwargs
    ) -> str | list[str]:
        """将字典中的格式化字符串连接成一个字符串。
        
        
                !!! 注意：
                    By alias 为 True，因为在某些情况下，特别是 CropSettings，数据库中参数的 WOFOST 名���与 AHC 中使用的名称不同。
                    这允许这些参数正确匹配，但在 AHC 输入文件中正确序列化。
        
                参数：
                    mode (Literal["str", "list]): 输出格式。
                    kwargs (dict): 传递给 `model_dump()` 的附加关键字参数。
        """
        dump = self.model_dump(
            mode="json", exclude_none=True, by_alias=True, **kwargs
        ).values()

        if mode == "list":
            return list(dump)
        else:
            return "\n".join(dump)



class YAMLValidatorMixin(_BaseModel):
    """YAML 验证混入类 - 参考 pySWAP-main 模式"""

    _validation: bool = _PrivateAttr(default=False)
    _validation_rules: _Dict[str, _Any] = _PrivateAttr(default_factory=dict)
    _validation_errors: _List[str] = _PrivateAttr(default_factory=list)
    _validation_warnings: _List[str] = _PrivateAttr(default_factory=list)

    def __init__(self, **data):
        super().__init__(**data)
        self._load_validation_rules()

    def _load_validation_rules(self):
        """加载验证规则"""
        try:
            rules_path = _Path(__file__).parent.parent / "core" / "validation.yaml"
            if rules_path.exists():
                with open(rules_path, 'r', encoding='utf-8') as f:
                    all_rules = _yaml.safe_load(f)
                    self._validation_rules = all_rules.get(self.__class__.__name__, {})
                    logger.debug(f"Loaded validation rules for {self.__class__.__name__}")
            else:
                logger.warning(f"Validation rules file not found: {rules_path}")
        except Exception as e:
            logger.error(f"Error loading validation rules: {e}")

    @_model_validator(mode="after")
    def validate_with_yaml(self) -> "YAMLValidatorMixin":
        """使用 YAML 规则进行验证"""
        if not self._validation:
            return self

        self._validation_errors.clear()
        self._validation_warnings.clear()

        # 执行条件参数验证
        self._validate_conditional_parameters()

        # 执行参数范围验证
        self._validate_parameter_ranges()

        # 执行自定义验证规则
        self._validate_custom_rules()

        # 如果有错误，抛出异常
        if self._validation_errors:
            error_msg = "; ".join(self._validation_errors)
            raise ValueError(f"Validation failed: {error_msg}")

        return self

    def _validate_conditional_parameters(self):
        """验证条件参数"""
        for switch_name, switch_rules in self._validation_rules.items():
            if not isinstance(switch_rules, dict):
                continue

            switch_value = getattr(self, switch_name, None)
            if switch_value is None:
                continue

            if switch_value in switch_rules:
                rule_set = switch_rules[switch_value]

                # 检查必需参数
                required_params = rule_set.get('required', []) if isinstance(rule_set, dict) else rule_set
                for param in required_params:
                    if not hasattr(self, param) or getattr(self, param) is None:
                        self._validation_errors.append(
                            f"When {switch_name}={switch_value}, parameter '{param}' is required"
                        )

                # 执行验证规则（如果规则集是字典且包含验证规则）
                if isinstance(rule_set, dict):
                    validation_rules = rule_set.get('validation', [])
                    for rule in validation_rules:
                        try:
                            # 创建验证上下文
                            context = {attr: getattr(self, attr, None) for attr in dir(self)
                                     if not attr.startswith('_')}

                            # 创建安全的内置函数环境
                            safe_builtins = {
                                'len': len,
                                'all': all,
                                'any': any,
                                'isinstance': isinstance,
                                'int': int,
                                'float': float,
                                'str': str,
                                'bool': bool,
                                'tuple': tuple,
                                'list': list,
                                'dict': dict,
                                'min': min,
                                'max': max,
                                'abs': abs,
                                'round': round
                            }

                            # 执行验证表达式
                            if not eval(rule, {"__builtins__": safe_builtins}, context):
                                self._validation_errors.append(
                                    f"Validation rule failed: {rule}"
                                )
                        except Exception as e:
                            self._validation_errors.append(
                                f"Error evaluating validation rule '{rule}': {e}"
                            )

    def _validate_parameter_ranges(self):
        """验证参数范围"""
        try:
            from ..core.valueranges import validate_range

            # 获取参数外部化配置
            param_config = self._validation_rules.get('ParameterExternalization', {})
            configurable_params = param_config.get('configurable_params', [])

            for param_info in configurable_params:
                param_name = param_info.get('name')
                param_range = param_info.get('range')

                if param_name and param_range and hasattr(self, param_name):
                    value = getattr(self, param_name)
                    if value is not None:
                        min_val, max_val = param_range
                        if not (min_val <= value <= max_val):
                            self._validation_errors.append(
                                f"Parameter '{param_name}' value {value} is outside range [{min_val}, {max_val}]"
                            )
        except ImportError:
            logger.warning("valueranges module not available for range validation")
        except Exception as e:
            logger.warning(f"Error in parameter range validation: {e}")

    def _validate_custom_rules(self):
        """验证自定义规则"""
        # 执行模型特定的验证方法（只调用不需要参数的方法）
        validation_methods = [method for method in dir(self)
                            if method.startswith('validate_') and
                            callable(getattr(self, method)) and
                            method not in ['validate_with_yaml', 'validate_component_dependencies', 'validate_parameter_consistency']]

        for method_name in validation_methods:
            try:
                method = getattr(self, method_name)
                # 检查方法是否需要参数（除了 self）
                import inspect
                sig = inspect.signature(method)
                if len(sig.parameters) == 0:  # 只有 self 参数
                    method()
            except Exception as e:
                self._validation_errors.append(f"{method_name}: {str(e)}")

    def validate_component_dependencies(self, dependencies: _Dict[str, _List[str]] = None):
        """验证组件依赖关系"""
        if not self._validation:
            return

        if dependencies is None:
            # 如果没有提供依赖关系，跳过验证
            return

        for component_name, required_attrs in dependencies.items():
            component = getattr(self, component_name, None)
            if component is None:
                self._validation_errors.append(f"Required component {component_name} is missing")
                continue

            for attr in required_attrs:
                if not hasattr(component, attr) or getattr(component, attr) is None:
                    self._validation_errors.append(
                        f"Required attribute {attr} in component {component_name} is missing"
                    )

    def validate_parameter_consistency(self, param_groups: _Dict[str, _List[str]] = None):
        """验证参数一致性"""
        if not self._validation:
            return

        if param_groups is None:
            # 如果没有提供参数组，跳过验证
            return

        for group_name, params in param_groups.items():
            values = [getattr(self, param, None) for param in params]
            if any(v is not None for v in values) and any(v is None for v in values):
                self._validation_errors.append(
                    f"Parameter group {group_name} requires all or none: {params}"
                )

    def get_validation_errors(self) -> _List[str]:
        """获取验证错误列表"""
        return self._validation_errors.copy()

    def get_validation_warnings(self) -> _List[str]:
        """获取验证警告列表"""
        return self._validation_warnings.copy()

    def clear_validation_messages(self):
        """清除验证消息"""
        self._validation_errors.clear()
        self._validation_warnings.clear()

    def enable_validation(self):
        """启用验证模式"""
        self._validation = True

    def disable_validation(self):
        """禁用验证模式"""
        self._validation = False

    def is_validation_enabled(self) -> bool:
        """检查验证是否启用"""
        return self._validation


class ValidationDecoratorMixin:
    """验证装饰器混入类"""

    @staticmethod
    def conditional_validation(conditions: _Dict[str, _Dict[_Any, _List[str]]]):
        """条件验证装饰器"""
        def decorator(func: _Callable) -> _Callable:
            @_wraps(func)
            def wrapper(self, *args, **kwargs):
                if hasattr(self, '_validation') and self._validation:
                    for switch_name, switch_conditions in conditions.items():
                        switch_value = getattr(self, switch_name, None)
                        if switch_value is not None and switch_value in switch_conditions:
                            required_params = switch_conditions[switch_value]
                            missing_params = []

                            for param in required_params:
                                if not hasattr(self, param) or getattr(self, param) is None:
                                    missing_params.append(param)

                            if missing_params:
                                raise ValueError(
                                    f"When {switch_name}={switch_value}, the following parameters are required: {missing_params}"
                                )

                return func(self, *args, **kwargs)
            return wrapper
        return decorator

    @staticmethod
    def range_validation(param_ranges: _Dict[str, tuple]):
        """范围验证装饰器"""
        def decorator(func: _Callable) -> _Callable:
            @_wraps(func)
            def wrapper(self, *args, **kwargs):
                if hasattr(self, '_validation') and self._validation:
                    for param_name, (min_val, max_val) in param_ranges.items():
                        if hasattr(self, param_name):
                            value = getattr(self, param_name)
                            if value is not None and not (min_val <= value <= max_val):
                                raise ValueError(
                                    f"Parameter {param_name} value {value} is outside range [{min_val}, {max_val}]"
                                )

                return func(self, *args, **kwargs)
            return wrapper
        return decorator


class WOFOSTUpdateMixin:
    """pyAHC 的 WOFOST 作物参数数据库接口。
    
            此混入应由与 WOFOST 作物数据库共享参数的类继承。
    """

    def update_from_wofost(self) -> None:
        """使用 WOFOST 品种设置更新模型。"""
        # parameters 属性返回一个包含键值对和列表的字典。在更新之前，应该创建表格。
        if not hasattr(self, "wofost_variety"):
            msg = "模型没有 WOFOST 品种设置。"
            raise AttributeError(msg)

        variety_params = self.wofost_variety.parameters
        # 直接使用 WOFOST 品种参数更新模型
        self.update(variety_params, inplace=True)


# 组合混入类
class ComprehensiveMixin(YAMLValidatorMixin, FileMixin, SerializableMixin, ValidationDecoratorMixin):
    """综合混入类 - 包含所有功能"""
    pass

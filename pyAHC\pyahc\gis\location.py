# mypy: disable-error-code="no-any-unimported"
# 此错误是由于缺少 shapely 的存根。
"""Location 类及相关功能。

类：
    Location: 表示位置的通用类。
"""

from decimal import Decimal

from pydantic import Field, field_validator
from pyproj import CRS, Transformer
from shapely.geometry import Point

from pyahc.core.basemodel import PyAHCBaseModel


class Location(PyAHCBaseModel):
    """表示位置的通用类。

    此类可以表示包中使用的任何位置（例如，气象站、模型土壤柱等）。

    属性：
        lon (Decimal): 点经度。
        lat (Decimal): 点纬度。
        alt (Decimal): 点海拔（米）。
        crs (string): 输入坐标的坐标参考系统。

    属性：
        point: 返回一个 Shapely Point 对象。

    方法：
        to_crs: 将位置转换为新的坐标参考系统。
    """

    lon: Decimal
    lat: Decimal
    alt: Decimal
    crs: str = Field(default="EPSG:4326", exclude=True)

    @property
    def point(self) -> Point:
        """返回一个 Shapely Point 对象"""
        return Point(self.lon, self.lat, self.alt)

    @field_validator("crs")
    def validate_crs(cls, v):
        try:
            CRS.from_user_input(v)
        except Exception:
            msg = f"Invalid CRS: {v}"
            raise ValueError(msg) from None
        else:
            return v

    def to_crs(self, target_crs: str) -> "Location":
        """将位置转换为新的坐标参考系统。

        参数：
            target_crs (str): 目标 CRS。

        返回：
            Location: 具有转换后坐标的新 Location 对象。
        """

        CRS.from_user_input(target_crs)

        transformer = Transformer.from_crs(self.crs, target_crs, always_xy=True)

        new_lon, new_lat, new_alt = transformer.transform(self.lon, self.lat, self.alt)

        return self.model_copy(
            update={"lon": new_lon, "lat": new_lat, "alt": new_alt, "crs": target_crs},
            deep=True,
        )

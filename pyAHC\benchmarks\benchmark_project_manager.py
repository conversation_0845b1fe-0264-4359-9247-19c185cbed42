"""ProjectHDF5Manager性能基准测试

测试ProjectHDF5Manager的性能指标：
1. 存储效率（压缩率）
2. 读写速度
3. 内存使用
4. 大数据集处理能力
"""

import time
import psutil
import numpy as np
import tempfile
from pathlib import Path
from datetime import date, timedelta
from typing import Dict, Any, List, Tuple
import logging

from pyahc.db.hdf5 import ProjectHDF5Manager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PerformanceBenchmark:
    """性能基准测试类"""
    
    def __init__(self):
        self.results = {}
        self.temp_files = []
    
    def cleanup(self):
        """清理临时文件"""
        for file_path in self.temp_files:
            if file_path.exists():
                file_path.unlink()
    
    def create_large_dataset(self, size_mb: int = 10) -> Dict[str, Any]:
        """创建大型数据集用于测试"""
        # 计算数组大小以达到目标MB
        target_bytes = size_mb * 1024 * 1024
        array_size = target_bytes // (8 * 4)  # 假设float64，4个数组
        
        return {
            'soil_moisture_profile': np.random.random(array_size).astype(np.float64),
            'temperature_profile': np.random.random(array_size).astype(np.float64) * 40 - 10,
            'hydraulic_conductivity': np.random.random(array_size).astype(np.float64) * 100,
            'root_density': np.random.random(array_size).astype(np.float64)
        }
    
    def create_mock_objects(self, complexity: str = 'medium') -> Tuple[Any, Any]:
        """创建模拟对象"""
        class MockModel:
            def __init__(self, complexity: str):
                self.name = "BenchmarkModel"
                if complexity == 'simple':
                    self.data = np.random.random(100)
                elif complexity == 'medium':
                    self.data = np.random.random(10000)
                else:  # complex
                    self.data = np.random.random(100000)
                self.parameters = {f'param_{i}': np.random.random() for i in range(50)}
        
        class MockResult:
            def __init__(self, complexity: str):
                self.name = "BenchmarkResult"
                if complexity == 'simple':
                    self.output_data = np.random.random(100)
                elif complexity == 'medium':
                    self.output_data = np.random.random(10000)
                else:  # complex
                    self.output_data = np.random.random(100000)
                self.statistics = {f'stat_{i}': np.random.random() for i in range(20)}
        
        return MockModel(complexity), MockResult(complexity)
    
    def benchmark_compression_ratio(self) -> Dict[str, float]:
        """测试压缩率"""
        logger.info("测试压缩率...")
        
        with tempfile.NamedTemporaryFile(suffix='.h5', delete=False) as f:
            temp_path = Path(f.name)
        self.temp_files.append(temp_path)
        
        # 创建测试数据
        large_states = self.create_large_dataset(50)  # 50MB数据
        model, result = self.create_mock_objects('complex')
        
        with ProjectHDF5Manager(temp_path, mode='w') as manager:
            manager.create_project('compression_test', {'test': 'compression'})
            
            # 保存数据
            manager.save_daily_data('compression_test', date(2013, 6, 1), 
                                  model, result, large_states)
        
        # 计算压缩率
        file_size_mb = temp_path.stat().st_size / (1024 * 1024)
        original_size_mb = 50 + 10  # 大约60MB原始数据
        compression_ratio = (1 - file_size_mb / original_size_mb) * 100
        
        logger.info(f"原始数据大小: {original_size_mb:.1f} MB")
        logger.info(f"压缩后大小: {file_size_mb:.1f} MB")
        logger.info(f"压缩率: {compression_ratio:.1f}%")
        
        return {
            'original_size_mb': original_size_mb,
            'compressed_size_mb': file_size_mb,
            'compression_ratio_percent': compression_ratio
        }
    
    def benchmark_write_speed(self, num_days: int = 100) -> Dict[str, float]:
        """测试写入速度"""
        logger.info(f"测试写入速度 ({num_days} 天数据)...")
        
        with tempfile.NamedTemporaryFile(suffix='.h5', delete=False) as f:
            temp_path = Path(f.name)
        self.temp_files.append(temp_path)
        
        # 准备测试数据
        states = self.create_large_dataset(5)  # 5MB per day
        model, result = self.create_mock_objects('medium')
        
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        with ProjectHDF5Manager(temp_path, mode='w') as manager:
            manager.create_project('speed_test', {'test': 'write_speed'})
            
            start_date = date(2013, 5, 1)
            for i in range(num_days):
                current_date = start_date + timedelta(days=i)
                manager.save_daily_data('speed_test', current_date, 
                                      model, result, states)
        
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        total_time = end_time - start_time
        days_per_second = num_days / total_time
        memory_increase = end_memory - start_memory
        
        logger.info(f"写入 {num_days} 天数据耗时: {total_time:.2f} 秒")
        logger.info(f"写入速度: {days_per_second:.2f} 天/秒")
        logger.info(f"内存增长: {memory_increase:.1f} MB")
        
        return {
            'total_time_seconds': total_time,
            'days_per_second': days_per_second,
            'memory_increase_mb': memory_increase,
            'avg_time_per_day_ms': (total_time / num_days) * 1000
        }
    
    def benchmark_read_speed(self, num_days: int = 100) -> Dict[str, float]:
        """测试读取速度"""
        logger.info(f"测试读取速度 ({num_days} 天数据)...")
        
        # 首先创建测试数据
        with tempfile.NamedTemporaryFile(suffix='.h5', delete=False) as f:
            temp_path = Path(f.name)
        self.temp_files.append(temp_path)
        
        states = self.create_large_dataset(5)  # 5MB per day
        model, result = self.create_mock_objects('medium')
        
        # 写入测试数据
        with ProjectHDF5Manager(temp_path, mode='w') as manager:
            manager.create_project('read_test', {'test': 'read_speed'})
            
            start_date = date(2013, 5, 1)
            for i in range(num_days):
                current_date = start_date + timedelta(days=i)
                manager.save_daily_data('read_test', current_date, 
                                      model, result, states)
        
        # 测试读取速度
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        with ProjectHDF5Manager(temp_path, mode='r') as manager:
            start_date = date(2013, 5, 1)
            for i in range(num_days):
                current_date = start_date + timedelta(days=i)
                data = manager.load_daily_data('read_test', current_date)
                # 验证数据完整性
                assert 'soil_moisture_profile' in data['states']
        
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        total_time = end_time - start_time
        days_per_second = num_days / total_time
        memory_increase = end_memory - start_memory
        
        logger.info(f"读取 {num_days} 天数据耗时: {total_time:.2f} 秒")
        logger.info(f"读取速度: {days_per_second:.2f} 天/秒")
        logger.info(f"内存增长: {memory_increase:.1f} MB")
        
        return {
            'total_time_seconds': total_time,
            'days_per_second': days_per_second,
            'memory_increase_mb': memory_increase,
            'avg_time_per_day_ms': (total_time / num_days) * 1000
        }
    
    def benchmark_memory_usage(self) -> Dict[str, float]:
        """测试内存使用"""
        logger.info("测试内存使用...")
        
        with tempfile.NamedTemporaryFile(suffix='.h5', delete=False) as f:
            temp_path = Path(f.name)
        self.temp_files.append(temp_path)
        
        # 监控内存使用
        memory_samples = []
        
        def sample_memory():
            return psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        initial_memory = sample_memory()
        memory_samples.append(initial_memory)
        
        # 创建大量数据
        large_states = self.create_large_dataset(100)  # 100MB
        model, result = self.create_mock_objects('complex')
        
        memory_samples.append(sample_memory())
        
        with ProjectHDF5Manager(temp_path, mode='w') as manager:
            manager.create_project('memory_test', {'test': 'memory'})
            memory_samples.append(sample_memory())
            
            # 保存多天数据
            for i in range(10):
                current_date = date(2013, 5, 1) + timedelta(days=i)
                manager.save_daily_data('memory_test', current_date, 
                                      model, result, large_states)
                memory_samples.append(sample_memory())
        
        final_memory = sample_memory()
        memory_samples.append(final_memory)
        
        max_memory = max(memory_samples)
        memory_increase = max_memory - initial_memory
        
        logger.info(f"初始内存: {initial_memory:.1f} MB")
        logger.info(f"最大内存: {max_memory:.1f} MB")
        logger.info(f"内存增长: {memory_increase:.1f} MB")
        
        return {
            'initial_memory_mb': initial_memory,
            'max_memory_mb': max_memory,
            'memory_increase_mb': memory_increase,
            'memory_samples': memory_samples
        }
    
    def run_all_benchmarks(self) -> Dict[str, Any]:
        """运行所有基准测试"""
        logger.info("开始性能基准测试...")
        
        try:
            self.results['compression'] = self.benchmark_compression_ratio()
            self.results['write_speed'] = self.benchmark_write_speed(100)
            self.results['read_speed'] = self.benchmark_read_speed(100)
            self.results['memory_usage'] = self.benchmark_memory_usage()
            
            # 生成总结报告
            self.generate_report()
            
            return self.results
            
        finally:
            self.cleanup()
    
    def generate_report(self):
        """生成性能报告"""
        logger.info("=== 性能基准测试报告 ===")
        
        # 压缩性能
        comp = self.results['compression']
        logger.info(f"压缩率: {comp['compression_ratio_percent']:.1f}% "
                   f"(目标: >50%)")
        
        # 写入性能
        write = self.results['write_speed']
        logger.info(f"写入速度: {write['avg_time_per_day_ms']:.1f} ms/天 "
                   f"(目标: <1000ms)")
        
        # 读取性能
        read = self.results['read_speed']
        logger.info(f"读取速度: {read['avg_time_per_day_ms']:.1f} ms/天 "
                   f"(目标: <1000ms)")
        
        # 内存使用
        memory = self.results['memory_usage']
        logger.info(f"内存增长: {memory['memory_increase_mb']:.1f} MB")
        
        # 验收标准检查
        logger.info("=== 验收标准检查 ===")
        
        compression_pass = comp['compression_ratio_percent'] >= 50
        write_speed_pass = write['avg_time_per_day_ms'] <= 1000
        read_speed_pass = read['avg_time_per_day_ms'] <= 1000
        memory_pass = memory['memory_increase_mb'] <= 500  # 合理的内存使用
        
        logger.info(f"压缩率测试: {'PASS' if compression_pass else 'FAIL'}")
        logger.info(f"写入速度测试: {'PASS' if write_speed_pass else 'FAIL'}")
        logger.info(f"读取速度测试: {'PASS' if read_speed_pass else 'FAIL'}")
        logger.info(f"内存使用测试: {'PASS' if memory_pass else 'FAIL'}")
        
        all_pass = all([compression_pass, write_speed_pass, read_speed_pass, memory_pass])
        logger.info(f"总体评估: {'PASS' if all_pass else 'FAIL'}")


def main():
    """主函数"""
    benchmark = PerformanceBenchmark()
    
    try:
        results = benchmark.run_all_benchmarks()
        return results
    except Exception as e:
        logger.error(f"基准测试失败: {e}")
        raise
    finally:
        benchmark.cleanup()


if __name__ == "__main__":
    main()

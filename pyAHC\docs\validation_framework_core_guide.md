# pyAHC 验证框架核心指南

## 概述

pyAHC 验证框架核心提供了统一的验证管理系统，实现了完整的验证生命周期管理。该框架基于 pySWAP-main 的验证模式，使用下划线导入前缀，提供了强大而灵活的验证功能。

## 核心组件

### 1. ValidationManager - 基础验证管理器

基础验证管理器提供了核心的验证功能：

```python
from pyahc.core.validation_framework import ValidationManager

# 创建验证管理器
manager = ValidationManager()

# 验证单个组件
report = manager.validate_component(component)

# 验证完整模型
components = {'Component1': comp1, 'Component2': comp2}
model_report = manager.validate_model(components)

# 生成验证报告
report_content = manager.generate_report()
```

#### 主要功能：
- 单组件验证
- 多组件模型验证
- 组件间依赖关系验证
- 验证报告生成
- 验证摘要统计

### 2. AdvancedValidationManager - 高级验证管理器

高级验证管理器扩展了基础功能，添加了缓存和批量验证：

```python
from pyahc.core.validation_framework import AdvancedValidationManager

# 创建高级验证管理器
manager = AdvancedValidationManager()

# 带缓存的验证
result = manager.validate_with_cache(component)

# 批量验证
components = [comp1, comp2, comp3]
results = manager.batch_validate(components)

# 清除缓存
manager.clear_cache()
```

#### 主要功能：
- 验证结果缓存
- 批量验证处理
- 性能优化
- 缓存管理

### 3. EnhancedValidationManager - 增强验证管理器

增强验证管理器集成了插件系统，提供最全面的验证功能：

```python
from pyahc.core.validation_framework import EnhancedValidationManager

# 创建增强验证管理器
manager = EnhancedValidationManager()

# 使用插件验证组件
report = manager.validate_component_with_plugins(component)

# 注册自定义插件
manager.register_plugin(custom_plugin)
```

#### 主要功能：
- 插件系统集成
- 增强验证报告
- 自定义验证逻辑
- 物理一致性检查

## 验证上下文管理

### ValidationContext - 验证上下文管理器

验证上下文管理器允许临时控制组件的验证状态：

```python
from pyahc.core.validation_framework import ValidationContext

# 在上下文中启用验证
with ValidationContext([component1, component2], enable_validation=True):
    # 在此上下文中，验证被启用
    result = some_operation()
# 退出上下文后，验证状态自动恢复
```

#### 使用场景：
- 临时启用/禁用验证
- 批量操作的验证控制
- 测试环境的验证管理

## 插件系统

### ValidationPlugin - 验证插件基类

创建自定义验证插件：

```python
from pyahc.core.validation_framework import ValidationPlugin

class CustomValidationPlugin(ValidationPlugin):
    def get_plugin_name(self) -> str:
        return "CustomValidator"
    
    def get_supported_components(self) -> list:
        return ["MyComponent"]
    
    def validate(self, component) -> dict:
        result = {'status': 'passed', 'errors': [], 'warnings': []}
        
        # 实现自定义验证逻辑
        if hasattr(component, 'my_param'):
            if component.my_param < 0:
                result['errors'].append("my_param must be >= 0")
                result['status'] = 'failed'
        
        return result
```

### 内置插件

#### PhysicalConsistencyPlugin - 物理一致性验证插件

自动验证参数的物理一致性：

```python
# 自动注册到 EnhancedValidationManager
manager = EnhancedValidationManager()
# PhysicalConsistencyPlugin 已自动注册

# 支持的验证：
# - BottomBoundary: 地下水位与排水参数一致性
# - EpicCrop: 温度参数合理性
# - 其他物理参数验证
```

## 性能分析

### ValidationProfiler - 验证性能分析器

分析验证框架的性能：

```python
from pyahc.tools.validation_profiler import ValidationProfiler

profiler = ValidationProfiler()

# 分析组件验证性能
result = profiler.profile_component_validation(
    ComponentClass, config, iterations=100
)

# 分析缓存性能
cache_result = profiler.profile_cache_performance(
    ComponentClass, config, iterations=100
)

# 分析插件性能
plugin_result = profiler.profile_plugin_performance(
    ComponentClass, config, iterations=50
)

# 生成性能报告
report = profiler.generate_performance_report([result, cache_result])
profiler.save_report(report, "performance_report.md")
```

## 验证报告格式

### 基础验证报告

```python
{
    'component': 'ComponentName',
    'status': 'passed',  # 或 'failed'
    'errors': [],        # 错误列表
    'warnings': []       # 警告列表
}
```

### 增强验证报告

```python
{
    'component': 'ComponentName',
    'status': 'passed',
    'errors': [],
    'warnings': [],
    'plugin_results': [  # 插件验证结果
        {
            'plugin': 'PluginName',
            'status': 'passed',
            'errors': [],
            'warnings': []
        }
    ]
}
```

### 模型验证报告

```python
{
    'model_status': 'passed',
    'component_reports': {
        'Component1': {...},
        'Component2': {...}
    },
    'cross_component_errors': []  # 组件间验证错误
}
```

## 最佳实践

### 1. 选择合适的验证管理器

- **ValidationManager**: 基础验证需求
- **AdvancedValidationManager**: 需要缓存和批量处理
- **EnhancedValidationManager**: 需要插件系统和高级功能

### 2. 验证策略

```python
# 开发阶段：启用详细验证
with ValidationContext(components, enable_validation=True):
    model = create_model()

# 生产阶段：选择性验证
manager = AdvancedValidationManager()
critical_components = [boundary, crop]
results = manager.batch_validate(critical_components)
```

### 3. 性能优化

```python
# 使用缓存减少重复验证
manager = AdvancedValidationManager()
for component in similar_components:
    result = manager.validate_with_cache(component)

# 批量验证提高效率
results = manager.batch_validate(components)
```

### 4. 自定义插件开发

```python
class DomainSpecificPlugin(ValidationPlugin):
    def get_plugin_name(self) -> str:
        return "DomainValidator"
    
    def validate(self, component) -> dict:
        # 实现领域特定的验证逻辑
        return validation_result
    
    def get_supported_components(self) -> list:
        return ["DomainComponent"]

# 注册插件
manager = EnhancedValidationManager()
manager.register_plugin(DomainSpecificPlugin())
```

## 集成示例

### 完整的验证工作流

```python
from pyahc.core.validation_framework import EnhancedValidationManager
from pyahc.core.validation_framework import ValidationContext

# 1. 创建验证管理器
manager = EnhancedValidationManager()

# 2. 创建组件
components = {
    'boundary': create_boundary_component(),
    'crop': create_crop_component(),
    'soil': create_soil_component()
}

# 3. 在验证上下文中执行验证
with ValidationContext(list(components.values()), enable_validation=True):
    # 验证完整模型
    model_report = manager.validate_model(components)
    
    # 检查验证结果
    if model_report['model_status'] == 'failed':
        print("模型验证失败:")
        for comp_name, comp_report in model_report['component_reports'].items():
            if comp_report['status'] == 'failed':
                print(f"  {comp_name}: {comp_report['errors']}")
    
    

# 4. 性能分析（可选）
from pyahc.tools.validation_profiler import ValidationProfiler

profiler = ValidationProfiler()
for comp_name, component in components.items():
    perf_result = profiler.profile_component_validation(
        type(component), component.model_dump(), iterations=10
    )
    print(f"{comp_name} 验证性能: {perf_result['validation_time']['mean']:.6f}s")
```

## 故障排除

### 常见问题

1. **验证缓存问题**: 使用 `clear_cache()` 清除缓存
2. **插件冲突**: 检查插件的 `get_supported_components()` 返回值
3. **性能问题**: 使用 ValidationProfiler 分析性能瓶颈
4. **依赖关系错误**: 检查 validation.yaml 中的 ComponentDependencies 配置

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 检查验证规则加载
manager = ValidationManager()
print(f"加载的验证规则: {len(manager.validation_rules)}")

# 分析验证时间
import time
start_time = time.time()
result = manager.validate_component(component)
print(f"验证耗时: {time.time() - start_time:.6f}s")
```

## 总结

pyAHC 验证框架核心提供了完整的验证管理解决方案，支持从基础验证到高级插件系统的全方位功能。通过合理使用不同的验证管理器和工具，可以构建高效、可靠的验证工作流，确保 pyAHC 模型的质量和一致性。

# pyAHC 验证规则说明

## 概述

pyAHC 验证规则系统基于 YAML 配置文件，提供了灵活且强大的参数验证机制。该系统支持：

- 组件级验证规则
- 条件验证规则（基于开关参数）
- 组件间依赖关系验证
- 参数外部化配置
- 与 pySWAP-main 兼容的规则结构

## 验证规则结构

### 组件级验证规则

每个组件的验证规则包含以下部分：

```yaml
ComponentName:
  switch_parameter:
    value1:  # 当开关参数为 value1 时
      required: [param1, param2]  # 必需参数列表
      validation: ["param1 > 0", "param2 < 100"]  # 验证表达式列表
    value2:  # 当开关参数为 value2 时
      required: [param3, param4]
      validation: ["param3 is not None"]
```

### 条件验证规则

基于开关参数的条件验证，例如：

```yaml
BottomBoundary:
  swbotb:
    1:  # 地下水位选项
      required:
        - gwlevel_data
      validation:
        - "len(gwlevel_data) > 0"
    3:  # 深层含水层选项
      required:
        - shape
        - hdrain
        - rimlay
      validation:
        - "shape > 0.0 and shape <= 10.0"
        - "hdrain < 0.0"
```

## 验证表达式语法

支持的验证表达式类型：

### 数值比较
- `param > 0` - 大于零
- `param <= 100` - 小于等于100
- `param >= 0.0 and param <= 1.0` - 范围检查

### 类型检查
- `isinstance(param, int)` - 整数类型检查
- `isinstance(param, tuple)` - 元组类型检查
- `all(isinstance(item, tuple) for item in param_list)` - 列表元素类型检查

### 长度检查
- `len(param) > 0` - 非空检查
- `len(param) == expected_count` - 长度匹配

### 存在性检查
- `param is not None` - 非空值检查
- `param is not None and len(param) > 0` - 非空字符串检查

### 复合条件
- `param > 0 and param < 100` - 多条件组合
- `param in [0, 1]` - 枚举值检查

## 组件间依赖关系

配置组件间的依赖关系：

```yaml
ComponentDependencies:
  ComponentA:
    depends_on:
      - component: "ComponentB"
        conditions: 
          - "param1 is not None"
          - "param2 > 0"
```

### 示例：EpicCrop 依赖 CtrFile

```yaml
ComponentDependencies:
  EpicCrop:
    depends_on:
      - component: "CtrFile"
        conditions:
          - "project is not None"
          - "ssrun is not None"
          - "esrun is not None"
```

## 参数外部化配置

支持将硬编码参数外部化为可配置参数：

```yaml
ParameterExternalization:
  ComponentName:
    configurable_params:
      - name: "param_name"
        default: 1.0
        range: [0.1, 10.0]
        description: "参数描述"
```

### 示例：BottomBoundary 参数外部化

```yaml
ParameterExternalization:
  BottomBoundary:
    configurable_params:
      - name: "shape"
        default: 1.0
        range: [0.1, 10.0]
        description: "形状因子"
      - name: "hdrain"
        default: -120.0
        range: [-1000.0, 0.0]
        description: "排水基础"
```

## AHC 特定验证规则

### BottomBoundary 组件

支持基于 `swbotb` 开关的条件验证：

- **swbotb=1**: 需要 `gwlevel_data` 参数
- **swbotb=3**: 需要深层含水层参数（shape, hdrain, rimlay等）
- **swbotb=4**: 需要地下水位函数参数（cofqha, cofqhb, cofqhc）

### EpicCrop 组件

支持基于 `swcf` 和 `swrsc` 开关的条件验证：

- **swcf=1**: 需要 `cftb` 作物因子表
- **swcf=2**: 需要 `chtb` 作物高度表和 `cfini` 初始高度
- **swrsc=1**: 需要固定表面阻力参数
- **swrsc=2**: 需要可变表面阻力参数

### ObservationPoints 组件

验证观测点深度数据的合理性：

- 观测点数量在合理范围内（1-100）
- 深度数据格式正确
- 上边界小于下边界
- 深度值为非负数

## 使用方法

### 在组件中启用验证

```python
from pyahc.components.boundary import BottomBoundary

# 创建组件实例
boundary = BottomBoundary(
    swbbcfile=1,
    bbcfil="test",
    swbotb=3,
    shape=1.5,
    hdrain=-150.0,
    rimlay=600.0
)

# 启用验证
boundary.enable_validation()

# 验证将在模型操作时自动执行
```

### 手动验证

```python
try:
    boundary.validate_conditional_parameters()
    print("验证通过")
except ValueError as e:
    print(f"验证失败: {e}")
```

## 扩展验证规则

### 添加新的组件验证规则

1. 在 `validation.yaml` 中添加组件配置
2. 定义开关参数和对应的验证规则
3. 更新组件类以支持新的验证逻辑

### 使用验证规则生成器

```python
from pyahc.tools.generate_validation_rules import ValidationRuleGenerator

generator = ValidationRuleGenerator()

# 添加新的验证规则
generator.add_component_rule(
    'NewComponent', 'switch_param', 1,
    ['required_param1', 'required_param2'],
    ['required_param1 > 0', 'required_param2 is not None']
)

# 生成 YAML 文件
generator.generate_yaml('new_validation_rules.yaml')
```

## 最佳实践

1. **明确的验证表达式**: 使用清晰、易理解的验证表达式
2. **合理的参数范围**: 设置符合物理意义的参数范围
3. **完整的依赖关系**: 明确定义组件间的依赖关系
4. **渐进式验证**: 从基础验证开始，逐步添加复杂验证规则
5. **测试驱动**: 为每个验证规则编写对应的测试用例

## 故障排除

### 常见验证错误

1. **缺少必需参数**: 检查开关参数对应的必需参数是否提供
2. **参数范围错误**: 确认参数值在定义的范围内
3. **类型不匹配**: 检查参数类型是否符合验证规则要求
4. **依赖关系未满足**: 确保依赖的组件已正确配置

### 调试验证规则

```python
# 启用详细验证信息
boundary._validation = True
boundary._debug_validation = True

# 查看验证过程
boundary.validate_conditional_parameters()
```

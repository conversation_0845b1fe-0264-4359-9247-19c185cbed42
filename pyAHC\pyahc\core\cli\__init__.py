"""pyAHC 的命令行界面。

这是一个原型子包，用于增强 pyahc 的功能。
CLI 工具在自动化某些任务（例如加载数据库或经典 AHC 模型）方面非常有用。

!!! 注意

    目前只原型化了创建项目结构。如果用户有此需求，将来会添加更多功能。


示例：

    ```cmd
    pyahc init --notebook  # 创建包含模板 .ipynb 文件的项目结构。
    pyahc init --script  # 创建包含 .py 文件的项目结构。
    ```

运行脚本后，您将看到以下文件夹：

```cmd
test project
├── README
├── __init__.py
├── data
├── models
│   ├── __init__.py
│   └── main.ipynb
└── scripts
    └── __init__.py
```

添加 `__init__.py` 文件是为了创建模块结构。现在，当您在 scripts 中创建包含一些辅助函数的 python 文件时，
您可以将这些函数导入到主模型脚本或 notebook 中并使用它们。

```python
from ..scripts.helper_module import helper_function

var = helper_function(**kwargs)
```

默认情况下，还会随项目结构一起创建 Git 仓库。
"""

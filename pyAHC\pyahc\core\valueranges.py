"""pyAHC 值范围定义模块

本模块定义了 pyAHC 中所有参数的有效值范围，用于数据验证和约束检查。

范围分类：
1. 基础范围：通用的数值范围
2. 物理量范围：具有物理意义的参数范围
3. AHC 特定范围：AHC 模型特有的参数范围
4. 作物参数范围：作物生长相关的参数范围
5. 土壤参数范围：土壤水力特性参数范围
6. 观测参数范围：观测点配置相关的参数范围
7. 时间参数范围：日期时间相关的参数范围
8. 地理参数范围：地理位置相关的参数范围

使用示例：
    from pyahc.core.valueranges import DEPTHRANGE, validate_range

    # 直接使用范围
    min_depth, max_depth = DEPTHRANGE

    # 验证值是否在范围内
    validate_range(-50.0, 'DEPTH')  # 返回 True
    validate_range(10.0, 'DEPTH')   # 抛出 ValueError
"""

# =============================================================================
# 基础范围定义（保持向后兼容性）
# =============================================================================

UNITRANGE = {"ge": 0.0, "le": 1.0}
"""0.0 到 1.0 之间的值范围。"""

YEARRANGE = {"ge": 0, "le": 366}
"""年份的值范围 (0 <= x <= 366)。"""

DVSRANGE = {"ge": 0.0, "le": 2.0}
"""发育阶段的值范围 (0 <= x <= 2)。"""

ACTUALYEARRANGE = (1900, 2100)
"""实际年份范围 (1900 <= x <= 2100)。"""

# =============================================================================
# 新增基础物理量范围
# =============================================================================

DEPTHRANGE = (-1000.0, 0.0)
"""深度范围（负值，单位：cm）"""

CONCENTRATIONRANGE = (0.0, 1000.0)
"""浓度范围（非负，单位：mg/L）"""

TEMPERATURERANGE = (-50.0, 60.0)
"""温度范围（单位：°C）"""

PRESSURERANGE = (-1000.0, 1000.0)
"""压力范围（单位：cm）"""

# =============================================================================
# AHC 特定参数范围
# =============================================================================

# 边界条件参数
SHAPERANGE = (0.1, 10.0)
"""形状因子范围"""

HDRAINRANGE = (-1000.0, 0.0)
"""排水基础深度范围（cm）"""

RIMLAYRANGE = (1.0, 10000.0)
"""隔水层垂直阻力范围（day）"""

AQAVERANGE = (-1000.0, 1000.0)
"""平均水力水头差范围（cm）"""

AQAMPRANGE = (0.0, 1000.0)
"""水力水头正弦波振幅范围（cm）"""

AQPERRANGE = (1.0, 3650.0)
"""水力水头正弦波周期范围（day）"""

# =============================================================================
# 作物参数范围
# =============================================================================

TEMPERATUREBASERANGE = (-10.0, 50.0)
"""基础温度范围，作物生长基础温度（°C）"""

TEMPERATUREOPTRANGE = (0.0, 50.0)
"""最适温度范围，作物最适生长温度（°C）"""

RUEPRANGE = (1.0, 100.0)
"""辐射利用效率范围（kg/ha/MJ）"""

DLAPRANGE = (0.1, 100.0)
"""叶面积参数范围"""

CHMAXRANGE = (10.0, 1000.0)
"""最大冠层高度范围（cm）"""

LMAXRANGE = (0.1, 20.0)
"""最大叶面积指数范围"""

RDMAXRANGE = (1.0, 500.0)
"""最大根深度范围（cm）"""

# =============================================================================
# 土壤参数范围
# =============================================================================

THETASATRANGE = (0.1, 0.8)
"""饱和含水量范围"""

THETARESRANGE = (0.0, 0.5)
"""残余含水量范围"""

ALPHARANGE = (0.001, 10.0)
"""van Genuchten α参数范围（1/cm）"""

NRANGE = (1.01, 10.0)
"""van Genuchten n参数范围"""

KSRANGE = (0.001, 1000.0)
"""饱和导水率范围（cm/day）"""

# =============================================================================
# 观测参数范围
# =============================================================================

OBSNRANGE = (1, 100)
"""观测点数量范围"""

DEPTHMINRANGE = (0, 1000)
"""最小深度范围（cm）"""

DEPTHMAXRANGE = (1, 10000)
"""最大深度范围（cm）"""

REPEATCOUNTRANGE = (1, 10)
"""重复次数范围"""

# =============================================================================
# 时间参数范围
# =============================================================================

DAYRANGE = (1, 31)
"""日期范围"""

MONTHRANGE = (1, 12)
"""月份范围"""

HOURRANGE = (0, 23)
"""小时范围"""

# =============================================================================
# 百分比和比率范围
# =============================================================================

PERCENTAGERANGE = (0.0, 100.0)
"""百分比范围"""

FRACTIONRANGE = (0.0, 1.0)
"""分数范围"""

EFFICIENCYRANGE = (0.0, 1.0)
"""效率范围"""

# =============================================================================
# 物理常数范围
# =============================================================================

LATITUDERANGE = (-90.0, 90.0)
"""纬度范围（度）"""

LONGITUDERANGE = (-180.0, 180.0)
"""经度范围（度）"""

ALTITUDERANGE = (-500.0, 10000.0)
"""海拔范围（m）"""

# =============================================================================
# 范围定义字典（便于程序化访问）
# =============================================================================

RANGE_DEFINITIONS = {
    # 基础范围
    'UNIT': UNITRANGE,
    'YEAR': YEARRANGE,
    'DVS': DVSRANGE,
    'ACTUAL_YEAR': ACTUALYEARRANGE,

    # 物理量范围
    'DEPTH': DEPTHRANGE,
    'CONCENTRATION': CONCENTRATIONRANGE,
    'TEMPERATURE': TEMPERATURERANGE,
    'PRESSURE': PRESSURERANGE,

    # AHC 特定范围
    'SHAPE': SHAPERANGE,
    'HDRAIN': HDRAINRANGE,
    'RIMLAY': RIMLAYRANGE,
    'AQAVE': AQAVERANGE,
    'AQAMP': AQAMPRANGE,
    'AQPER': AQPERRANGE,

    # 作物参数范围
    'TEMPERATURE_BASE': TEMPERATUREBASERANGE,
    'TEMPERATURE_OPT': TEMPERATUREOPTRANGE,
    'RUEP': RUEPRANGE,
    'DLAP': DLAPRANGE,
    'CHMAX': CHMAXRANGE,
    'LMAX': LMAXRANGE,
    'RDMAX': RDMAXRANGE,

    # 土壤参数范围
    'THETA_SAT': THETASATRANGE,
    'THETA_RES': THETARESRANGE,
    'ALPHA': ALPHARANGE,
    'N': NRANGE,
    'KS': KSRANGE,

    # 观测参数范围
    'OBSN': OBSNRANGE,
    'DEPTH_MIN': DEPTHMINRANGE,
    'DEPTH_MAX': DEPTHMAXRANGE,
    'REPEAT_COUNT': REPEATCOUNTRANGE,

    # 时间参数范围
    'DAY': DAYRANGE,
    'MONTH': MONTHRANGE,
    'HOUR': HOURRANGE,

    # 百分比和比率范围
    'PERCENTAGE': PERCENTAGERANGE,
    'FRACTION': FRACTIONRANGE,
    'EFFICIENCY': EFFICIENCYRANGE,

    # 地理参数范围
    'LATITUDE': LATITUDERANGE,
    'LONGITUDE': LONGITUDERANGE,
    'ALTITUDE': ALTITUDERANGE,
}

# =============================================================================
# 范围说明字典
# =============================================================================

RANGE_DESCRIPTIONS = {
    'UNIT': '单位区间，用于比例和分数值',
    'YEAR': '年份范围，用于日期验证 (0-366天)',
    'DVS': '发育阶段范围，作物生长阶段指标',
    'ACTUAL_YEAR': '实际年份范围，用于日历年份验证 (1900-2100)',
    'DEPTH': '深度范围，负值表示地下深度（cm）',
    'CONCENTRATION': '浓度范围，溶质浓度值（mg/L）',
    'TEMPERATURE': '温度范围，环境温度值（°C）',
    'PRESSURE': '压力范围，水压或气压值（cm）',
    'SHAPE': '形状因子范围，边界条件参数',
    'HDRAIN': '排水基础深度范围，负值（cm）',
    'RIMLAY': '隔水层垂直阻力范围（day）',
    'AQAVE': '平均水力水头差范围（cm）',
    'AQAMP': '水力水头正弦波振幅范围（cm）',
    'AQPER': '水力水头正弦波周期范围（day）',
    'TEMPERATURE_BASE': '基础温度范围，作物生长基础温度（°C）',
    'TEMPERATURE_OPT': '最适温度范围，作物最适生长温度（°C）',
    'RUEP': '辐射利用效率范围（kg/ha/MJ）',
    'DLAP': '叶面积参数范围',
    'CHMAX': '最大冠层高度范围（cm）',
    'LMAX': '最大叶面积指数范围',
    'RDMAX': '最大根深度范围（cm）',
    'THETA_SAT': '饱和含水量范围',
    'THETA_RES': '残余含水量范围',
    'ALPHA': 'van Genuchten α参数范围（1/cm）',
    'N': 'van Genuchten n参数范围',
    'KS': '饱和导水率范围（cm/day）',
    'OBSN': '观测点数量范围',
    'DEPTH_MIN': '最小深度范围（cm）',
    'DEPTH_MAX': '最大深度范围（cm）',
    'REPEAT_COUNT': '重复次数范围',
    'DAY': '日期范围（1-31）',
    'MONTH': '月份范围（1-12）',
    'HOUR': '小时范围（0-23）',
    'PERCENTAGE': '百分比范围（0-100%）',
    'FRACTION': '分数范围（0-1）',
    'EFFICIENCY': '效率范围（0-1）',
    'LATITUDE': '纬度范围（度）',
    'LONGITUDE': '经度范围（度）',
    'ALTITUDE': '海拔范围（m）',
}

# =============================================================================
# 范围验证函数
# =============================================================================

def validate_range(value, range_name):
    """验证值是否在指定范围内

    Args:
        value: 待验证的值
        range_name (str): 范围名称

    Returns:
        bool: 验证通过返回 True

    Raises:
        ValueError: 如果范围名称不存在或值超出范围
    """
    if range_name not in RANGE_DEFINITIONS:
        raise ValueError(f"Unknown range: {range_name}")

    range_def = RANGE_DEFINITIONS[range_name]

    # 处理字典格式的范围定义（向后兼容）
    if isinstance(range_def, dict):
        min_val = range_def.get('ge', float('-inf'))
        max_val = range_def.get('le', float('inf'))
    else:
        # 处理元组格式的范围定义
        min_val, max_val = range_def

    if not (min_val <= value <= max_val):
        raise ValueError(
            f"Value {value} is outside range {range_name} [{min_val}, {max_val}]"
        )
    return True


def get_range_info(range_name):
    """获取范围信息

    Args:
        range_name (str): 范围名称

    Returns:
        dict: 包含范围信息的字典

    Raises:
        ValueError: 如果范围名称不存在
    """
    if range_name not in RANGE_DEFINITIONS:
        raise ValueError(f"Unknown range: {range_name}")

    range_def = RANGE_DEFINITIONS[range_name]

    # 处理字典格式的范围定义（向后兼容）
    if isinstance(range_def, dict):
        min_val = range_def.get('ge', float('-inf'))
        max_val = range_def.get('le', float('inf'))
    else:
        # 处理元组格式的范围定义
        min_val, max_val = range_def

    return {
        'name': range_name,
        'min': min_val,
        'max': max_val,
        'range': (min_val, max_val),
        'description': get_range_description(range_name)
    }


def get_range_description(range_name):
    """获取范围描述

    Args:
        range_name (str): 范围名称

    Returns:
        str: 范围描述
    """
    return RANGE_DESCRIPTIONS.get(range_name, "无描述")


def list_all_ranges():
    """列出所有可用的范围定义

    Returns:
        list: 所有范围名称的列表
    """
    return list(RANGE_DEFINITIONS.keys())


def get_range_by_category(category):
    """按类别获取范围定义

    Args:
        category (str): 范围类别

    Returns:
        dict: 该类别下的所有范围定义
    """
    category_mapping = {
        'basic': ['UNIT', 'YEAR', 'DVS'],
        'physical': ['DEPTH', 'CONCENTRATION', 'TEMPERATURE', 'PRESSURE'],
        'ahc_specific': ['SHAPE', 'HDRAIN', 'RIMLAY', 'AQAVE', 'AQAMP', 'AQPER'],
        'crop': ['TEMPERATURE_BASE', 'TEMPERATURE_OPT', 'RUEP', 'DLAP', 'CHMAX', 'LMAX', 'RDMAX'],
        'soil': ['THETA_SAT', 'THETA_RES', 'ALPHA', 'N', 'KS'],
        'observation': ['OBSN', 'DEPTH_MIN', 'DEPTH_MAX', 'REPEAT_COUNT'],
        'time': ['DAY', 'MONTH', 'HOUR'],
        'ratio': ['PERCENTAGE', 'FRACTION', 'EFFICIENCY'],
        'geographic': ['LATITUDE', 'LONGITUDE', 'ALTITUDE'],
    }

    if category not in category_mapping:
        raise ValueError(f"Unknown category: {category}. Available: {list(category_mapping.keys())}")

    return {name: RANGE_DEFINITIONS[name] for name in category_mapping[category] if name in RANGE_DEFINITIONS}

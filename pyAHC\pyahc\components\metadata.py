"""重构后的模型元数据组件。

类：
    Metadata: 重构后的 AHC 模型元数据，支持元数据验证和外部配置。
"""

# 使用 pySWAP-main 导入别名约定，带下划线前缀
from typing import Optional as _Optional, Dict as _Dict, Any as _Any, List as _List
from pydantic import Field as _Field, model_validator as _model_validator, ConfigDict as _ConfigDict
from datetime import datetime as _datetime

from pyahc.core.basemodel import PyAHCBaseModel as _PyAHCBaseModel
from pyahc.core.configurable import ConfigurableComponent as _ConfigurableComponent
from pyahc.utils.mixins import (
    SerializableMixin as _SerializableMixin,
    YAMLValidatorMixin as _YAMLValidatorMixin,
)


class Metadata(_ConfigurableComponent, _YAMLValidatorMixin, _SerializableMixin):
    """重构后的 AHC 模型元数据组件 - 支持元数据验证和外部配置。

    新增功能：
    - 元数据完整性验证
    - 必需字段验证
    - 外部参数配置支持
    - 日期格式验证
    - 项目信息验证

    该组件用于描述模型运行的元数据信息，包括项目信息、版本信息、
    作者信息等。支持从外部配置创建实例，并提供完整的验证机制。

    属性：
        pyahc_version (str): pyAHC版本号
        ahc_version (Optional[str]): AHC版本号
        project_name (str): 项目名称
        project_description (Optional[str]): 项目描述
        author (Optional[str]): 作者
        institution (Optional[str]): 机构
        email (Optional[str]): 电子邮件
        creation_date (Optional[str]): 创建日期 (YYYY-MM-DD格式)
        model_configuration (Optional[Dict[str, Any]]): 模型配置信息
    """

    model_config = _ConfigDict(
        protected_namespaces=()
    )

    # 版本信息（外部化配置）
    pyahc_version: _Optional[str] = _Field(default="1.0.0", description="pyAHC版本号")
    ahc_version: _Optional[str] = _Field(default=None, description="AHC版本号")

    # 项目信息（外部化配置）
    project_name: _Optional[str] = _Field(default="default_project", description="项目名称")
    project_description: _Optional[str] = _Field(default=None, description="项目描述")
    author: _Optional[str] = _Field(default=None, description="作者")
    institution: _Optional[str] = _Field(default=None, description="机构")
    email: _Optional[str] = _Field(default=None, description="电子邮件")
    creation_date: _Optional[str] = _Field(default=None, description="创建日期 (YYYY-MM-DD格式)")

    # 模型配置信息（外部化配置）
    model_configuration: _Optional[_Dict[str, _Any]] = _Field(default=None, description="模型配置信息")

    def __init__(self, **data):
        """自定义初始化方法"""
        # 如果没有提供 pyahc_version，使用默认值
        if 'pyahc_version' not in data:
            data['pyahc_version'] = "1.0.0"

        # 如果没有提供 project_name，但有其他项目相关信息，生成默认名称
        if 'project_name' not in data:
            if 'author' in data:
                data['project_name'] = f"{data['author']}_project"
            else:
                data['project_name'] = "default_project"

        super().__init__(**data)



    @_model_validator(mode="after")
    def validate_metadata_integrity(self):
        """验证元数据完整性"""
        if not self._validation:
            return self

        # 验证必需字段
        if not self.pyahc_version or (isinstance(self.pyahc_version, str) and len(self.pyahc_version.strip()) == 0):
            raise ValueError("pyAHC version is required and cannot be empty")

        if not self.project_name or (isinstance(self.project_name, str) and len(self.project_name.strip()) == 0):
            raise ValueError("Project name is required and cannot be empty")

        # 验证版本格式（基础语义化版本检查）
        if self.pyahc_version:
            self._validate_version_format(self.pyahc_version, "pyAHC version")

        if self.ahc_version:
            self._validate_version_format(self.ahc_version, "AHC version")

        # 验证日期格式
        if self.creation_date:
            try:
                _datetime.strptime(self.creation_date, "%Y-%m-%d")
            except ValueError:
                raise ValueError("Creation date must be in YYYY-MM-DD format")

        # 验证电子邮件格式（基础检查）
        if self.email and "@" not in self.email:
            raise ValueError("Email format is invalid")

        return self



    @_model_validator(mode="after")
    def validate_project_information(self):
        """验证项目信息"""
        if not self._validation:
            return self

        # 验证项目名称格式
        if self.project_name:
            # 检查项目名称是否包含无效字符
            invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '\\', '/']
            for char in invalid_chars:
                if char in self.project_name:
                    raise ValueError(f"Project name contains invalid character: {char}")

        # 验证模型配置完整性
        if self.model_configuration:
            if not isinstance(self.model_configuration, dict):
                raise ValueError("Model configuration must be a dictionary")

        return self

    def _validate_version_format(self, version: str, version_type: str) -> None:
        """验证版本格式"""
        # 基础语义化版本检查 (major.minor.patch)
        import re
        # 支持的格式：主版本.次版本[.修订版本][.构建版本][预发布标识]
        # 例如：1.0, 1.0.0, 1.0.0-beta, 1.2.3.4 等，但不超过4个数字部分
        parts = version.split('.')
        numeric_parts = []
        for part in parts:
            if part.isdigit():
                numeric_parts.append(part)
            else:
                # 如果包含非数字字符，检查是否是预发布标识
                if '-' in part or any(c.isalpha() for c in part):
                    break
                else:
                    raise ValueError(f"{version_type} format is invalid. Expected format: major.minor[.patch][.build]")

        # 检查数字部分数量（最多4个：major.minor.patch.build）
        if len(numeric_parts) < 2 or len(numeric_parts) > 4:
            raise ValueError(f"{version_type} format is invalid. Expected format: major.minor[.patch][.build]")

        # 检查是否以字母开头（无效）
        if version[0].isalpha():
            raise ValueError(f"{version_type} format is invalid. Expected format: major.minor[.patch][.build]")



    @classmethod
    def from_config(cls, config: _Dict[str, _Any]) -> "Metadata":
        """从外部配置创建实例"""
        return cls.from_external_config(config)

    def get_metadata_summary(self) -> _Dict[str, _Any]:
        """获取元数据摘要"""
        return {
            'project_name': self.project_name,
            'pyahc_version': self.pyahc_version,
            'ahc_version': self.ahc_version,
            'author': self.author,
            'institution': self.institution,
            'created': self.creation_date,
            'has_model_config': self.model_configuration is not None,
            'config_keys_count': len(self.model_configuration) if self.model_configuration else 0
        }

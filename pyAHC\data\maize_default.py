"""玉米作物的默认EPIC参数配置

此模块包含玉米作物的默认参数设置，用于生成Maize.CRE文件。
"""

import pandas as pd

# 默认的相对根密度分布表
DEFAULT_RDCTB = pd.DataFrame([
    [0.00, 0.8],
    [0.15, 0.95],
    [0.25, 1.05],
    [0.35, 1.1],
    [0.5, 1.0],
    [0.6, 0.7],
    [0.65, 0.38],
    [0.7, 0.24],
    [0.8, 0.15],
    [0.9, 0.1],
    [1.00, 0.05]
], columns=['RD', 'RDC'])

# 默认的根系干物质分配表
DEFAULT_FRTB = pd.DataFrame([
    [0.0, 0.80],
    [0.2, 0.60],
    [0.5, 0.30],
    [0.7, 0.10],
    [1.0, 0.02]
], columns=['DVS', 'FR'])

# 默认的全球太阳辐射消光系数表
DEFAULT_KGSRTB = pd.DataFrame([
    [0.0, 0.62],
    [1.0, 0.62]
], columns=['DVS', 'KGSR'])

# 默认的光合有效辐射消光系数表
DEFAULT_KPARTB = pd.DataFrame([
    [0.0, 0.65],
    [1.0, 0.65]
], columns=['DVS', 'KPAR'])

# 默认的作物因子表（当SWCF=1时使用）
DEFAULT_CFTB = pd.DataFrame([
    [0.00, 1.2],
    [1.00, 1.2]
], columns=['DVS', 'CF'])

# 默认的作物高度表（当SWCF=3时使用）
DEFAULT_CHTB = pd.DataFrame([
    [0.00, 1.00],
    [0.14, 30.00],
    [0.29, 60.00],
    [0.38, 90.10],
    [0.46, 160.8],
    [0.52, 180.0],
    [0.60, 200.0],
    [0.73, 200.0],
    [0.86, 200.0],
    [1.00, 200.0]
], columns=['DVS', 'CH'])

# 默认的表面阻力表（当SWRSC=2时使用）
DEFAULT_RSCTB = pd.DataFrame([
    [0.0, 80.0],
    [0.50, 80.0],
    [0.51, 80.0],
    [0.80, 80.0],
    [0.81, 150.0],
    [1.0, 150.0]
], columns=['DVS', 'RSC'])

# 默认的根系呼吸温度敏感性表
DEFAULT_Q12PTB = pd.DataFrame([
    [0.0, 5.0],
    [0.5, 2.6],
    [1.0, 1.6]
], columns=['DVS', 'Q12P'])

# 默认的积水深度调节表（当SWPDA=1时使用）
DEFAULT_PDMXCTB = pd.DataFrame([
    [0.0, 5.0],
    [0.2, 5.0],
    [0.2, 3.0],
    [0.4, 3.0],
    [0.4, 2.0],
    [0.6, 2.0],
    [0.6, 4.0],
    [0.8, 4.0],
    [0.8, 2.0],
    [1.0, 2.0]
], columns=['DVS', 'PDMXC'])

# 玉米作物的默认参数字典
MAIZE_DEFAULT_PARAMS = {
    # Section 1: Crop factor or crop height
    "swcf": 2,
    "cftb": DEFAULT_CFTB,
    "cfini": 0.0,
    "chtb": DEFAULT_CHTB,
    
    # Section 2: Surface resistance parameters
    "swrsc": 1,
    "rsc": 70.0,
    "rsctb": DEFAULT_RSCTB,
    "frgmax": 0.75,
    "vpdfr": 4.00,
    
    # Section 3: Root water uptake compensation
    "swcompen": 0,
    "omgs": 0.8,
    
    # Section 4: Root water uptake: water stress
    "swaquc": 0,
    "swrootw": 1,
    "hlim1": -1,
    "hlim2u": -15.0,
    "hlim2l": -15.0,
    "hlim3h": -400.0,
    "hlim3l": -600.0,
    "hlim4": -8000.0,
    "adcrh": 0.5,
    "adcrl": 0.1,
    "p1": 2.5,
    "h50": -8000,
    
    # Section 5: Root water uptake: salt stress
    "swroots": 3,
    "ecmax": 3.5,
    "ecslop": 8.0,
    "h50c": -6000,
    "ci": -537,
    "p2v": 3.0,
    "p2d": 1.86,
    "hcritx": -2000,
    
    # Section 6: Interception of rainfall
    "cofab": 0.25,
    "fwcc": 0.60,
    
    # Section 7: Root density distribution and root growth
    "rdctb": DEFAULT_RDCTB,
    "frtb": DEFAULT_FRTB,
    "swrtcr": 0,
    "ratres": 26.18,
    "q12ptb": DEFAULT_Q12PTB,
    
    # Section 8: Light extinction
    "kgsrtb": DEFAULT_KGSRTB,
    "kpartb": DEFAULT_KPARTB,
    
    # Section 9: EPIC crop growth module parameters
    "tba": 9.0,
    "topt": 25.0,
    "ruep": 47.0,
    "dlap1": 15.03,
    "dlap2": 60.95,
    "fdlai": 0.8,
    "rlad": 0.8,
    "chmax": 265.0,
    "lmax": 5.0,
    "rdmax": 90.0,
    "bn1": 0.044,
    "bn2": 0.015,
    "bn3": 0.010,
    "hipot": 0.52,
    "himin": 0.40,
    "phu": 1900,
    "druevp": 8.0,
    
    # Section 10: Active root nitrogen uptake
    "swactn": 0,
    "pwrts": 30000,
    "cnup1": 5.5,
    "cnup2": 1.5,
    
    # Section 11: Maximum ponding depth control
    "swpda": 0,
    "pdmxctb": DEFAULT_PDMXCTB,
    
    # Section 12: CO2 effects
    "swco2": 1,
    "co2": 330.0,
    "pdlc": 0.40,
    "pilm": 0.40,
    "ruecp2": 660.50,
    
    # Section 13: Initial values
    "huiini": 0.0,
    "laiini": 0.0,
    "rdini": 0.01,
    "bioini": 0.0
}
"""AHC.CTR 文件组件。

此模块包含用于处理 AHC.CTR 控制文件的组件类。
AHC.CTR 文件包含 AHC 模型的主要控制参数和配置信息。

重构后支持：
- Pydantic 验证框架
- 外部参数配置
- 日期格式验证
- 文件依赖关系验证
- 参数一致性验证
"""

from __future__ import annotations

from typing import List as _List, Optional as _Optional, Dict as _Dict, Any as _Any
from pydantic import Field as _Field, model_validator as _model_validator

from pyahc.core.basemodel import PyAHCBaseModel as _PyAHCBaseModel
from pyahc.utils.mixins import YAMLValidatorMixin as _YAMLValidatorMixin, FileMixin as _FileMixin

# 常量定义
MIN_YEAR = 1900
MAX_YEAR = 2100
MIN_ALTITUDE = -500  # 死海以下
MAX_BALANCE_NODE = 500


class CtrFile(_PyAHCBaseModel, _YAMLValidatorMixin, _FileMixin):
    """AHC.CTR控制文件组件 - 支持外部参数配置。

    此类表示 AHC 模型的主控制文件，包含环境设置、时间变量、
    气象数据配置、输入输出文件设置以及各种模拟开关。

    新增功能：
    - Pydantic 验证框架
    - 外部参数配置支持
    - 日期格式验证
    - 文件依赖关系验证
    - 参数一致性验证
    """

    # Section 1: Environment (外部化配置)
    project: str = _Field(description="项目名称，用于 .SWP, .SLT 和 .HEA 文件的通用名称")
    ctype: int = _Field(default=1, description="计算机类型开关，PC=1, VAX & Workstation=2")
    swscre: int = _Field(default=1, description="显示模拟运行进度开关，Y=1, N=0")

    # Section 2: Time variables (外部化配置)
    ssrun: _List[int] = _Field(description="模拟运行开始日期，日 月 年")
    esrun: _List[int] = _Field(description="模拟运行结束日期，日 月 年")
    fmay: int = _Field(default=1, description="农业年的第一个月，1月=1")
    period: int = _Field(default=1, description="输出间隔，忽略=0")
    swres: int = _Field(default=0, description="每年重置输出间隔计数器，Y=1, N=0")
    swodat: int = _Field(default=0, description="表中给出额外输出日期的开关，Y=1, N=0")
    output_dates: _Optional[_List[_List[int]]] = _Field(default=None, description="额外输出日期列表")

    # Section 3: Meteorological data (外部化配置)
    metfil: str = _Field(description="气象数据文件名（不含扩展名）")
    lat: float = _Field(ge=-90.0, le=90.0, description="气象站纬度")
    alt: float = _Field(ge=-500.0, description="气象站海拔高度")  # 允许死海以下
    winal: float = _Field(default=2.0, description="风速测量高度")
    swdivpm: int = _Field(default=1, description="潜在蒸发蒸腾模拟开关")
    swetr: int = _Field(default=0, description="使用气象数据文件中的ETRef值开关")
    swrai: int = _Field(default=0, description="使用详细降雨数据开关")
    swrad: int = _Field(default=1, description="辐射数据类型选择")
    swhum: int = _Field(default=1, description="湿度数据类型选择")

    # Section 4: Input and output files (外部化配置)
    irgfil: _Optional[str] = _Field(default=None, description="固定灌溉数据输入文件名")
    calfil: _Optional[str] = _Field(default=None, description="作物轮作方案输入文件名")
    drfil: _Optional[str] = _Field(default=None, description="排水例程输入文件名")
    bbcfil: _Optional[str] = _Field(default=None, description="底部边界条件文件名")
    outfil: str = _Field(default="result", description="输出文件通用名称")
    
    # Section 5: Processes (外部化配置)
    swswf: int = _Field(default=1, description="土壤水流模拟开关")
    swdra: int = _Field(default=0, description="侧向排水模拟开关")  # 默认关闭
    swsolu: int = _Field(default=0, description="溶质运移模拟开关")  # 默认关闭
    swstyp: int = _Field(default=1, description="溶质类型选择")
    swhea: int = _Field(default=0, description="土壤热传输模拟开关")
    swvapt: int = _Field(default=0, description="考虑蒸汽传输开关")

    # Section 6: Optional output files (外部化配置)
    swvap: int = _Field(default=0, description="输出水分溶质和温度剖面开关")  # 默认关闭
    swdrf: int = _Field(default=0, description="输出排水通量开关")
    swswb: int = _Field(default=0, description="输出地表水库开关")

    # Section 7: Output balance zone (外部化配置)
    balnode: int = _Field(default=200, description="平衡区底部节点")  # 提供合理默认值

    # Section 8: Optional for model running (外部化配置)
    iclb: int = _Field(default=0, description="模型运行选项")

    def _validate_single_date(self, date_list: _List[int], date_name: str) -> None:
        """验证单个日期的格式和范围"""
        if len(date_list) != 3:
            raise ValueError(f"{date_name} date must have 3 elements [day, month, year]")

        day, month, year = date_list
        if not (1 <= day <= 31):
            raise ValueError(f"{date_name} date: invalid day {day}, must be 1-31")
        if not (1 <= month <= 12):
            raise ValueError(f"{date_name} date: invalid month {month}, must be 1-12")
        if year < MIN_YEAR or year > MAX_YEAR:
            raise ValueError(f"{date_name} date: invalid year {year}, must be {MIN_YEAR}-{MAX_YEAR}")

    @_model_validator(mode="after")
    def validate_date_format(self):
        """验证日期格式和范围"""
        if not self._validation:
            return self

        self._validate_single_date(self.ssrun, 'start')
        self._validate_single_date(self.esrun, 'end')

        return self

    @_model_validator(mode="after")
    def validate_file_dependencies(self):
        """验证文件依赖关系"""
        if not self._validation:
            return self

        # 检查开关与文件名的依赖关系
        if self.swdra == 1 and not self.drfil:
            raise ValueError("swdra=1 requires drfil (drainage file)")

        if self.swdra == 2 and not self.drfil:
            raise ValueError("swdra=2 requires drfil (extended drainage file)")

        if self.swsolu == 1 and not self.bbcfil:
            raise ValueError("swsolu=1 requires bbcfil (bottom boundary file)")

        # 检查输出文件开关的依赖关系
        if self.swdrf == 1 and self.swdra == 0:
            raise ValueError("swdrf=1 (drainage flux output) requires swdra=1 or swdra=2")

        if self.swswb == 1 and self.swdra != 2:
            raise ValueError("swswb=1 (surface water reservoir output) requires swdra=2 (extended drainage)")

        return self

    def _date_to_int(self, date_list: _List[int]) -> int:
        """将日期列表转换为整数用于比较"""
        day, month, year = date_list
        return year * 10000 + month * 100 + day

    @_model_validator(mode="after")
    def validate_parameter_consistency(self):
        """验证参数一致性"""
        if not self._validation:
            return self

        # 验证日期顺序
        if len(self.ssrun) == 3 and len(self.esrun) == 3:
            start_date = self._date_to_int(self.ssrun)
            end_date = self._date_to_int(self.esrun)

            if start_date >= end_date:
                raise ValueError("Start date must be before end date")

        # 验证海拔高度（纬度已在字段定义中验证）
        if self.alt < MIN_ALTITUDE:
            raise ValueError(f"Altitude seems unreasonable: {self.alt}, minimum allowed: {MIN_ALTITUDE}")

        # 验证开关参数逻辑
        if self.swsolu == 1 and self.swstyp not in [1, 2, 3, 4]:
            raise ValueError(f"Invalid swstyp value {self.swstyp}, must be 1-4 when swsolu=1")

        # 验证平衡节点
        if self.balnode < 0 or self.balnode > MAX_BALANCE_NODE:
            raise ValueError(f"balnode must be between 0 and {MAX_BALANCE_NODE}, got {self.balnode}")

        return self

    @classmethod
    def from_config(cls, config: _Dict[str, _Any]) -> "CtrFile":
        """从外部配置创建实例"""
        return cls(**config)

    def model_string(self) -> str:
        """生成 AHC.CTR 文件的字符串表示。
        
        Returns:
            str: 格式化的 AHC.CTR 文件内容
        """
        lines = []
        
        # Header
        lines.extend([
            "********************************************************************************",
            "* Filename: AHC.CTR",
            "* Contents: AHC v2.0  general input data",
            "********************************************************************************",
            "* HUINONG AREA WATER, SOLUTE AND CROP",
            "* ********************************************************************************",
            "",
        ])
        
        # Section 1: Environment
        lines.extend([
            "********************************************************************************",
            "* Section 1: Environment",
            "*",
            f"  Project = '{self.project}' ! Generic name for .SWP, .SLT and .HEA file, [A8]",
            "* Path    = '' ! Path to data directory, [A50]",
            f"  CType   = {self.ctype} ! Switch, computer type, [PC = 1, VAX & Workstation = 2]",
            f"  SWSCRE  = {self.swscre} ! Switch, display progression of simulation run [Y=1, N=0]",
            "********************************************************************************",
            "",
        ])
        
        # Section 2: Time variables
        lines.extend([
            "********************************************************************************",
            "* Section 2: Time variables",
            "*",
            f"  SSRUN  = {self.ssrun[0]} {self.ssrun[1]} {self.ssrun[2]} ! Start date of simulation run, give day month year, [3I]",
            f"  ESRUN  = {self.esrun[0]} {self.esrun[1]} {self.esrun[2]} ! End date of simulation run, give day month year, [3I]",
            f"  FMAY   = {self.fmay} ! First month of the agricultural year, Jan.= 1, [1..12, I]",
            f"  Period = {self.period} ! Output interval, ignore = 0, [0..366, I]",
            f"  SWRES  = {self.swres} ! reset output interval counter each year, [Y=1, N=0]",
            f"  SWODAT = {self.swodat}! Switch, extra output dates are given in table, [Y=1, N=0]",
            "*",
        ])
        
        # Output dates table 
        lines.append("* If SWODAT = 1, table with additional output dates, SWODAT=0时跳过不输")
        lines.append("* Date records of type dd mm yyyy (max. 366), [1..31, 1..12, 1..3000, 3I]")
        if self.output_dates:
            for date in self.output_dates:
                lines.append(f" {date[0]}  {date[1]}  {date[2]}  ")

        lines.extend([
            "* End of table",
            "********************************************************************************",
            "",
        ])
        
        # Section 3: Meteorological data
        lines.extend([
            "********************************************************************************",
            "* Section 3: Meteorological data",
            "*",
            f"  METFIL = '{self.metfil}' ! Filename without extension of meteorological data, [A7]",
            f"  LAT    = {self.lat} ! Latitude of meteo station [-90..90 degrees, R, North = +]",
            f"  ALT    = {self.alt} ! Altitude of meteo station [-400..3000 m, R]",
            f"  WINAL  = {self.winal} ! Height of wind speed measurement above soil surface[0..99 m, R]",
            f"  SWDIVPM = {self.swdivpm} ! Switch, simulation of potential E and T",
            "*            1 = Calculate PE and PT based on crop and soil factors",
            "*            2 = Reseved method 1",
            "*            3 = Reseved method 2",
            "*            4 = Use Hargreaves equation to calculate ETo",
            f"  SWETR  = {self.swetr} ! Switch, use ETRef values from meteo data file",
            f"  SWRAI  = {self.swrai} ! Switch, use detailed rainfall data [Y=1, N=0]",
            f"  SWRAD  ={self.swrad} ! Switch, choose the type of measured radiation data",
            "*            1 = Using global solar radiation",
            "*            2 = Use the daily sunshine hours",
            f"  SWHUM  ={self.swhum} ! Switch, choose the type of measured humidity data",
            "*            1 = Use vapor pressure",
            "*            2 = Use relative humidity",
            "********************************************************************************",
            "",
        ])
        
        # Section 4: Input and output files
        lines.extend([
            "********************************************************************************",
            "* Section 4: In- and output files for the simulation runs",
            "*",
            "* Specify for each simulation run (max. 30) the following 5 file names",
            "* without extension (no file: write '  ' )",
            "* 1 - the (optional) input file with fixed irrigation data [.IRG]",
            "* 2 - the (optional) input file with the crop rotation scheme [.CAL]",
            "* 3 - the (optional) input file for the basic drainage routine [.DRB]",
            "*  or the (optional) input file for the extended drainage routine [.DRE]",
            "* 4 - the input file with the bottom boundary condition [.BBC]",
            "* 5 - generic name of output files",
            "*",
            "*   IRGFIL     CALFIL      DRFIL     BBCFIL     OUTFIL",
        ])

        # 格式化文件名行，提高可读性
        irgfil = self.irgfil or ''
        calfil = self.calfil or ''
        drfil = self.drfil or ''
        bbcfil = self.bbcfil or ''
        outfil = self.outfil

        file_line = f" '   {irgfil}' '  {calfil}' '   {drfil}' '   {bbcfil}' '  {outfil}' "
        lines.extend([
            file_line,
            "********************************************************************************",
            "",
        ])
        
        # Section 5: Processes
        lines.extend([
            "********************************************************************************",
            "* Section 5: Processes which should be considered",
            "* ",
            f"  SWSWF  = {self.swswf} ! Switch, simulation of soil water flow [Y=1, N=0] 用于溶质解析解cxu",
            "*",
            f"  SWDRA  = {self.swdra} ! Switch, simulation of lateral drainage:",
            "*            0 = No simulation of drainage",
            "*            1 = Simulation with basic drainage routine",
            "*            2 = Simulation with extended drainage routine",
            "*",
            f"  SWSOLU = {self.swsolu} ! Switch, simulation of solute transport [Y=1, N=0]",
            "* If SWSOLU = 1 specify:",
            f"     SWSTYP = {self.swstyp} ! Choose solute type [=1,2,3]",
            "*              1 = Salinity, total solute transport ",
            "*              2 = Nitrogen transport",
            "*              3 = Multicomponent solute transport",
            "*              4 = Nitrogen & Salinity transport",
            f"  SWHEA  = {self.swhea} ! Switch, simulation of soil heat transport [Y=1, N=0]",
            f"  SWVAPT = {self.swvapt} ! Switch, considering vapor transfer [Y=1, N=0]",
            "********************************************************************************",
            "",
        ])
        
        # Section 6: Optional output files
        lines.extend([
            "********************************************************************************",
            "* Section 6: Optional output files, each run generates a separate file",
            "*",
            f"  SWVAP = {self.swvap} ! Switch, output profiles of moisture solute and temperature [Y=1, N=0]",
            f"  SWDRF = {self.swdrf} ! Switch, output drainage fluxes, only for extended drainage [Y=1, N=0]",
            f"  SWSWB = {self.swswb} ! Switch, output surface water reservoir, only for ext. dr. [Y=1, N=0]",
            "********************************************************************************",
            "",
        ])
        
        # Section 7: Output balance zone
        lines.extend([
            "********************************************************************************",
            "* Section 7: Output balance zone from node #1 to node #BALNODE",
            "* 定义了水盐均衡计算剖面的底部节点",
            f"  BALNODE ={self.balnode} ! bottom node of balance zone, [=0..500; N=0 means no output]",
            "********************************************************************************",
            "",
        ])
        
        # Section 8: Optional for model running
        lines.extend([
            "********************************************************************************",
            "* Section 8: Optional for model running [=0,1,2]",
            f"  iclb = {self.iclb} !      0 = normal run ",
            "*                    1 = do sensitivity analysis",
            "*                    2 = do model calibration",
            "*                    3 = GA run",
            "* End of File ******************************************************************",
            "",
        ])

        return "\n".join(lines)
BottomBoundary:
  swbotb:
    1:
      required:
      - gwlevel_data
      validation:
      - len(gwlevel_data) > 0
      - all(isinstance(item, tuple) and len(item) == 3 for item in gwlevel_data)
    3:
      required:
      - shape
      - hdrain
      - rimlay
      - aqave
      - aqamp
      - aqtamx
      - aqper
      validation:
      - shape > 0.0 and shape <= 10.0
      - hdrain < 0.0
      - rimlay > 0.0
      - aqper > 0.0
    4:
      required:
      - cofqha
      - cofqhb
      - cofqhc
      validation:
      - cofqha is not None
      - cofqhb is not None
      - cofqhc is not None
ComponentDependencies:
  BottomBoundary:
    depends_on:
    - component: CtrFile
      conditions:
      - swsolu is not None
      - swhea is not None
  EpicCrop:
    depends_on:
    - component: CtrFile
      conditions:
      - project is not None
      - ssrun is not None
      - esrun is not None
  ObservationPoints:
    depends_on:
    - component: CtrFile
      conditions:
      - project is not None
CtrFile:
  swdra:
    1:
      required:
      - drfil
      validation:
      - drfil is not None and len(drfil) > 0
  swsolu:
    1:
      required:
      - swstyp
      - bbcfil
      validation:
      - swstyp in [0, 1]
      - bbcfil is not None and len(bbcfil) > 0
EpicCrop:
  swcf:
    1:
      required:
      - cftb
      validation:
      - cftb is not None
    2:
      required:
      - chtb
      - cfini
      validation:
      - chtb is not None
      - cfini >= 0.0 and cfini <= 500.0
    3:
      required:
      - cfini
      validation:
      - cfini >= 0.0 and cfini <= 500.0
  swrsc:
    1:
      required:
      - rsc
      - frgmax
      - vpdfr
      validation:
      - rsc > 0.0
      - frgmax >= 0.0 and frgmax <= 1.0
      - vpdfr > 0.0
    2:
      required:
      - rsctb
      - frgmax
      - vpdfr
      validation:
      - rsctb is not None
      - frgmax >= 0.0 and frgmax <= 1.0
      - vpdfr > 0.0
ParameterExternalization:
  BottomBoundary:
    configurable_params:
    - default: 1.0
      description: 形状因子
      name: shape
      range:
      - 0.1
      - 10.0
    - default: -120.0
      description: 排水基础
      name: hdrain
      range:
      - -1000.0
      - 0.0
    - default: 500.0
      description: 隔水层垂直阻力
      name: rimlay
      range:
      - 1.0
      - 10000.0
    - default: 0.0
      description: 平均水力水头差
      name: aqave
      range:
      - -1000.0
      - 1000.0
    - default: 0.0
      description: 水力水头正弦波振幅
      name: aqamp
      range:
      - 0.0
      - 1000.0
    - default: 365.0
      description: 水力水头正弦波周期
      name: aqper
      range:
      - 1.0
      - 3650.0
  EpicCrop:
    configurable_params:
    - default: 9.0
      description: 基础温度
      name: tba
      range:
      - -10.0
      - 50.0
    - default: 25.0
      description: 最适温度
      name: topt
      range:
      - 0.0
      - 50.0
    - default: 47.0
      description: 辐射利用效率
      name: ruep
      range:
      - 1.0
      - 100.0
    - default: 265.0
      description: 最大冠层高度
      name: chmax
      range:
      - 10.0
      - 1000.0
  ObservationPoints:
    configurable_params:
    - default: 2
      description: 重复次数
      name: repeat_count
      range:
      - 1
      - 10
    - default: 1
      description: 最小深度差异
      name: min_depth_difference
      range:
      - 1
      - 100
    - default: 1000
      description: 最大深度
      name: max_depth
      range:
      - 100
      - 10000

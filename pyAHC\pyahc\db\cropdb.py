"""封装 WOFOST (A. de Wit) 作物参数数据库的类。

在此处的类中，只有 WOFOSTCropDB 由用户直接访问，
但是，最终可用的对象将是 CropVariety。

类：
    WOFOSTCropFile: 管理单个 WOFOST 作物文件内容。
    CropVariety: 管理作物品种参数。
    WOFOSTCropDB: 管理单个 WOFOST 作物文件内容。
"""

from pathlib import Path
from pprint import pprint

from pydantic import BaseModel, computed_field

from pyahc.core.io.io_yaml import load_yaml
from pyahc.libs import crop_params


class WOFOSTCropFile(BaseModel):
    """管理单个 WOFOST 作物文件内容。

    属性：
        yaml_content: YAML 文件的全部内容。

    属性：
        metadata: 作物文件的元数据。
        ecotypes: 生态类型列表。
        genericc3: C3 作物类型的通用设置。
        genericc4: C4 作物类型的通用设置。
        varieties: 可用品种列表。

    方法：
        get_variety: 获取特定品种的参数。
    """

    yaml_content: dict

    @computed_field(return_type=dict)
    def metadata(self):
        """yaml 作物文件的元数据"""
        return self.yaml_content["Metadata"]

    @computed_field(return_type=dict)
    def ecotypes(self):
        return list(self.yaml_content["CropParameters"]["EcoTypes"])

    @computed_field(return_type=dict)
    def genericc3(self):
        """获取 C3 作物类型的通用设置 - 将 CO2 结合到
        具有三个碳原子的 3-磷酸甘油酸的植物。例如，小麦、水稻"""
        return self.yaml_content["CropParameters"]["GenericC3"]

    @computed_field(return_type=dict)
    def genericc4(self):
        """获取 C4 作物类型的通用设置 - 将 CO2 结合到
        具有四个碳原子的草酰乙酸的植物。例如，玉米、甘蔗"""
        return self.yaml_content["CropParameters"]["GenericC4"]

    @computed_field(return_type=dict)
    def varieties(self):
        return list(self.yaml_content["CropParameters"]["Varieties"])

    def get_variety(self, variety: str):
        return CropVariety(
            variety=self.yaml_content["CropParameters"]["Varieties"][variety]
        )


class CropVariety(BaseModel):
    """管理作物品种参数。

    属性：
        variety: YAML 文件中作物品种的参数（包含元数据）。

    属性：
        parameters: 品种的裸参数（所有元数据已删除）。
        metadata: 品种的元数据。
    """

    variety: dict

    @computed_field(return_type=dict)
    def parameters(self):
        params = {
            k.lower(): v[0]
            for k, v in self.variety.items()
            if k != "Metadata" and v[0] != -99.0
        }
        return self._format_tables(params)

    @computed_field(return_type=dict)
    def metadata(self):
        return self.variety["Metadata"]

    @staticmethod
    def _format_tables(table: dict) -> dict[str, list[list]]:
        """将 YAML 中的表格预格式化为列表的列表。

        在 YAML 文件中，表格的格式似乎是
        列表中奇数元素为一列，偶数元素为另一列。此方法
        将此格式转换为包含两个列表的字典。
        """

        formatted = {
            k: [list(row) for row in zip(v[::2], v[1::2], strict=False)]
            if isinstance(v, list)
            else v
            for k, v in table.items()
        }

        return formatted


class WOFOSTCropDB(BaseModel):
    """用于管理作物参数文件的简单类。

    最初，它旨在与 A. de Wit 的 WOFOST 作物参数数据库一起使用，
    该数据库是 YAML 文件的集合。但是，当出现其他格式和数据库时，
    它可以很容易地扩展以支持它们。所有方法都应将内容作为 WOFOSTCropFile 实例返回。
    这样，我们确保无论源格式是什么，内容始终以相同的方式可用。

    属性：
        libdir: 包含作物参数的目录路径

    属性：
        croptypes: 列出所有可用的作物类型（目录中的文件）

    方法：
        load_crop_file: 加载特定的作物文件并以 WOFOSTCropFile 实例的形式返回内容
    """

    libdir: Path = crop_params

    @computed_field(return_type=None)
    def croptypes(self):
        """打印可用文件列表"""
        pprint(load_yaml(crop_params / "crops.yaml")["available_crops"])

    def load_crop_file(self, crop: str) -> WOFOSTCropFile:
        """加载特定的作物文件并以字典形式返回内容"""
        path = (
            self.libdir / f"{crop}"
            if crop.endswith(".yaml")
            else self.libdir / f"{crop}.yaml"
        )
        return WOFOSTCropFile(yaml_content=load_yaml(path))


__all__ = ["WOFOSTCropDB"]

"""观测点组件模块

该模块定义了用于生成 point.OBS 文件的 ObservationPoints 组件。
point.OBS 文件包含土壤分层观测点的深度信息。
重构版本支持可配置格式化模板和深度数据验证。
"""

from typing import List as _List, Tuple as _Tuple, Dict as _Dict, Any as _Any, Optional as _Optional
from pydantic import Field as _Field, model_validator as _model_validator
from pathlib import Path as _Path

from pyahc.core.configurable import ConfigurableComponent as _ConfigurableComponent
from pyahc.utils.mixins import FileMixin as _FileMixin


class ObservationPoints(_ConfigurableComponent, _FileMixin):
    """重构后的观测点组件 - 支持可配置格式化模板

    用于生成 point.OBS 文件，包含土壤分层观测点的深度信息。
    支持可配置格式化模板和深度数据验证。
    """
    
    _extension = "OBS"
    
    # 观测点数量
    obsn: int = _Field(ge=1, le=100, description="观测点数量")

    # 观测点深度数据 (上边界, 下边界)
    observation_depths: _List[_Tuple[int, int]] = _Field(
        description="观测点深度数据列表，每个元组包含(上边界深度, 下边界深度)"
    )

    # 重复次数配置
    repeat_count: int = _Field(default=2, ge=1, description="观测点深度数据在文件中的重复次数")

    # 可配置的格式化模板
    depth_format_template: _Optional[str] = _Field(
        default=" {upper:<6} {lower:<6}      ",
        description="深度格式化模板"
    )

    # 可配置的OBSN行模板
    obsn_format_template: _Optional[str] = _Field(
        default=" {obsn}   {obsn}      #OBSN=Number of observed layer, {obsn} layer is recommond",
        description="OBSN行格式化模板"
    )

    def __init__(self, **data):
        super().__init__(**data)
        self._setup_default_templates()

    def _setup_default_templates(self):
        """设置默认模板（可被外部配置覆盖）"""
        # 设置默认格式化模板
        if self.depth_format_template is None:
            self.set_param_default('depth_format_template', " {upper:<6} {lower:<6}      ")

        if self.obsn_format_template is None:
            self.set_param_default('obsn_format_template',
                                 " {obsn}   {obsn}      #OBSN=Number of observed layer, {obsn} layer is recommond")

    @_model_validator(mode="after")
    def validate_observation_depths(self):
        """验证观测点深度数据"""
        if not self._validation:
            return self

        if not self.observation_depths:
            raise ValueError("observation_depths list cannot be empty")

        # 检查深度值是否为正数且上边界小于下边界
        for i, (upper, lower) in enumerate(self.observation_depths):
            if upper < 0 or lower < 0:
                raise ValueError(f"Depth values at index {i} must be non-negative, got upper={upper}, lower={lower}")

            if upper >= lower:
                raise ValueError(f"Upper boundary must be less than lower boundary at index {i}, got upper={upper}, lower={lower}")

        # 检查深度范围是否有重叠
        for i in range(len(self.observation_depths)):
            for j in range(i + 1, len(self.observation_depths)):
                upper1, lower1 = self.observation_depths[i]
                upper2, lower2 = self.observation_depths[j]

                # 检查是否有重叠
                if not (lower1 <= upper2 or lower2 <= upper1):
                    raise ValueError(
                        f"Depth ranges overlap: range {i} ({upper1}-{lower1}) "
                        f"overlaps with range {j} ({upper2}-{lower2})"
                    )

        return self

    def format_obsn_line(self) -> str:
        """使用可配置模板格式化OBSN行"""
        template = self.get_param_value('obsn_format_template', self.obsn_format_template)
        return template.format(obsn=self.obsn)

    def format_depth_line(self, upper: int, lower: int) -> str:
        """使用可配置模板格式化深度行"""
        template = self.get_param_value('depth_format_template', self.depth_format_template)
        return template.format(upper=upper, lower=lower)

    def model_string(self) -> str:
        """生成 point.OBS 文件的字符串表示"""
        lines = []

        # 使用可配置模板添加 OBSN 参数
        lines.append(self.format_obsn_line())

        # 使用可配置模板添加观测点深度数据
        for _ in range(self.repeat_count):
            for upper, lower in self.observation_depths:
                lines.append(self.format_depth_line(upper, lower))

        # 添加空行以匹配原始文件格式
        lines.append("")

        return "\n".join(lines)
    
    def write_observation_points(self, path: str | _Path, filename: str = "point") -> None:
        """写入 point.OBS 文件
        
        Args:
            path: 输出路径
            filename: 文件名（不包含扩展名）
        """
        content = self.model_string()
        self.save_file(content, filename, path)

    @classmethod
    def from_config(cls, config: _Dict[str, _Any]) -> "ObservationPoints":
        """从外部配置创建实例"""
        return cls.from_external_config(config)

    @classmethod
    def create_standard_depths(cls, max_depth: int = 100, num_layers: int = 5,
                             config_overrides: _Optional[_Dict[str, _Any]] = None) -> "ObservationPoints":
        """创建标准深度配置的观测点实例"""
        # 创建均匀分布的深度层
        layer_thickness = max_depth // num_layers
        observation_depths = []

        for i in range(num_layers):
            upper = i * layer_thickness
            lower = (i + 1) * layer_thickness
            observation_depths.append((upper, lower))

        base_params = {
            'obsn': num_layers,
            'observation_depths': observation_depths,
            'repeat_count': 2
        }

        if config_overrides:
            base_params.update(config_overrides)

        return cls.from_external_config(base_params)
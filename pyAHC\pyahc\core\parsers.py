"""将 AHC 格式的 ascii 文件解析为 pyAHC 对象的函数。

pyAHC 能够直接与经典的 AHC 输入文件交互。
此模块中定义的解析器用于 `pyahc.core.fields` 模块中定义的自定义字段验证器。
这些函数将 AHC 格式的 ascii 文件转换为（或反序列化）pyAHC 对象。

此模块中的解析器：
    parse_string_list: 将 AHC 字符串列表转换为字符串列表。
    parse_quoted_string: 确保从源中删除不必要的引号。
    parse_day_month: 将字符串转换为仅包含日期和月份的日期对象。
"""

from datetime import date
from typing import Union


def parse_string_list(value: str) -> str:
    """将 AHC 字符串列表转换为字符串列表。"""
    if value is None:
        return None
    if isinstance(value, list):
        return value
    if isinstance(value, str):
        return value.strip("'").split(",")


def parse_float_list(value: str) -> str:
    """将 AHC 字符串列表转换为字符串列表。"""
    if value is None:
        return None
    if isinstance(value, list):
        return value
    if isinstance(value, str):
        return value.strip("'").split(" ")


def parse_int_list(value: str) -> str:
    """将 AHC 字符串列表转换为字符串列表。"""
    if value is None:
        return None
    if isinstance(value, list):
        return value
    if isinstance(value, str):
        return value.strip("'").split(" ")


def parse_decimal(value: str) -> str:
    """删除 Fortran 风格的小数点。"""
    if value is None:
        return None
    if isinstance(value, str):
        value = value.lower().replace("d", "e")
    return float(value)


def parse_quoted_string(value: str) -> str:
    """确保从源中删除不必要的引号。"""
    if value is None:
        return None
    if isinstance(value, str):
        return value.strip("'")
    msg = "Invalid type. Expected string"
    raise ValueError(msg)


def parse_day_month(value: Union[str, date]) -> date:
    """将字符串转换为仅包含日期和月份的日期对象。"""
    msg = "Invalid day-month format. Expected 'DD MM'"
    if value is None:
        return None
    if isinstance(value, date):
        return value
    if isinstance(value, str):
        try:
            day, month = map(int, value.split())
            return date(date.today().year, month, day)
        except (ValueError, TypeError):
            raise ValueError(msg) from None
    raise ValueError(msg)

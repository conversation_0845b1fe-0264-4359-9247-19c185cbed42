"""StateExtractor使用示例

本示例演示如何使用StateExtractor类从pyAHC模型结果中提取状态变量。
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入StateExtractor
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))
from pyahc.db.hdf5.state_extractor import StateExtractor


def create_sample_result_with_csv():
    """创建包含CSV数据的示例Result对象"""
    class MockResult:
        def __init__(self):
            # 创建示例CSV数据
            self.csv = pd.DataFrame({
                'DATETIME': pd.date_range('2023-01-01', periods=365, freq='D'),
                'moisture_layer1': np.random.uniform(0.20, 0.35, 365),
                'moisture_layer2': np.random.uniform(0.18, 0.32, 365),
                'moisture_layer3': np.random.uniform(0.15, 0.28, 365),
                'moisture_layer4': np.random.uniform(0.12, 0.25, 365),
                'lai': np.concatenate([
                    np.zeros(60),  # 冬季
                    np.linspace(0, 4, 120),  # 春季生长
                    np.linspace(4, 2, 120),  # 夏季成熟
                    np.linspace(2, 0, 65)   # 秋季收获
                ]),
                'biomass': np.concatenate([
                    np.zeros(60),  # 冬季
                    np.linspace(0, 8000, 120),  # 春季生长
                    np.linspace(8000, 8500, 120),  # 夏季成熟
                    np.linspace(8500, 8500, 65)   # 秋季收获
                ]),
                'root_depth': np.concatenate([
                    np.full(60, 0),  # 冬季
                    np.linspace(0, 60, 120),  # 春季生长
                    np.linspace(60, 80, 120),  # 夏季成熟
                    np.full(65, 80)   # 秋季收获
                ]),
                'groundwater': np.random.uniform(-200, -100, 365)
            })
            self.ascii = {}
    
    return MockResult()


def create_sample_result_with_ascii():
    """创建包含ASCII数据的示例Result对象"""
    class MockResult:
        def __init__(self):
            self.csv = None
            self.ascii = {
                'sba': """# Soil moisture balance file
# Layer  Moisture  Depth
1  0.28  10
2  0.25  30
3  0.22  60
4  0.19  100
""",
                'crop': """# Crop output file
# Variable  Value  Unit
LAI  3.2  m2/m2
Biomass  7500.0  kg/ha
Root_depth  65.0  cm
""",
                'hydro': """# Hydrological output
# Variable  Value  Unit
Groundwater_level  -145.0  cm
Water_table  -145.0  cm
"""
            }
    
    return MockResult()


def demonstrate_basic_extraction():
    """演示基本的状态变量提取"""
    print("\n=== 基本状态变量提取示例 ===")
    
    # 创建示例结果对象
    result = create_sample_result_with_csv()
    
    # 提取所有状态变量
    print("提取所有状态变量...")
    states = StateExtractor.extract_all_states(result)
    
    print(f"成功提取 {len(states)} 个状态变量:")
    for var_name, value in states.items():
        if isinstance(value, np.ndarray):
            print(f"  {var_name}: 数组，形状={value.shape}, 范围=[{value.min():.3f}, {value.max():.3f}]")
        else:
            print(f"  {var_name}: {value:.3f}")


def demonstrate_individual_extraction():
    """演示单独提取各个状态变量"""
    print("\n=== 单独提取状态变量示例 ===")
    
    result = create_sample_result_with_csv()
    
    # 单独提取土壤含水量
    print("提取土壤含水量...")
    soil_moisture = StateExtractor.extract_soil_moisture(result)
    if soil_moisture is not None:
        print(f"  土壤含水量: {len(soil_moisture)} 层")
        for i, moisture in enumerate(soil_moisture):
            print(f"    第{i+1}层: {moisture:.3f} cm³/cm³")
    
    # 单独提取LAI
    print("提取LAI...")
    lai = StateExtractor.extract_crop_lai(result)
    if lai is not None:
        print(f"  LAI: {lai:.2f} m²/m²")
    
    # 单独提取生物量
    print("提取生物量...")
    biomass = StateExtractor.extract_crop_biomass(result)
    if biomass is not None:
        print(f"  生物量: {biomass:.1f} kg/ha")
    
    # 单独提取根深
    print("提取根深...")
    root_depth = StateExtractor.extract_root_depth(result)
    if root_depth is not None:
        print(f"  根深: {root_depth:.1f} cm")
    
    # 单独提取地下水位
    print("提取地下水位...")
    gw_level = StateExtractor.extract_groundwater_level(result)
    if gw_level is not None:
        print(f"  地下水位: {gw_level:.1f} cm")


def demonstrate_ascii_extraction():
    """演示从ASCII格式提取状态变量"""
    print("\n=== ASCII格式提取示例 ===")
    
    result = create_sample_result_with_ascii()
    
    # 从ASCII提取状态变量
    print("从ASCII格式提取状态变量...")
    states = StateExtractor.extract_all_states(result)
    
    print(f"从ASCII格式成功提取 {len(states)} 个状态变量:")
    for var_name, value in states.items():
        if isinstance(value, np.ndarray):
            print(f"  {var_name}: 数组，形状={value.shape}")
            for i, val in enumerate(value):
                print(f"    第{i+1}层: {val:.3f}")
        else:
            print(f"  {var_name}: {value:.3f}")


def demonstrate_validation():
    """演示数据验证功能"""
    print("\n=== 数据验证示例 ===")
    
    # 创建包含异常值的测试数据
    test_states = {
        'soil_moisture': np.array([-0.1, 0.5, 1.2]),  # 超出范围
        'lai': 20.0,  # 超出范围
        'biomass': 60000.0,  # 超出范围
        'root_depth': 600.0,  # 超出范围
        'groundwater_level': 50.0  # 超出范围
    }
    
    print("原始数据（包含异常值）:")
    for var_name, value in test_states.items():
        if isinstance(value, np.ndarray):
            print(f"  {var_name}: [{', '.join(f'{v:.1f}' for v in value)}]")
        else:
            print(f"  {var_name}: {value:.1f}")
    
    # 验证数据
    print("\n验证后的数据:")
    validated_states = StateExtractor.validate_extracted_states(test_states)
    
    for var_name, value in validated_states.items():
        if isinstance(value, np.ndarray):
            print(f"  {var_name}: [{', '.join(f'{v:.1f}' for v in value)}]")
        else:
            print(f"  {var_name}: {value:.1f}")


def demonstrate_error_handling():
    """演示错误处理"""
    print("\n=== 错误处理示例 ===")
    
    # 创建空的结果对象
    class EmptyResult:
        def __init__(self):
            self.csv = None
            self.ascii = {}
    
    empty_result = EmptyResult()
    
    print("从空结果对象提取状态变量...")
    states = StateExtractor.extract_all_states(empty_result)
    
    print(f"提取结果: {len(states)} 个状态变量")
    if not states:
        print("  没有找到任何状态变量（这是预期的）")


def main():
    """主函数"""
    print("StateExtractor使用示例")
    print("=" * 50)
    
    try:
        # 演示各种功能
        demonstrate_basic_extraction()
        demonstrate_individual_extraction()
        demonstrate_ascii_extraction()
        demonstrate_validation()
        demonstrate_error_handling()
        
        print("\n=== 示例完成 ===")
        print("StateExtractor可以成功从pyAHC模型结果中提取状态变量。")
        print("支持CSV和ASCII格式，具有数据验证和错误处理功能。")
        
    except Exception as e:
        logger.error(f"示例运行失败: {e}")
        raise


if __name__ == "__main__":
    main()

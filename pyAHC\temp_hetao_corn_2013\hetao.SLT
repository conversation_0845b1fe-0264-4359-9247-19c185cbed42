*******************************************************************************************************
* Filename: hetao.SLT
* Contents: AHC 1.0 - Solute data
*******************************************************************************************************
*c Case: Water and solute transport in the hetao area,
*******************************************************************************************************

*******************************************************************************************************
* Section 1: Number of salt species and calculation method
 Numsat = 1 ! Number of salt species [1...8] 
 SWCALM = 1 ! Choose numerical method [=0,1,2]
*                0 = Fully explicit finite-difference, only can be used when Numsat =1
*                1 = Fully implicit finite-difference, Numsat =1~8
*                2 = Crank-Nichoson scheme, Numsat =1~8
 CHRSUN = 1 ! Choose the unit for output file in *.SPI
*                1 = g/L, often for solute concentration
*                2 = g/kg, often for salt contant in mass
*                3 = mg/kg, often for nitrogen content in mass
 SWOCN  = 0 ! Switch, consider organic C&N turnover [Y=1, N=0]
*                only needed when doing N simulation (i.e. SWSTYP=2)
*******************************************************************************************************
  
*******************************************************************************************************
* Section 2: Top boundary condition
* List Solute conc. of different species (max 8) in precipitation [1..100 mg/cm3, R]
  CPRE = 
  0.0      
*******************************************************************************************************

*******************************************************************************************************
* Section 3: Diffusion, dispersion, and solute uptake by roots
* List molecular diffusion coefficient of different layers [0..10 cm2/day, R]
  DDIF =
  3.5      
* List Dispersion length of different species (max 8) [0..100 cm, R]
  LDIS = 
  20.00    
  20.00    
  20.00    
  20.00    
* List Relative uptake of solutes by roots for different species (max 8) [0..10, R]
  TSCF =
  0.0      
*******************************************************************************************************

*******************************************************************************************************
* Section 4: Adsorption 
*
  SWSP = 0 ! Switch, consider solute adsorption [Y=1, N=0]  
*
* If SWSP = 1 specify all belows:
* List Ks or KF coefficient for different species (max 8) [0..100 cm3/mg, R]
  KF=  
  0.05    
  0.05    
  0.05    
  0.05    
* List BETAXU exponent for different species (max 8 column) [0..10 -, R]
* if FREXP=1, means linear adsorption
  FREXP = 
  1.0      
  1.0      
  1.0      
  1.0      
* List Reference solute concentration for different species (max 8 col.) [0..1000 mg/cm3, R]
* [aa]
  CREF  =
  1.0      
* List Langmuir equation coefficient for different species (max 8 col.) [0..1000 mg/cm3, R]
*  [when using Langmuir, set value to 0]
  ETA = 
  0.1      
  0.1      
  0.1      
  0.1      
*******************************************************************************************************

*******************************************************************************************************
* Section 5: Decomposition  
*
  SWDC = 0 ! Choose, solute decomposition method [0,1,2]
*                0 = no solute decomposition
*                1 = reaction based on potential rate
*                2 = EPIC model for N
*
* If SWDC = 1 specify all belows:
* List Potential decomposition rate in liquid for different species (max 8 col.) [0..10 /d, R]
  DECPOT =  
  0.00     
  0.00     
  0.00     
  0.00     
* List Potential decomposition rate in solid for different species (max 8 col.) [0..10 /d, R]
  DECPOTS =  
  0.00     
  0.00     
  0.00     
  0.00     
* List 1st order Potential decomposition rate in liquid for N chain (max 8 col.) [0..10 /d, R]    N modification
  DECPOT_C = 
  0.00     
  0.00     
  0.00     
  0.00     
  0.00     
* List 1st order Potential decomposition rate in solid for N chain (max 8 col.) [0..10 /d, R]    N modification
  DECPOTS_C =  
  0.00     
  0.00     
  0.00     
  0.00     
  0.00     
* List Factor reduction decomposition due to temperature. for diff. species(max 8)[0..0.5/C,R]
  GAMPAR =   
  0.2      
  0.2      
  0.2      
  0.2      
  0.2      
* List Minimum water content for pot. decompos. rate for diff.species(max 8) [0..0.4cm3/cm3,R]
  RTHETA = 
  0.3      
  0.3      
  0.3      
  0.3      
  0.3      
* List Exponent in reduction decompos. due to dryness for diff. species(max 8) [0..2 -, R]
  BEXP   =
  1.2      
* List reduction of pot. decompos. for each soil layer for diff. species(max 8), [0..1 -, R]
* salt1  salt2   salt3  salt4  salt5
  FDEPTH =    
  0.10     
  0.10     
  0.10     
  0.10     
  0.10     
* End of table
* If SWDC = 2 specify all belows: soil chemical information for EPIC
* DepC/cm  CECLAY  SOILPHLAY
  CHM_INF =
  30.0     5.0      7.8	     
  45.0     5.0      7.8	     
  70.0     5.0      7.8	     
  100.0     5.0      7.8	     
  300.0     5.0      7.8	     
* End of table    N modification
*******************************************************************************************************

*******************************************************************************************************
* Section 6: Transfer between mobile and immobile water volumes (if present)
*
  SWPREF = 0! Switch, consider mobile-immobile water volumes [Y=1, N=0]
*
* If SWPREF = 1 specify K_MOBIL:
* List Solute transfer coef. between mobile-immobile parts for diff.species(max 8)[0..100/d,R]
  KMOBIL =   
  0.1      
*******************************************************************************************************

*******************************************************************************************************
* Section 7: Solute residence in the saturated zone
*
  SWBR = 0! Switch, consider mixed reservoir of saturated zone [Y=1, N=0] 
*
* If SWBR = 0 specify C_DRAIN:
* List Solute concentration in aquifer for diff.species(max 8 col.) [0..100 mg/cm3, R]
  CDRAIN  = 
  2.0      
*
*
* If SWBR = 1 specify all belows:
  HAQUIF  = 290 ! Thickness saturated part of aquifer [0..10000 cm, R]
  POROS   = 0.3 ! Porosity of aquifer [0..0.6 cm3/cm3, R]
* List Linear adsorption coefficient in aquifer for diff.species(max 8) [0..100 cm3/mg, R]
  KFSAT   =  
  10.3     
* List Decomposition rate in aquifer for diff.species(max 8) [0..10 /d, R]
  DECSAT  = 
  1.2      
* List Initial solute concentration in groundwater for diff.species(max 8) [0..100 mg/cm3, R]
  CDRAINI = 
  1.7      
*
*******************************************************************************************************

*******************************************************************************************************
* Section 8:  Initial condition
* List initial solute concentration of each comp. (max 8 col.), [1..1000 mg/cm3, R]:
  CMLI =
  14.5     
  14.5     
  14.5     
  14.5     
  12.4     
  12.4     
  12.4     
  12.4     
  12.4     
  12.4     
  5.5     
  5.5     
  5.5     
  5.5     
  5.5     
  5.5     
  5.5     
  5.5     
  5.5     
  5.5     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.2     
  3.0     
  3.0     
  3.0     
  3.0     
  3.0     
  3.0     
  3.0     
  3.0     
  3.0     
  3.0     
  3.0     
  3.0     
  3.0     
  3.0     
  3.0     
  3.0     
  3.0     
  3.0     
  3.0     
  3.0     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
  2.6     
*End of table
* End of file *********************************************************************************
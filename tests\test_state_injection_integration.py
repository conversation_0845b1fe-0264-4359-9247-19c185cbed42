"""StateInjector集成测试"""
import pytest
import numpy as np
from datetime import date
import tempfile
import os
from pathlib import Path

from pyahc.db.hdf5.state_injector import StateInjector
from pyahc.db.hdf5.state_extractor import StateExtractor


class TestStateInjectionIntegration:
    """StateInjector集成测试类"""
    
    def setup_method(self):
        """测试前的设置"""
        self.test_states = {
            'soil_moisture': np.array([0.35, 0.30, 0.25, 0.20]),
            'lai': 4.0,
            'biomass': 2000.0,
            'root_depth': 90.0,
            'groundwater_level': -180.0
        }
    
    def _create_test_model(self):
        """创建测试用的真实模型对象"""
        try:
            from pyahc.model.model import Model
            from pyahc.components.soilwater import SoilMoisture
            from pyahc.components.crop import Crop
            from pyahc.components.boundary import BottomBoundary
            from pyahc.components.general import GeneralSettings
            from pyahc.components.meteorology import Meteorology
            
            # 创建基本的模型组件
            general_settings = GeneralSettings(
                project="Test Project",
                pathwork=str(Path.cwd()),
                swscre=0,
                swerror=1,
                extensions=["csv"]
            )
            
            # 创建土壤含水量组件
            soil_moisture = SoilMoisture(
                swinco=0,
                thetai=[0.40, 0.35, 0.30, 0.25]
            )
            
            # 创建作物组件
            crop = Crop(
                swcrop=1,
                leaf_area_index=3.0,
                biomass=1500.0,
                rds=75.0
            )
            
            # 创建底边界组件
            bottom_boundary = BottomBoundary(
                swbbcfile=0,
                swbotb=1,
                gwlevel_data=[(1, 1, -150.0), (31, 12, -150.0)]
            )
            
            # 创建气象组件（简化）
            meteorology = Meteorology(
                swmetfil=0
            )
            
            # 创建模型
            model = Model(
                generalsettings=general_settings,
                soilmoisture=soil_moisture,
                crop=crop,
                bottomboundary=bottom_boundary,
                meteorology=meteorology
            )
            
            return model
            
        except ImportError as e:
            pytest.skip(f"Cannot import required components: {e}")
    
    def test_end_to_end_state_injection(self):
        """测试端到端的状态注入流程"""
        # 创建测试模型
        original_model = self._create_test_model()
        
        # 验证原始状态
        assert original_model.soilmoisture.thetai != self.test_states['soil_moisture'].tolist()
        assert original_model.crop.leaf_area_index != self.test_states['lai']
        
        # 执行状态注入
        updated_model = StateInjector.inject_all_states(original_model, self.test_states)
        
        # 验证注入结果
        assert updated_model is not None
        assert updated_model != original_model  # 应该是不同的对象
        
        # 验证土壤含水量注入
        assert np.allclose(updated_model.soilmoisture.thetai, self.test_states['soil_moisture'])
        
        # 验证作物状态注入
        assert abs(updated_model.crop.leaf_area_index - self.test_states['lai']) < 1e-6
        
        # 验证地下水位注入
        if updated_model.bottomboundary.gwlevel_data:
            injected_gw_level = updated_model.bottomboundary.gwlevel_data[0][2]
            assert abs(injected_gw_level - self.test_states['groundwater_level']) < 1e-6
    
    def test_partial_state_injection(self):
        """测试部分状态变量注入"""
        original_model = self._create_test_model()
        
        # 只注入土壤含水量和LAI
        partial_states = {
            'soil_moisture': self.test_states['soil_moisture'],
            'lai': self.test_states['lai']
        }
        
        updated_model = StateInjector.inject_all_states(original_model, partial_states)
        
        # 验证只有指定的状态被注入
        assert np.allclose(updated_model.soilmoisture.thetai, partial_states['soil_moisture'])
        assert abs(updated_model.crop.leaf_area_index - partial_states['lai']) < 1e-6
        
        # 验证其他状态保持不变
        assert updated_model.crop.rds == original_model.crop.rds
    
    def test_state_injection_with_invalid_data(self):
        """测试使用无效数据的状态注入"""
        original_model = self._create_test_model()
        
        invalid_states = {
            'soil_moisture': np.array([1.5, -0.1, 0.3, 0.25]),  # 超出范围
            'lai': -1.0,  # 负值
            'biomass': 'invalid',  # 非数值
            'groundwater_level': None  # 空值
        }
        
        # 应该能够处理无效数据而不崩溃
        updated_model = StateInjector.inject_all_states(original_model, invalid_states)
        
        assert updated_model is not None
        # 土壤含水量应该被裁剪到有效范围
        assert all(0 <= val <= 1 for val in updated_model.soilmoisture.thetai)
    
    def test_state_injection_preserves_original_model(self):
        """测试状态注入保持原始模型不变"""
        original_model = self._create_test_model()
        
        # 记录原始值
        original_moisture = original_model.soilmoisture.thetai.copy()
        original_lai = original_model.crop.leaf_area_index
        original_rds = original_model.crop.rds
        
        # 执行状态注入
        updated_model = StateInjector.inject_all_states(original_model, self.test_states)
        
        # 验证原始模型未被修改
        assert original_model.soilmoisture.thetai == original_moisture
        assert original_model.crop.leaf_area_index == original_lai
        assert original_model.crop.rds == original_rds
        
        # 验证更新后的模型有不同的值
        assert updated_model.soilmoisture.thetai != original_moisture
        assert updated_model.crop.leaf_area_index != original_lai
    
    def test_state_injection_with_missing_components(self):
        """测试缺少组件时的状态注入"""
        # 创建缺少某些组件的模型
        try:
            from pyahc.model.model import Model
            from pyahc.components.general import GeneralSettings
            
            # 只创建基本设置，缺少其他组件
            general_settings = GeneralSettings(
                project="Test Project",
                pathwork=str(Path.cwd()),
                swscre=0,
                swerror=1
            )
            
            incomplete_model = Model(
                generalsettings=general_settings
                # 缺少soilmoisture, crop, bottomboundary等组件
            )
            
            # 尝试注入状态，应该能够优雅地处理缺少的组件
            result = StateInjector.inject_all_states(incomplete_model, self.test_states)
            
            assert result is not None
            assert result == incomplete_model  # 应该返回原始模型
            
        except ImportError:
            pytest.skip("Cannot import required components")
    
    def test_state_injection_performance(self):
        """测试状态注入的性能"""
        import time
        
        original_model = self._create_test_model()
        
        # 测试单次注入时间
        start_time = time.time()
        updated_model = StateInjector.inject_all_states(original_model, self.test_states)
        end_time = time.time()
        
        injection_time = end_time - start_time
        
        # 验证注入时间小于0.1秒（性能要求）
        assert injection_time < 0.1, f"State injection took {injection_time:.3f}s, should be < 0.1s"
        assert updated_model is not None
    
    def test_state_injection_memory_usage(self):
        """测试状态注入的内存使用"""
        try:
            import psutil
            import os
        except ImportError:
            pytest.skip("psutil not available")
        
        original_model = self._create_test_model()
        
        # 获取初始内存使用
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # 执行多次状态注入
        for _ in range(10):
            updated_model = StateInjector.inject_all_states(original_model, self.test_states)
        
        # 获取最终内存使用
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # 验证内存增长合理（小于50MB）
        assert memory_increase < 50 * 1024 * 1024, f"Memory increased by {memory_increase / 1024 / 1024:.1f}MB"
    
    def test_state_injection_with_different_layer_counts(self):
        """测试不同土壤层数的状态注入"""
        original_model = self._create_test_model()
        
        # 测试更少的层数
        fewer_layers = np.array([0.35, 0.30])
        states_fewer = {'soil_moisture': fewer_layers}
        
        result = StateInjector.inject_all_states(original_model, states_fewer)
        assert len(result.soilmoisture.thetai) == len(original_model.soilmoisture.thetai)
        
        # 测试更多的层数
        more_layers = np.array([0.35, 0.30, 0.25, 0.20, 0.15, 0.10])
        states_more = {'soil_moisture': more_layers}
        
        result = StateInjector.inject_all_states(original_model, states_more)
        assert len(result.soilmoisture.thetai) == len(original_model.soilmoisture.thetai)
    
    def test_state_injection_validation_accuracy(self):
        """测试状态注入验证的准确性"""
        original_model = self._create_test_model()
        
        # 执行状态注入
        updated_model = StateInjector.inject_all_states(original_model, self.test_states)
        
        # 手动验证注入结果
        validation_result = StateInjector._validate_injection_result(updated_model, self.test_states)
        
        assert validation_result is True
        
        # 测试验证失败的情况
        wrong_states = {
            'soil_moisture': np.array([0.1, 0.1, 0.1, 0.1]),  # 与注入的值不同
            'lai': 1.0  # 与注入的值不同
        }
        
        validation_result = StateInjector._validate_injection_result(updated_model, wrong_states)
        assert validation_result is False


if __name__ == '__main__':
    pytest.main([__file__])

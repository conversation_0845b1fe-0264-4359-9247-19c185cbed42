# Aquacrop-EnKF
基于 Aquacrop-OS 作物模型和集合卡尔曼滤波器的作物生长模拟系统。

# 亮点
## 在不干扰模型时钟/时间线的情况下同化物候观测数据。

**背景或问题：** 在作物生长数据同化系统中，模拟和观测物候之间的不匹配显著降低了作物生长建模的性能。这种情况在小农户管理的田地中可能更为严重，即使在气候条件相对均匀的情况下，物候异质性也很高。以往的研究调查了回顾性同化历史物候观测的非序列方法。然而，通过序列数据同化方法动态同化物候测量的方法仍未得到探索。

**目标或研究问题：** 动态物候同化最棘手的挑战之一是相当大比例的模型参数和变量与物候纠缠在一起，因此简单地同化物候测量可能会干扰模型时钟。本研究旨在建立一个强健的作物数据同化框架，能够在不干扰模型时钟的情况下实时同化物候测量。

**引用：** Yang, Q., Shi, L., Han, J., Zha, Y., Yu, J., Wu, W. and Huang, K., 2023. Regulating the time of the crop model clock: A data assimilation framework for regions with high phenological heterogeneity. Field Crops Research, 293, p.108847.

# 使用方法
## 安装 Matlab 的 Python API

```# 找到你的 Matlab 路径```<br>
```cd *:\***\Polyspace\R2019b\extern\engines\python```<br>
```python setup.py install```<br>
或参考[此页面](https://www.mathworks.com/help/matlab/matlab_external/install-the-matlab-engine-for-python.html)

## 验证官方 FAO-AquaCrop (v6.0) 和 AquaCropOS (v6.0) 之间的一致性

**DAA_OS_vs_official_climate.py:** 比较 Aquacrop-OS 和 FAO-Aquacrop 在各种气候情景下的一致性

**DAA_OS_vs_official_parameter.py** 比较 Aquacrop-OS 和 FAO-Aquacrop 在不同参数组合下的一致性

## 两步全局敏感性分析

```pip install salib # 安装外部 SA 库```

**rain_frequency_sort.py:** 此脚本用于从随机天气数据中找出典型年份（即湿润年、正常年和干旱年）。

**SA_aquacrop_morris.py:** Morris 方法 (Morris, 1991) 用于筛选出 AquaCropOS 中的不敏感参数（参数详情在补充材料表 S1 中描述）。该方法具有高计算效率，因此适合在定量分析前进行快速预选。 

**SA_aquacrop_sobol.py** the sensitivity of the remaining parameters was quantitatively analyzed by Sobol’ method (Sobol’, 1990)
Morris, M.D., 1991. Factorial sampling plans for preliminary computational experiments. Technometrics 33, 161–174. https://doi.org/10.1080/00401706.1991.10484804
Sobol’, I.M., 1990. On sensitivity estimation for nonlinear mathematical models. Mat. Model 2, 112–118.

## 观测系统模拟实验 (OSSE)

**DAA_EnKF_batchOSS.py:** 仅同化冠层覆盖度 (CC) 的测量值。

**DAA_EnKF_batchOSS_phenoShift.py:** 在错误种植日期下仅同化冠层覆盖度 (CC) 的测量值。

**DAA_EnKF_batchOSS_2obs.py:** 同化冠层覆盖度 (CC) 和地上生物量。

**DAA_EnKF_batchOSS_3obs_phenoShift.py:** 在错误种植日期下同化冠层覆盖度 (CC)、地上生物量和物候（由 GDD 表示）。

**DAA_EnKF_batchOSS_3obs_phenoShift_restartEnKF.py:** 在错误种植日期下使用 restartEnKF 策略同化冠层覆盖度 (CC)、地上生物量和物候（由 GDD 表示）。

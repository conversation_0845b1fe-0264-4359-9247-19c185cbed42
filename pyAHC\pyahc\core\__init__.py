"""pyAHC 的核心功能。

核心包包含 AHC 模型的主要类和函数。它主要由包内部使用。
没有直接向用户公开任何功能。

模块：
    basemodel: 所有 pyAHC 模型继承的基模型。
    defaults: 包中共享的变量。
    fields: pyAHC 序列化和反序列化的自定义字段类型。
    mixins: 增强特定 PyAHCBaseModel 功能的可重用混入。
    parsers: 将 AHC 格式的 ascii 文件解析为 pyAHC 对象的函数。
    serializers: 微调 pyAHC 对象序列化为 AHC 格式 ASCII 的函数。
    valueranges: pyahc 验证中使用的 pydantic Field 对象的取值范围。

子包：
    cli: pyAHC 的命令行界面（原型功能）。
    io: pyAHC 的输入/输出功能。
    db: pyAHC 的数据库集成。

资源：
    validation.yaml: 包含 pyAHC 模型验证模式的 YAML 文件
"""

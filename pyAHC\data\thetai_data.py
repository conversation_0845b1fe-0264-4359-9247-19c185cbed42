#!/usr/bin/env python3
"""THETAI 土壤初始含水量数据

这个文件包含了hetao_corn_2013案例中使用的标准THETAI数据。
数据来源于标准案例文件，总计300个值，代表不同土壤层的初始含水量。

数据结构说明：
- 第208行: 0.26 × 10个值
- 第209行: 0.32 × 10个值  
- 第210-211行: 0.30 × 20个值
- 第212-213行: 0.34 × 20个值
- 第214-215行: 0.39 × 20个值
- 第216-217行: 0.38 × 20个值
- 第218行: 0.38×2 + 0.39×4 + 0.40×4 = 10个值
- 第219行: 0.40×1 + 0.41×5 + 0.42×4 = 10个值
- 第220行: 0.43×5 + 0.44×5 = 10个值
- 第221行: 0.45×4 + 0.46×6 = 10个值
- 第222-231行: 0.47 × 100个值
- 第232-237行: 0.45 × 60个值

总计：300个值
"""

# 总计300个值
STANDARD_THETAI = (
    [0.26] * 10 +   # 第208行: 0.26 × 10
    [0.32] * 10 +   # 第209行: 0.32 × 10
    [0.30] * 20 +   # 第210-211行: 0.30 × 20
    [0.34] * 20 +   # 第212-213行: 0.34 × 20
    [0.39] * 20 +   # 第214-215行: 0.39 × 20
    [0.38] * 20 +   # 第216-217行: 0.38 × 20
    # 第218行: 0.38×2 + 0.39×4 + 0.40×4 = 10个
    [0.38, 0.38, 0.39, 0.39, 0.39, 0.39, 0.40, 0.40, 0.40, 0.40] +
    # 第219行: 0.40×1 + 0.41×5 + 0.42×4 = 10个
    [0.40, 0.41, 0.41, 0.41, 0.41, 0.41, 0.42, 0.42, 0.42, 0.42] +
    # 第220行: 0.43×5 + 0.44×5 = 10个
    [0.43, 0.43, 0.43, 0.43, 0.43, 0.44, 0.44, 0.44, 0.44, 0.44] +
    # 第221行: 0.45×4 + 0.46×6 = 10个
    [0.45, 0.45, 0.45, 0.45, 0.46, 0.46, 0.46, 0.46, 0.46, 0.46] +
    [0.47] * 100 +  # 第222-231行: 0.47 × 100
    [0.45] * 60     # 第232-237行: 0.45 × 60
)

# 验证数据长度
assert len(STANDARD_THETAI) == 300, f"THETAI数据长度应为300，实际为{len(STANDARD_THETAI)}"

# 数据统计信息
THETAI_STATS = {
    'total_values': len(STANDARD_THETAI),
    'min_value': min(STANDARD_THETAI),
    'max_value': max(STANDARD_THETAI),
    'unique_values': len(set(STANDARD_THETAI)),
    'description': '标准hetao_corn_2013案例的土壤初始含水量数据'
}

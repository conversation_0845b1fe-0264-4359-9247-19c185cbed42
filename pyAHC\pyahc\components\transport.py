# mypy: disable-error-code="call-overload, misc, override"

from typing import Literal as _Literal, Optional as _Optional, List as _List, Dict as _Dict, Any as _Any

from pydantic import Field as _Field, model_validator as _model_validator

from pyahc.components.tables import INITSOILTEMP, SOILTEXTURES
from pyahc.core.basemodel import PyAHCBaseModel as _PyAHCBaseModel
from pyahc.core.configurable import ConfigurableComponent as _ConfigurableComponent
from pyahc.core.fields import (
    Decimal2f as _Decimal2f,
    String as _String,
    Table as _Table,
    PositiveFloat as _PositiveFloat,
    ConcentrationFloat as _ConcentrationFloat,
    TemperatureFloat as _TemperatureFloat,
)
from pyahc.core.valueranges import YEARRANGE as _YEARRANGE
from pyahc.utils.mixins import (
    SerializableMixin as _SerializableMixin,
    YAMLValidatorMixin as _YAMLValidatorMixin,
    FileMixin as _FileMixin,
)

__all__ = ["HeatFlow", "SoluteTransport", "Transport", "SOILTEXTURES", "INITSOILTEMP"]


class Transport(_ConfigurableComponent, _YAMLValidatorMixin, _FileMixin):
    """重构后的传输组件 - 支持溶质和热传输参数验证和外部配置

    新增功能：
    - 溶质传输参数验证（弥散度、扩散系数、初始浓度等）
    - 热传输参数验证（热导率、热容量、初始温度等）
    - 条件参数验证（根据传输类型开关验证相应的必需参数）
    - 参数外部化配置支持
    - 物理约束验证（确保传输参数符合物理约束条件）

    属性：
        # 传输类型开关（外部化配置）
        solute_transport (Literal[0, 1]): 溶质传输开关
        heat_transport (Literal[0, 1]): 热传输开关

        # 溶质传输参数（外部化配置）
        dispersivity (Optional[PositiveFloat]): 弥散度
        diffusion_coefficient (Optional[PositiveFloat]): 扩散系数
        initial_concentration (Optional[ConcentrationFloat]): 初始浓度

        # 热传输参数（外部化配置）
        thermal_conductivity (Optional[PositiveFloat]): 热导率
        heat_capacity (Optional[PositiveFloat]): 热容量
        initial_temperature (Optional[TemperatureFloat]): 初始温度

        # 原有组件（保持向后兼容性）
        heatflow (Optional[HeatFlow]): 热流组件
        solutetransport (Optional[SoluteTransport]): 溶质传输组件
    """

    # 传输类型开关（外部化配置）
    solute_transport: _Literal[0, 1] = _Field(default=0, description="溶质传输开关")
    heat_transport: _Literal[0, 1] = _Field(default=0, description="热传输开关")

    # 溶质传输参数（外部化配置）
    dispersivity: _Optional[_PositiveFloat] = _Field(default=None, description="弥散度 [cm]")
    diffusion_coefficient: _Optional[_PositiveFloat] = _Field(default=None, description="扩散系数 [cm²/d]")
    initial_concentration: _Optional[_ConcentrationFloat] = _Field(default=None, description="初始浓度 [mg/cm³]")

    # 热传输参数（外部化配置）
    thermal_conductivity: _Optional[_PositiveFloat] = _Field(default=None, description="热导率 [J/cm/K/d]")
    heat_capacity: _Optional[_PositiveFloat] = _Field(default=None, description="热容量 [J/cm³/K]")
    initial_temperature: _Optional[_TemperatureFloat] = _Field(default=None, description="初始温度 [°C]")

    # 原有组件（保持向后兼容性）
    heatflow: _Optional['HeatFlow'] = _Field(default=None, description="热流组件")
    solutetransport: _Optional['SoluteTransport'] = _Field(default=None, description="溶质传输组件")

    @_model_validator(mode="after")
    def validate_transport_parameters(self):
        """验证传输参数的条件依赖和物理约束"""
        if not self._validation:
            return self

        # 溶质传输参数验证
        if self.solute_transport == 1:
            self._validate_solute_transport_parameters()

        # 热传输参数验证
        if self.heat_transport == 1:
            self._validate_heat_transport_parameters()

        # 验证参数的物理合理性
        self._validate_physical_constraints()

        return self

    def _validate_solute_transport_parameters(self):
        """验证溶质传输必需参数"""
        required_solute = ['dispersivity', 'diffusion_coefficient']
        missing_solute = [p for p in required_solute if getattr(self, p) is None]
        if missing_solute:
            raise ValueError(f"solute_transport=1 requires: {missing_solute}")

        # 验证溶质传输参数范围
        if self.dispersivity is not None and self.dispersivity <= 0:
            raise ValueError("Dispersivity must be > 0")

        if self.diffusion_coefficient is not None and self.diffusion_coefficient <= 0:
            raise ValueError("Diffusion coefficient must be > 0")

    def _validate_heat_transport_parameters(self):
        """验证热传输必需参数"""
        required_heat = ['thermal_conductivity', 'heat_capacity']
        missing_heat = [p for p in required_heat if getattr(self, p) is None]
        if missing_heat:
            raise ValueError(f"heat_transport=1 requires: {missing_heat}")

        # 验证热传输参数范围
        if self.thermal_conductivity is not None and self.thermal_conductivity <= 0:
            raise ValueError("Thermal conductivity must be > 0")

        if self.heat_capacity is not None and self.heat_capacity <= 0:
            raise ValueError("Heat capacity must be > 0")

    def _validate_physical_constraints(self):
        """验证物理约束条件"""
        # 验证弥散度的合理范围（通常在0.1-100 cm之间）
        if self.dispersivity is not None:
            if self.dispersivity > 1000:
                raise ValueError(f"Dispersivity {self.dispersivity} cm seems unreasonably large (>1000 cm)")

        # 验证扩散系数的合理范围（通常在1e-10到1e-5 cm²/s之间，转换为cm²/d）
        if self.diffusion_coefficient is not None:
            # 转换为cm²/s进行检查 (1 cm²/d = 1.157e-5 cm²/s)
            diff_coeff_per_sec = self.diffusion_coefficient * 1.157e-5
            if diff_coeff_per_sec > 1e-3:
                raise ValueError(f"Diffusion coefficient {self.diffusion_coefficient} cm²/d seems unreasonably large")

        # 验证热导率的合理范围（土壤热导率通常在0.1-5 W/m/K之间）
        if self.thermal_conductivity is not None:
            # 假设输入单位为J/cm/K/d，转换为W/m/K进行检查
            # 1 W/m/K ≈ 86.4 J/cm/K/d
            thermal_cond_w_per_mk = self.thermal_conductivity / 86.4
            if thermal_cond_w_per_mk > 10:
                raise ValueError(f"Thermal conductivity {self.thermal_conductivity} J/cm/K/d seems unreasonably large")

        # 验证热容量的合理范围（土壤热容量通常在1-4 MJ/m³/K之间）
        if self.heat_capacity is not None:
            # 假设输入单位为J/cm³/K，转换为MJ/m³/K进行检查
            # 1 MJ/m³/K = 1 J/cm³/K
            if self.heat_capacity > 10:
                raise ValueError(f"Heat capacity {self.heat_capacity} J/cm³/K seems unreasonably large")

    def get_effective_heatflow(self) -> _Optional['HeatFlow']:
        """获取有效的热流组件（优先使用外部配置）"""
        if self.heat_transport == 1:
            if self.heatflow is not None:
                return self.heatflow
            else:
                # 使用外部配置创建默认热流组件
                return HeatFlow(
                    swhea=1,
                    swshf=2,  # 数值方法
                    tampli=10.0,
                    tmean=self.initial_temperature or 15.0,
                    ddamp=50.0,
                    timref=90.0,
                    swessm=0,
                    hconsm=self.thermal_conductivity or 1.0,
                    tempi=[self.initial_temperature or 12.0] * 300
                )
        return self.heatflow

    def get_effective_solutetransport(self) -> _Optional['SoluteTransport']:
        """获取有效的溶质传输组件（优先使用外部配置）"""
        if self.solute_transport == 1:
            if self.solutetransport is not None:
                return self.solutetransport
            else:
                # 使用外部配置创建默认溶质传输组件
                return SoluteTransport(
                    swsolu=1,
                    numsat=1,
                    swcalm=1,
                    chrsun=1,
                    swocn=0,
                    cpre=self.initial_concentration or 0.1,
                    ddif=self.diffusion_coefficient or 1.0,
                    ldis=[self.dispersivity or 20.0] * 4,
                    tscf=1.0,
                    swsp=0
                )
        return self.solutetransport


class HeatFlow(_ConfigurableComponent, _YAMLValidatorMixin, _FileMixin):
    """重构后的热流组件 - 支持热传输参数验证和外部配置

    新增功能：
    - 热传输参数验证（温度范围、热导率、热容量等）
    - 条件参数验证（根据热流开关验证相应的必需参数）
    - 参数外部化配置支持
    - 物理约束验证（确保热传输参数符合物理约束条件）

    属性：
        swhea (Literal[0, 1]): 热流开关
        swcalt (Optional[Literal[1, 2]]): 计算方法选择
            * 1 - 解析方法
            * 2 - 数值方法
        tampli (Optional[TemperatureFloat]): 土壤表面年温度波幅 [0..50 °C]
        tmean (Optional[TemperatureFloat]): 土壤表面年平均温度 [-10..30 °C]
        timref (Optional[Decimal2f]): 正弦温度波达到峰值的时间 [0..366.0 d]
        ddamp (Optional[PositiveFloat]): 土壤温度波的衰减深度 [1..500 cm]
        swtopbhea (Optional[Literal[1, 2]]): 定义顶部边界条件
            * 1 - 使用气象输入文件中的气温作为顶部边界
            * 2 - 使用测量的表层土壤温度作为顶部边界
        tsoilfile (Optional[str]): 包含土壤表面温度的输入文件名，不带 .TSS 扩展名
        swbotbhea (Optional[Literal[1, 2]]): 定义底部边界条件
            * 1 - 无热通量
            * 2 - 规定底部温度
        soiltextures (Optional[Table]): 每个物理土壤层的土壤质地（g/g 矿物部分）和有机质含量（g/g 干土）
        initsoiltemp (Optional[Table]): 初始温度 TSOIL [-50..50 °C] 作为土壤深度 ZH [-100000..0 cm] 的函数
        bbctsoil (Optional[Table]): 底部边界温度 TBOT [-50..50 °C] 作为日期 DATET [日期] 的函数
        swshf (Optional[Literal[1, 2, 3]]): 热流计算方法
        swessm (Optional[Literal[0, 1]]): 是否考虑土壤表面材料的影响
        hconsm (Optional[PositiveFloat]): 土壤表面材料的热导率 [0..20000 J/cm/K/d]
        tempi (Optional[List[TemperatureFloat]]): 初始温度数组 [-50..50 °C]

        # 新增外部化参数
        thermal_diffusivity (Optional[PositiveFloat]): 热扩散率 [cm²/d]
        surface_heat_flux (Optional[float]): 地表热通量 [J/cm²/d]
        bottom_temperature (Optional[TemperatureFloat]): 底部边界温度 [°C]
    """

    swhea: _Literal[0, 1] | None = None
    swcalt: _Literal[1, 2] | None = None
    tampli: _TemperatureFloat | None = _Field(None, ge=0, le=50, description="土壤表面年温度波幅")
    tmean: _TemperatureFloat | None = _Field(None, ge=-10, le=30, description="土壤表面年平均温度")
    timref: _Decimal2f | None = _Field(None, **_YEARRANGE, description="正弦温度波达到峰值的时间")
    ddamp: _PositiveFloat | None = _Field(None, ge=1, le=500, description="土壤温度波的衰减深度")
    swtopbhea: _Literal[1, 2] | None = None
    tsoilfile: _String | None = None
    swbotbhea: _Literal[1, 2] | None = None
    soiltextures: _Table | None = None
    initsoiltemp: _Table | None = None
    bbctsoil: _Table | None = None
    swshf: _Literal[1, 2, 3] | None = None
    swessm: _Literal[0, 1] | None = None
    hconsm: _PositiveFloat | None = _Field(None, ge=0, le=20000, description="土壤表面材料的热导率")
    tempi: _List[_TemperatureFloat] | None = None

    # 新增外部化参数
    thermal_diffusivity: _Optional[_PositiveFloat] = _Field(default=None, description="热扩散率 [cm²/d]")
    surface_heat_flux: _Optional[float] = _Field(default=None, description="地表热通量 [J/cm²/d]")
    bottom_temperature: _Optional[_TemperatureFloat] = _Field(default=None, description="底部边界温度 [°C]")

    @_model_validator(mode="after")
    def validate_heat_parameters(self):
        """验证热传输参数的条件依赖和物理约束"""
        if not self._validation:
            return self

        # 当启用热流时验证必需参数
        if self.swhea == 1:
            self._validate_required_heat_parameters()

        # 验证温度参数的物理合理性
        self._validate_temperature_constraints()

        return self

    def _validate_required_heat_parameters(self):
        """验证热流必需参数"""
        if self.swshf is None:
            raise ValueError("swhea=1 requires swshf (calculation method)")

        if self.swshf == 2:  # 数值方法需要更多参数
            required_params = ['tampli', 'tmean', 'ddamp', 'timref']
            missing_params = [p for p in required_params if getattr(self, p) is None]
            if missing_params:
                raise ValueError(f"swshf=2 (numerical method) requires: {missing_params}")

    def _validate_temperature_constraints(self):
        """验证温度参数的物理约束"""
        # 验证温度波幅和平均温度的合理性
        if self.tampli is not None and self.tmean is not None:
            if self.tampli > abs(self.tmean) + 50:
                raise ValueError(f"Temperature amplitude {self.tampli}°C seems unreasonably large relative to mean {self.tmean}°C")

        # 验证初始温度数组的一致性
        if self.tempi is not None:
            temp_range = max(self.tempi) - min(self.tempi)
            if temp_range > 100:
                raise ValueError(f"Initial temperature range {temp_range}°C seems unreasonably large")

        # 验证热扩散率的合理范围（土壤热扩散率通常在0.1-10 cm²/d之间）
        if self.thermal_diffusivity is not None:
            if self.thermal_diffusivity > 100:
                raise ValueError(f"Thermal diffusivity {self.thermal_diffusivity} cm²/d seems unreasonably large")

    def model_string(self, project_name: str = "hetao") -> str:
        """生成严格符合V201格式的HEA文件内容。

        Args:
            project_name: 项目名称，用于文件头部
        """
        lines = []

        # 文件头部
        lines.append("*" * 82)
        lines.append(f"* Filename: {project_name}.HEA ")
        lines.append("* Contents: AHC 1.0 - Heat flow data ")
        lines.append("*" * 82 + " ")
        lines.append("* Comment area: ")
        lines.append("* ")
        lines.append(f"* C Case: Water and heat transport in the {project_name} area, ")
        lines.append("* C       agricultural simulation case ")
        lines.append("* C ")
        lines.append("* C Example of the User's Guide: reference situation ")
        lines.append("* C ")
        lines.append("* C A set of input data to explore AHC")
        lines.append("*" * 82)
        lines.append(" ")
        
        # Section 1: Method
        lines.append("*" * 82)
        lines.append("* Section 1: Method ")
        lines.append("* ")
        swshf_val = self.swshf if self.swshf is not None else 2
        lines.append(f"  SWSHF = {swshf_val}  ! Switch, method:  ")
        lines.append("*                 1 = Use analytical method")
        lines.append("*                 2 = Use numerical method with only soil conductance  ")
        lines.append("*                 3 = Use numerical method with both conductance and advection  ")
        lines.append("*" * 82 + " ")
        lines.append(" ")
        
        # Section 2: Analytical method
        lines.append("*" * 82)
        lines.append("* Section 2: Analytical method ")
        lines.append("* ")
        tampli_val = f"{self.tampli:.1f}" if self.tampli is not None else "10.0"
        tmean_val = f"{self.tmean:.1f}" if self.tmean is not None else "15.0"
        ddamp_val = f"{self.ddamp:.1f}" if self.ddamp is not None else "50.0"
        timref_val = f"{self.timref:.1f}" if self.timref is not None else "90.0"
        
        lines.append(f"  TAMPLI = {tampli_val} ! Amplitude of annual temperature wave at soil surface, [0..50 C, R] ")
        lines.append(f"  TMEAN  = {tmean_val} ! Mean annual temperature at soil surface, [5..30 C, R] ")
        lines.append(f"  DDAMP  = {ddamp_val} ! Damping depth of temperature wave in soil, [0..500 cm, R] ")
        lines.append(f"  TIMREF = {timref_val} ! Day number (Jan 1 = 1) at top sine temperature wave [1..366 d, I] ")
        lines.append("*" * 82)
        lines.append(" ")
        
        # Section 3: Numerical method
        lines.append("*" * 82)
        lines.append("* Section 3: Numerical method ")
        lines.append("* ")
        lines.append("* List initial temperature of each compartment (max 40), [-10..40 C, R]   ")
        lines.append("  TEMPI =  ")
        
        # 生成TEMPI数组，每行10个值
        if self.tempi is not None:
            tempi_values = self.tempi
        else:
            # 默认值：300个12（30行，每行10个）
            tempi_values = [12.0] * 300
        
        # 按每行10个值的格式输出
        for i in range(0, len(tempi_values), 10):
            row_values = tempi_values[i:i+10]
            formatted_values = [f"{val:2.0f}" for val in row_values]
            line = " " + "      ".join(formatted_values) + "      "
            lines.append(line)
        
        lines.append("*" * 82 + " ")
        lines.append(" ")
        
        # Section 4: Effect of soil surface material on heat conduction
        lines.append("*" * 82)
        lines.append("* Section 4: Effect of soil surface material on heat conduction1")
        lines.append("* ")
        swessm_val = self.swessm if self.swessm is not None else 0
        lines.append(f"  SWESSM = {swessm_val} ! Switch, consider the effect of soil surface material [Y=1, N=0]")
        lines.append("* ")
        lines.append("* If SWESSM = 1, then specify heat conductivity of soil surface material")
        hconsm_val = f"{self.hconsm:.1f}" if self.hconsm is not None else "1.0"
        lines.append(f"  HCONSM = {hconsm_val} ! Heat conductivity of material on soil surface, [0..20000 J/cm/K/d, R]")
        lines.append("* ")
        lines.append("* End of file*" + "*" * 69)
        
        return "\n".join(lines)
    
    def write_hea(self, output_dir: str, project_name: str = "hetao") -> None:
        """将HEA文件写入指定目录。

        Args:
            output_dir: 输出目录路径
            project_name: 项目名称，用于构建文件名
        """
        from pathlib import Path

        # 构建文件路径
        hea_file_path = Path(output_dir) / f"{project_name}.HEA"

        # 写入文件
        with open(hea_file_path, 'w', encoding='utf-8') as f:
            f.write(self.model_string(project_name))

        print(f"✓ 已生成 {project_name}.HEA 文件: {hea_file_path}")


class SoluteTransport(_ConfigurableComponent, _YAMLValidatorMixin, _FileMixin):
    """重构后的溶质传输组件 - 支持溶质传输参数验证和外部配置

    新增功能：
    - 溶质传输参数验证（浓度范围、扩散系数、分散度等）
    - 条件参数验证（根据溶质传输开关验证相应的必需参数）
    - 参数外部化配置支持
    - 物理约束验证（确保溶质传输参数符合物理约束条件）

    属性：
        numsat (Optional[int]): 盐分种类数量 [1..8]
        swcalm (Optional[Literal[0, 1, 2]]): 数值方法选择
        chrsun (Optional[Literal[1, 2, 3]]): 输出文件单位选择
        swocn (Optional[Literal[0, 1]]): 有机C&N周转开关
        swsolu (Literal[0, 1]): 溶质传输模拟开关
        cpre (Optional[ConcentrationFloat]): 降水中溶质浓度 [0..100 mg/cm³]
        cdrain (Optional[ConcentrationFloat]): 地表水中溶质浓度 [0..100 mg/cm³]
        swbotbc (Optional[Literal[0, 1, 2]]): 向上流（渗流）情况下的地下水浓度开关
        cseep (Optional[ConcentrationFloat]): 地表水中溶质浓度 [0..100 mg/cm³]
        ddif (Optional[PositiveFloat]): 分子扩散系数 [0..10 cm²/d]
        ldis (Optional[List[PositiveFloat]]): 分散长度列表 [0..100 cm]
        tscf (Optional[PositiveFloat]): 根系对溶质的相对吸收 [0..10]
        swsp (Optional[Literal[0, 1]]): 开关，考虑溶质吸附
        kf (Optional[List[PositiveFloat]]): Ks或KF系数列表 [0..100 cm³/mg]
        frexp (Optional[PositiveFloat]): Freundlich 指数 [0..10]
        cref (Optional[ConcentrationFloat]): 吸附参考溶质浓度 [0..1000 mg/cm³]
        eta (Optional[List[PositiveFloat]]): Langmuir方程系数列表 [0..1000 mg/cm³]

        # 新增外部化参数
        max_concentration (Optional[ConcentrationFloat]): 最大允许浓度 [mg/cm³]
        min_dispersivity (Optional[PositiveFloat]): 最小弥散度 [cm]
        default_diffusion_coeff (Optional[PositiveFloat]): 默认扩散系数 [cm²/d]

        # 保持原有复杂参数
        swdc (Optional[Literal[0, 1, 2]]): 开关，考虑溶质分解
        decpot (Optional[list]): 液相潜在分解速率列表 [0..10 /d]
        decpots (Optional[list]): 固相潜在分解速率列表 [0..10 /d]
        decpot_c (Optional[list]): N链液相一阶潜在分解速率列表 [0..10 /d]
        decpots_c (Optional[list]): N链固相一阶潜在分解速率列表 [0..10 /d]
        gampar (Optional[Decimal2f]): 温度引起的分解减少因子 [0..0.5 /C]
        rtheta (Optional[Decimal2f]): 潜在分解的最小含水量 [0..0.4 cm³/cm³]
        bexp (Optional[Decimal2f]): 干燥引起的分解减少指数 [0..2]
        fdepth (Optional[list]): 各土壤层潜在分解减少列表 [0..1]
        chm_inf (Optional[list]): EPIC土壤化学信息列表
        swpref (Optional[Literal[0, 1]]): 移动-不移动水体积开关
        kmobil (Optional[list]): 移动-不移动部分溶质传输系数列表 [0..100 /d]
        swbr (Optional[Literal[0, 1]]): 开关，考虑饱和区混合储层
        daquif (Optional[Decimal2f]): 含水层饱和部分厚度 [0..10000 cm]
        poros (Optional[Decimal2f]): 含水层孔隙度 [0..0.6]
        kfsat (Optional[Decimal2f]): 含水层线性吸附系数 [0..100 cm³/mg]
        decsat (Optional[Decimal2f]): 含水层分解速率 [0..10 /d]
        cdraini (Optional[ConcentrationFloat]): 地下水中初始溶质浓度 [0..100 mg/cm³]
        cmli (Optional[List[ConcentrationFloat]]): 初始溶质浓度列表 [1..1000 mg/cm³]
        cseeparrtb (Optional[Table]): 地下水浓度随时间变化的表格
        inissoil (Optional[Table]): 初始溶质浓度随土壤深度变化的表格
        miscellaneous (Optional[Table]): 杂项参数随土壤深度变化的表格
    """

    numsat: int | None = _Field(None, ge=1, le=8, description="盐分种类数量")
    swcalm: _Literal[0, 1, 2] | None = None
    chrsun: _Literal[1, 2, 3] | None = None
    swocn: _Literal[0, 1] | None = None
    swsolu: _Literal[0, 1] | None = None
    cpre: _ConcentrationFloat | None = _Field(None, ge=0, le=100, description="降水中溶质浓度")
    cdrain: _ConcentrationFloat | None = _Field(None, ge=0, le=100, description="地表水中溶质浓度")
    swbotbc: _Literal[0, 1, 2] | None = None
    cseep: _ConcentrationFloat | None = _Field(None, ge=0, le=100, description="地表水中溶质浓度")
    ddif: _PositiveFloat | None = _Field(None, ge=0, le=10, description="分子扩散系数")
    ldis: _List[_PositiveFloat] | None = _Field(None, description="分散长度列表")
    tscf: float | None = _Field(None, ge=0, le=10, description="根系对溶质的相对吸收")
    swsp: _Literal[0, 1] | None = None
    kf: _List[_PositiveFloat] | None = _Field(None, description="Ks或KF系数列表")
    frexp: _PositiveFloat | None = _Field(None, ge=0, le=10, description="Freundlich 指数")
    cref: _ConcentrationFloat | None = _Field(None, ge=0, le=1000, description="吸附参考溶质浓度")
    eta: _List[_PositiveFloat] | None = _Field(None, description="Langmuir方程系数列表")

    # 新增外部化参数
    max_concentration: _Optional[_ConcentrationFloat] = _Field(default=None, description="最大允许浓度 [mg/cm³]")
    min_dispersivity: _Optional[_PositiveFloat] = _Field(default=None, description="最小弥散度 [cm]")
    default_diffusion_coeff: _Optional[_PositiveFloat] = _Field(default=None, description="默认扩散系数 [cm²/d]")
    swdc: _Literal[0, 1, 2] | None = None
    decpot: list | None = None
    decpots: list | None = None
    decpot_c: list | None = None
    decpots_c: list | None = None
    gampar: _Decimal2f | None = _Field(None, ge=0, le=0.5)
    rtheta: _Decimal2f | None = _Field(None, ge=0, le=0.4)
    bexp: _Decimal2f | None = _Field(None, ge=0, le=2)
    fdepth: list | None = None
    chm_inf: list | None = None
    swpref: _Literal[0, 1] | None = None
    kmobil: list | None = None
    swbr: _Literal[0, 1] | None = None
    daquif: _Decimal2f | None = _Field(None, ge=0, le=10000)
    poros: _Decimal2f | None = _Field(None, ge=0, le=0.6)
    kfsat: _Decimal2f | None = _Field(None, ge=0, le=100)
    decsat: _Decimal2f | None = _Field(None, ge=0, le=10)
    cdraini: _ConcentrationFloat | None = _Field(None, ge=0, le=100, description="地下水中初始溶质浓度")
    cmli: _List[_ConcentrationFloat] | None = _Field(None, description="初始溶质浓度列表")
    cseeparrtb: _Table | None = None
    inissoil: _Table | None = None
    misc: _Table | None = None

    @_model_validator(mode="after")
    def validate_solute_parameters(self):
        """验证溶质传输参数的条件依赖和物理约束"""
        if not self._validation:
            return self

        # 当启用溶质传输时验证必需参数
        if self.swsolu == 1:
            self._validate_required_solute_parameters()

        # 验证浓度参数的物理合理性
        self._validate_concentration_constraints()

        # 验证传输参数的物理合理性
        self._validate_transport_constraints()

        return self

    def _validate_required_solute_parameters(self):
        """验证溶质传输必需参数"""
        if self.numsat is None:
            raise ValueError("swsolu=1 requires numsat (number of salt species)")

        if self.swcalm is None:
            raise ValueError("swsolu=1 requires swcalm (calculation method)")

        if self.ddif is None:
            raise ValueError("swsolu=1 requires ddif (diffusion coefficient)")

    def _validate_concentration_constraints(self):
        """验证浓度参数的物理约束"""
        # 验证各种浓度参数的一致性
        concentrations = [self.cpre, self.cdrain, self.cseep, self.cref, self.cdraini]
        valid_concentrations = [c for c in concentrations if c is not None]

        if valid_concentrations:
            max_conc = max(valid_concentrations)
            if self.max_concentration and max_conc > self.max_concentration:
                raise ValueError(f"Concentration {max_conc} exceeds maximum allowed {self.max_concentration}")

        # 验证初始浓度列表的合理性
        if self.cmli is not None:
            if any(c <= 0 for c in self.cmli):
                raise ValueError("All initial concentrations must be positive")

            conc_range = max(self.cmli) - min(self.cmli)
            if conc_range > 1000:
                raise ValueError(f"Initial concentration range {conc_range} mg/cm³ seems unreasonably large")

    def _validate_transport_constraints(self):
        """验证传输参数的物理约束"""
        # 验证扩散系数的合理范围
        if self.ddif is not None:
            if self.ddif > 10:
                raise ValueError(f"Diffusion coefficient {self.ddif} cm²/d seems unreasonably large")

        # 验证分散长度的合理性
        if self.ldis is not None:
            if any(l <= 0 for l in self.ldis):
                raise ValueError("All dispersivity values must be positive")

            if self.min_dispersivity and any(l < self.min_dispersivity for l in self.ldis):
                raise ValueError(f"Some dispersivity values are below minimum {self.min_dispersivity} cm")

            if any(l > 1000 for l in self.ldis):
                raise ValueError("Some dispersivity values seem unreasonably large (>1000 cm)")

        # 验证吸附参数的合理性
        if self.kf is not None and any(k < 0 for k in self.kf):
            raise ValueError("All adsorption coefficients must be non-negative")

    def model_string(self, project_name: str = "hetao") -> str:
        """生成严格符合V201格式的SLT文件内容。

        Args:
            project_name: 项目名称，用于文件头部
        """
        lines = []

        # 文件头部
        lines.append("*" * 103)
        lines.append(f"* Filename: {project_name}.SLT")
        lines.append("* Contents: AHC 1.0 - Solute data")
        lines.append("*" * 103)
        lines.append(f"*c Case: Water and solute transport in the {project_name} area,")
        lines.append("*" * 103)
        lines.append("")
        
        # Section 1: Number of salt species and calculation method
        lines.append("*" * 103)
        lines.append("* Section 1: Number of salt species and calculation method")

        # 使用属性值或默认值
        numsat = self.numsat if self.numsat is not None else 1
        swcalm = self.swcalm if self.swcalm is not None else 1
        chrsun = self.chrsun if self.chrsun is not None else 1
        swocn = self.swocn if self.swocn is not None else 0

        lines.append(f" Numsat = {numsat} ! Number of salt species [1...8] ")
        lines.append(f" SWCALM = {swcalm} ! Choose numerical method [=0,1,2]")
        lines.append("*                0 = Fully explicit finite-difference, only can be used when Numsat =1")
        lines.append("*                1 = Fully implicit finite-difference, Numsat =1~8")
        lines.append("*                2 = Crank-Nichoson scheme, Numsat =1~8")
        lines.append(f" CHRSUN = {chrsun} ! Choose the unit for output file in *.SPI")
        lines.append("*                1 = g/L, often for solute concentration")
        lines.append("*                2 = g/kg, often for salt contant in mass")
        lines.append("*                3 = mg/kg, often for nitrogen content in mass")
        lines.append(f" SWOCN  = {swocn} ! Switch, consider organic C&N turnover [Y=1, N=0]")
        lines.append("*                only needed when doing N simulation (i.e. SWSTYP=2)")
        lines.append("*" * 103)
        lines.append("  ")
        
        # Section 2: Top boundary condition
        lines.append("*" * 103)
        lines.append("* Section 2: Top boundary condition")
        lines.append("* List Solute conc. of different species (max 8) in precipitation [1..100 mg/cm3, R]")
        lines.append("  CPRE = ")
        cpre_val = f"{self.cpre:.1f}" if self.cpre is not None else "0.0"
        lines.append(f"  {cpre_val}      ")
        lines.append("*" * 103)
        lines.append("")
        
        # Section 3: Diffusion, dispersion, and solute uptake by roots
        lines.append("*" * 103)
        lines.append("* Section 3: Diffusion, dispersion, and solute uptake by roots")
        lines.append("* List molecular diffusion coefficient of different layers [0..10 cm2/day, R]")
        lines.append("  DDIF =")
        ddif_val = f"{self.ddif:.1f}" if self.ddif is not None else "3.5"
        lines.append(f"  {ddif_val}      ")
        lines.append("* List Dispersion length of different species (max 8) [0..100 cm, R]")
        lines.append("  LDIS = ")

        # 使用ldis属性或默认值
        if self.ldis is not None:
            for value in self.ldis:
                lines.append(f"  {value:.2f}    ")
        else:
            # 默认值：4个20.00
            default_ldis = [20.0, 20.0, 20.0, 20.0]
            for value in default_ldis:
                lines.append(f"  {value:.2f}    ")
        lines.append("* List Relative uptake of solutes by roots for different species (max 8) [0..10, R]")
        lines.append("  TSCF =")
        tscf_val = f"{self.tscf:.1f}" if self.tscf is not None else "0.0"
        lines.append(f"  {tscf_val}      ")
        lines.append("*" * 103)
        lines.append("")
        
        # Section 4: Adsorption
        lines.append("*" * 103)
        lines.append("* Section 4: Adsorption ")
        lines.append("*")
        swsp_val = self.swsp if self.swsp is not None else 0
        lines.append(f"  SWSP = {swsp_val} ! Switch, consider solute adsorption [Y=1, N=0]  ")
        lines.append("*")
        lines.append("* If SWSP = 1 specify all belows:")
        lines.append("* List Ks or KF coefficient for different species (max 8) [0..100 cm3/mg, R]")
        lines.append("  KF=  ")

        # 使用kf属性或默认值
        if self.kf is not None:
            for value in self.kf:
                lines.append(f"  {value:.2f}    ")
        else:
            # 默认值：4个0.05
            default_kf = [0.05, 0.05, 0.05, 0.05]
            for value in default_kf:
                lines.append(f"  {value:.2f}    ")
        lines.append("* List BETAXU exponent for different species (max 8 column) [0..10 -, R]")
        lines.append("* if FREXP=1, means linear adsorption")
        lines.append("  FREXP = ")
        frexp_val = f"{self.frexp:.1f}" if self.frexp is not None else "1.0"
        lines.append(f"  {frexp_val}      ")
        # 其他种类使用默认值1.0
        for _ in range(3):
            lines.append("  1.0      ")
        lines.append("* List Reference solute concentration for different species (max 8 col.) [0..1000 mg/cm3, R]")
        lines.append("* [aa]")
        lines.append("  CREF  =")
        cref_val = f"{self.cref:.1f}" if self.cref is not None else "1.0"
        lines.append(f"  {cref_val}      ")
        lines.append("* List Langmuir equation coefficient for different species (max 8 col.) [0..1000 mg/cm3, R]")
        lines.append("*  [when using Langmuir, set value to 0]")
        lines.append("  ETA = ")

        # 使用eta属性或默认值
        if self.eta is not None:
            for value in self.eta:
                lines.append(f"  {value:.1f}      ")
        else:
            # 默认值：4个0.1
            default_eta = [0.1, 0.1, 0.1, 0.1]
            for value in default_eta:
                lines.append(f"  {value:.1f}      ")
        lines.append("*" * 103)
        lines.append("")
        
        # Section 5: Decomposition
        lines.append("*" * 103)
        lines.append("* Section 5: Decomposition  ")
        lines.append("*")
        swdc_val = self.swdc if self.swdc is not None else 0
        lines.append(f"  SWDC = {swdc_val} ! Choose, solute decomposition method [0,1,2]")
        lines.append("*                0 = no solute decomposition")
        lines.append("*                1 = reaction based on potential rate")
        lines.append("*                2 = EPIC model for N")
        lines.append("*")
        lines.append("* If SWDC = 1 specify all belows:")
        lines.append("* List Potential decomposition rate in liquid for different species (max 8 col.) [0..10 /d, R]")
        lines.append("  DECPOT =  ")

        # 使用decpot属性或默认值
        if self.decpot is not None:
            for value in self.decpot:
                lines.append(f"  {value:.2f}     ")
        else:
            # 默认值：4个0.00
            default_decpot = [0.0, 0.0, 0.0, 0.0]
            for value in default_decpot:
                lines.append(f"  {value:.2f}     ")

        lines.append("* List Potential decomposition rate in solid for different species (max 8 col.) [0..10 /d, R]")
        lines.append("  DECPOTS =  ")

        # 使用decpots属性或默认值
        if self.decpots is not None:
            for value in self.decpots:
                lines.append(f"  {value:.2f}     ")
        else:
            # 默认值：4个0.00
            default_decpots = [0.0, 0.0, 0.0, 0.0]
            for value in default_decpots:
                lines.append(f"  {value:.2f}     ")
        lines.append("* List 1st order Potential decomposition rate in liquid for N chain (max 8 col.) [0..10 /d, R]    N modification")
        lines.append("  DECPOT_C = ")

        # 使用decpot_c属性或默认值
        if self.decpot_c is not None:
            for value in self.decpot_c:
                lines.append(f"  {value:.2f}     ")
        else:
            # 默认值：5个0.00
            default_decpot_c = [0.0, 0.0, 0.0, 0.0, 0.0]
            for value in default_decpot_c:
                lines.append(f"  {value:.2f}     ")

        lines.append("* List 1st order Potential decomposition rate in solid for N chain (max 8 col.) [0..10 /d, R]    N modification")
        lines.append("  DECPOTS_C =  ")

        # 使用decpots_c属性或默认值
        if self.decpots_c is not None:
            for value in self.decpots_c:
                lines.append(f"  {value:.2f}     ")
        else:
            # 默认值：5个0.00
            default_decpots_c = [0.0, 0.0, 0.0, 0.0, 0.0]
            for value in default_decpots_c:
                lines.append(f"  {value:.2f}     ")
        lines.append("* List Factor reduction decomposition due to temperature. for diff. species(max 8)[0..0.5/C,R]")
        lines.append("  GAMPAR =   ")
        gampar_val = f"{self.gampar:.1f}" if self.gampar is not None else "0.2"
        lines.append(f"  {gampar_val}      ")
        # 其他种类使用默认值0.2
        for _ in range(4):
            lines.append("  0.2      ")

        lines.append("* List Minimum water content for pot. decompos. rate for diff.species(max 8) [0..0.4cm3/cm3,R]")
        lines.append("  RTHETA = ")
        rtheta_val = f"{self.rtheta:.1f}" if self.rtheta is not None else "0.3"
        lines.append(f"  {rtheta_val}      ")
        # 其他种类使用默认值0.3
        for _ in range(4):
            lines.append("  0.3      ")

        lines.append("* List Exponent in reduction decompos. due to dryness for diff. species(max 8) [0..2 -, R]")
        lines.append("  BEXP   =")
        bexp_val = f"{self.bexp:.1f}" if self.bexp is not None else "1.2"
        lines.append(f"  {bexp_val}      ")
        lines.append("* List reduction of pot. decompos. for each soil layer for diff. species(max 8), [0..1 -, R]")
        lines.append("* salt1  salt2   salt3  salt4  salt5")
        lines.append("  FDEPTH =    ")

        # 使用fdepth属性或默认值
        if self.fdepth is not None:
            for value in self.fdepth:
                lines.append(f"  {value:.2f}     ")
        else:
            # 默认值：5个0.10
            default_fdepth = [0.10, 0.10, 0.10, 0.10, 0.10]
            for value in default_fdepth:
                lines.append(f"  {value:.2f}     ")

        lines.append("* End of table")
        lines.append("* If SWDC = 2 specify all belows: soil chemical information for EPIC")
        lines.append("* DepC/cm  CECLAY  SOILPHLAY")
        lines.append("  CHM_INF =")

        # 使用chm_inf属性或默认值
        if self.chm_inf is not None:
            for row in self.chm_inf:
                if isinstance(row, (list, tuple)) and len(row) >= 3:
                    lines.append(f"  {row[0]:.1f}     {row[1]:.1f}      {row[2]:.1f}\t     ")
                else:
                    lines.append(f"  {row:.1f}     5.0      7.8\t     ")
        else:
            # 默认值：5行数据
            default_chm_inf = [
                (30.0, 5.0, 7.8),
                (45.0, 5.0, 7.8),
                (70.0, 5.0, 7.8),
                (100.0, 5.0, 7.8),
                (300.0, 5.0, 7.8)
            ]
            for row in default_chm_inf:
                lines.append(f"  {row[0]:.1f}     {row[1]:.1f}      {row[2]:.1f}\t     ")

        lines.append("* End of table    N modification")
        lines.append("*" * 103)
        lines.append("")
        
        # Section 6: Transfer between mobile and immobile water volumes
        lines.append("*" * 103)
        lines.append("* Section 6: Transfer between mobile and immobile water volumes (if present)")
        lines.append("*")

        swpref = self.swpref if self.swpref is not None else 0
        lines.append(f"  SWPREF = {swpref}! Switch, consider mobile-immobile water volumes [Y=1, N=0]")
        lines.append("*")
        lines.append("* If SWPREF = 1 specify K_MOBIL:")
        lines.append("* List Solute transfer coef. between mobile-immobile parts for diff.species(max 8)[0..100/d,R]")
        lines.append("  KMOBIL =   ")

        # 使用kmobil属性或默认值
        if self.kmobil is not None:
            for value in self.kmobil:
                lines.append(f"  {value:.1f}      ")
        else:
            # 默认值：1个0.1
            lines.append("  0.1      ")
        lines.append("*" * 103)
        lines.append("")
        
        # Section 7: Solute residence in the saturated zone
        lines.append("*" * 103)
        lines.append("* Section 7: Solute residence in the saturated zone")
        lines.append("*")
        swbr_val = self.swbr if self.swbr is not None else 0
        lines.append(f"  SWBR = {swbr_val}! Switch, consider mixed reservoir of saturated zone [Y=1, N=0] ")
        lines.append("*")
        lines.append("* If SWBR = 0 specify C_DRAIN:")
        lines.append("* List Solute concentration in aquifer for diff.species(max 8 col.) [0..100 mg/cm3, R]")
        lines.append("  CDRAIN  = ")
        cdrain_val = f"{self.cdrain:.1f}" if self.cdrain is not None else "2.0"
        lines.append(f"  {cdrain_val}      ")
        lines.append("*")
        lines.append("*")
        lines.append("* If SWBR = 1 specify all belows:")
        daquif_val = f"{self.daquif:.0f}" if self.daquif is not None else "290"
        lines.append(f"  HAQUIF  = {daquif_val} ! Thickness saturated part of aquifer [0..10000 cm, R]")
        poros_val = f"{self.poros:.1f}" if self.poros is not None else "0.3"
        lines.append(f"  POROS   = {poros_val} ! Porosity of aquifer [0..0.6 cm3/cm3, R]")
        lines.append("* List Linear adsorption coefficient in aquifer for diff.species(max 8) [0..100 cm3/mg, R]")
        lines.append("  KFSAT   =  ")
        kfsat_val = f"{self.kfsat:.1f}" if self.kfsat is not None else "10.3"
        lines.append(f"  {kfsat_val}     ")
        lines.append("* List Decomposition rate in aquifer for diff.species(max 8) [0..10 /d, R]")
        lines.append("  DECSAT  = ")
        decsat_val = f"{self.decsat:.1f}" if self.decsat is not None else "1.2"
        lines.append(f"  {decsat_val}      ")
        lines.append("* List Initial solute concentration in groundwater for diff.species(max 8) [0..100 mg/cm3, R]")
        lines.append("  CDRAINI = ")
        cdraini_val = f"{self.cdraini:.1f}" if self.cdraini is not None else "1.7"
        lines.append(f"  {cdraini_val}      ")
        lines.append("*")
        lines.append("*" * 103)
        lines.append("")
        
        # Section 8: Initial condition
        lines.append("*" * 103)
        lines.append("* Section 8:  Initial condition")
        lines.append("* List initial solute concentration of each comp. (max 8 col.), [1..1000 mg/cm3, R]:")
        lines.append("  CMLI =")

        # 使用cmli属性或生成默认的初始溶质浓度数据
        if self.cmli is not None:
            for value in self.cmli:
                lines.append(f"  {value:.1f}     ")
        else:
            # 生成默认的初始溶质浓度数据（300个值）
            initial_values = self._generate_initial_concentration_distribution()
            for value in initial_values:
                lines.append(f"  {value:.1f}     ")
        
        lines.append("*End of table")
        lines.append("* End of file *********************************************************************************")

        return "\n".join(lines)

    def _generate_initial_concentration_distribution(self) -> list:
        """生成初始溶质浓度分布。"""
        # 根据标准hetao_corn_2013案例的精确浓度分布（总计300个值）
        initial_values = (
            [14.5] * 4 +    # 14.5: 4个
            [12.4] * 6 +    # 12.4: 6个
            [5.5] * 10 +    # 5.5: 10个
            [3.2] * 40 +    # 3.2: 40个
            [3.0] * 20 +    # 3.0: 20个
            [2.6] * 220     # 2.6: 220个
        )

        return list(initial_values)
    
    def write_slt(self, output_dir: str, project_name: str = "hetao") -> None:
        """将SLT文件写入指定目录。

        Args:
            output_dir: 输出目录路径
            project_name: 项目名称，用于构建文件名
        """
        from pathlib import Path

        # 构建文件路径
        slt_file_path = Path(output_dir) / f"{project_name}.SLT"

        # 写入文件
        with open(slt_file_path, 'w', encoding='utf-8') as f:
            f.write(self.model_string(project_name))

        print(f"✓ 已生成 {project_name}.SLT 文件: {slt_file_path}")

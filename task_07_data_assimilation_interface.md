# 任务07：数据同化接口设计与实现

## 任务概述
设计和实现数据同化的基础接口架构，为后续集成观测数据同化功能提供完整的基础设施。包括观测数据处理、同化变量存储、不确定性量化等核心功能。

## 任务需求

### 核心目标
1. 设计完整的数据同化接口架构
2. 实现观测数据处理和验证模块
3. 实现同化变量的HDF5存储结构
4. 提供数据同化算法的插件接口
5. 实现不确定性量化和传播机制

### 具体要求
- 支持多种观测数据格式和来源
- 实现观测数据的质量控制和验证
- 提供灵活的同化算法接口
- 支持集合模拟的数据结构（预留）
- 实现同化结果的评估和诊断

## 实现步骤

### 步骤1：设计数据同化核心架构

```python
"""数据同化接口模块"""
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union, Tuple
from datetime import date, datetime
import logging
from pathlib import Path

from .manager import ProjectHDF5Manager
from .exceptions import HDF5Error

class AssimilationManager:
    """数据同化管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.observation_loader = ObservationDataLoader(config)
        self.quality_controller = ObservationQualityController(config)
        self.uncertainty_manager = UncertaintyManager(config)
        
        # 同化配置
        self.assimilation_enabled = config.get('enable_assimilation', False)
        self.assimilation_frequency = config.get('assimilation_frequency', 1)  # 每N天同化一次
        self.observation_window = config.get('observation_window', 1)  # 观测时间窗口（天）
        
    def process_daily_assimilation(self, manager: ProjectHDF5Manager,
                                 project_name: str,
                                 current_date: date) -> bool:
        """处理日数据同化
        
        Args:
            manager: HDF5管理器
            project_name: 项目名称
            current_date: 当前日期
            
        Returns:
            是否执行了数据同化
        """
        try:
            if not self.assimilation_enabled:
                return False
            
            # 检查是否需要执行同化
            if not self._should_assimilate(current_date):
                return False
            
            # 加载观测数据
            observations = self.observation_loader.load_observations_for_date(current_date)
            if not observations:
                self.logger.debug(f"No observations available for {current_date}")
                return False
            
            # 质量控制
            validated_observations = self.quality_controller.validate_observations(observations)
            if not validated_observations:
                self.logger.warning(f"No valid observations after quality control for {current_date}")
                return False
            
            # 执行数据同化
            assimilation_result = self._perform_assimilation(
                manager, project_name, current_date, validated_observations
            )
            
            # 保存同化结果
            self._save_assimilation_result(
                manager, project_name, current_date, assimilation_result
            )
            
            self.logger.info(f"Data assimilation completed for {current_date}")
            return True
            
        except Exception as e:
            self.logger.error(f"Data assimilation failed for {current_date}: {e}")
            return False
    
    def _should_assimilate(self, current_date: date) -> bool:
        """判断是否应该执行同化"""
        # 简单的频率控制
        day_of_year = current_date.timetuple().tm_yday
        return day_of_year % self.assimilation_frequency == 0
    
    def _perform_assimilation(self, manager: ProjectHDF5Manager,
                            project_name: str,
                            current_date: date,
                            observations: Dict[str, Any]) -> Dict[str, Any]:
        """执行数据同化"""
        try:
            # 加载当前模型状态
            with manager:
                daily_data = manager.load_daily_data(project_name, current_date)
                current_states = daily_data.get('states', {})
            
            # 应用同化算法（这里使用简单的直接替换作为示例）
            assimilated_states = self._apply_simple_assimilation(current_states, observations)
            
            # 计算同化统计信息
            assimilation_stats = self._calculate_assimilation_statistics(
                current_states, observations, assimilated_states
            )
            
            return {
                'assimilated_states': assimilated_states,
                'original_states': current_states,
                'observations': observations,
                'statistics': assimilation_stats,
                'algorithm': 'simple_replacement',
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Assimilation algorithm failed: {e}")
            raise
    
    def _apply_simple_assimilation(self, states: Dict[str, Any], 
                                 observations: Dict[str, Any]) -> Dict[str, Any]:
        """应用简单的数据同化算法"""
        assimilated_states = states.copy()
        
        # 简单的直接替换策略
        for obs_var, obs_value in observations.items():
            if obs_var in states:
                # 计算加权平均（简化的同化）
                state_value = states[obs_var]
                obs_weight = observations.get(f'{obs_var}_weight', 0.5)
                
                if isinstance(state_value, np.ndarray) and isinstance(obs_value, np.ndarray):
                    # 数组类型（如土壤含水量剖面）
                    if state_value.shape == obs_value.shape:
                        assimilated_states[obs_var] = (
                            (1 - obs_weight) * state_value + obs_weight * obs_value
                        )
                elif isinstance(state_value, (int, float)) and isinstance(obs_value, (int, float)):
                    # 标量类型（如LAI、生物量）
                    assimilated_states[obs_var] = (
                        (1 - obs_weight) * state_value + obs_weight * obs_value
                    )
        
        return assimilated_states
    
    def _calculate_assimilation_statistics(self, prior_states: Dict[str, Any],
                                         observations: Dict[str, Any],
                                         posterior_states: Dict[str, Any]) -> Dict[str, Any]:
        """计算同化统计信息"""
        stats = {}
        
        for var_name in observations.keys():
            if var_name.endswith('_weight') or var_name.endswith('_error'):
                continue
                
            if var_name in prior_states and var_name in posterior_states:
                prior = prior_states[var_name]
                posterior = posterior_states[var_name]
                obs = observations[var_name]
                
                if isinstance(prior, (int, float)):
                    stats[f'{var_name}_prior_obs_diff'] = abs(prior - obs)
                    stats[f'{var_name}_posterior_obs_diff'] = abs(posterior - obs)
                    stats[f'{var_name}_innovation'] = posterior - prior
                elif isinstance(prior, np.ndarray):
                    stats[f'{var_name}_prior_obs_rmse'] = np.sqrt(np.mean((prior - obs) ** 2))
                    stats[f'{var_name}_posterior_obs_rmse'] = np.sqrt(np.mean((posterior - obs) ** 2))
                    stats[f'{var_name}_innovation_mean'] = np.mean(posterior - prior)
        
        return stats
    
    def _save_assimilation_result(self, manager: ProjectHDF5Manager,
                                project_name: str,
                                current_date: date,
                                assimilation_result: Dict[str, Any]) -> None:
        """保存同化结果"""
        try:
            with manager:
                date_str = f"day_{current_date.strftime('%m-%d')}"
                project_group = manager._file[project_name]
                
                if date_str not in project_group:
                    self.logger.error(f"Date group {date_str} not found")
                    return
                
                day_group = project_group[date_str]
                
                # 创建同化组
                if 'assimilation' not in day_group:
                    assim_group = day_group.create_group('assimilation')
                else:
                    assim_group = day_group['assimilation']
                
                # 保存同化后的状态变量
                self._save_assimilated_states(assim_group, assimilation_result['assimilated_states'])
                
                # 保存观测数据
                self._save_observations(assim_group, assimilation_result['observations'])
                
                # 保存统计信息
                self._save_assimilation_statistics(assim_group, assimilation_result['statistics'])
                
                # 保存元数据
                assim_group.attrs['algorithm'] = assimilation_result['algorithm']
                assim_group.attrs['timestamp'] = assimilation_result['timestamp']
                assim_group.attrs['assimilation_date'] = current_date.isoformat()
                
        except Exception as e:
            self.logger.error(f"Failed to save assimilation result: {e}")
            raise
```

### 步骤2：实现观测数据处理模块

```python
class ObservationDataLoader:
    """观测数据加载器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.observation_dir = Path(config.get('observation_dir', './observations'))
        self.supported_formats = ['csv', 'json', 'netcdf', 'txt']
        
    def load_observations_for_date(self, target_date: date) -> Optional[Dict[str, Any]]:
        """加载指定日期的观测数据"""
        try:
            # 查找观测文件
            observation_files = self._find_observation_files(target_date)
            
            if not observation_files:
                return None
            
            # 加载和合并观测数据
            observations = {}
            for file_path in observation_files:
                file_obs = self._load_single_observation_file(file_path, target_date)
                if file_obs:
                    observations.update(file_obs)
            
            return observations if observations else None
            
        except Exception as e:
            self.logger.error(f"Failed to load observations for {target_date}: {e}")
            return None
    
    def _find_observation_files(self, target_date: date) -> List[Path]:
        """查找观测文件"""
        observation_files = []
        
        if not self.observation_dir.exists():
            return observation_files
        
        # 查找匹配日期的文件
        date_patterns = [
            target_date.strftime('%Y%m%d'),
            target_date.strftime('%Y-%m-%d'),
            target_date.strftime('%m-%d'),
            target_date.strftime('%j')  # 年积日
        ]
        
        for pattern in date_patterns:
            for ext in self.supported_formats:
                matching_files = list(self.observation_dir.glob(f"*{pattern}*.{ext}"))
                observation_files.extend(matching_files)
        
        return list(set(observation_files))  # 去重
    
    def _load_single_observation_file(self, file_path: Path, target_date: date) -> Optional[Dict[str, Any]]:
        """加载单个观测文件"""
        try:
            file_ext = file_path.suffix.lower()
            
            if file_ext == '.csv':
                return self._load_csv_observations(file_path, target_date)
            elif file_ext == '.json':
                return self._load_json_observations(file_path, target_date)
            elif file_ext == '.txt':
                return self._load_txt_observations(file_path, target_date)
            else:
                self.logger.warning(f"Unsupported observation file format: {file_ext}")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to load observation file {file_path}: {e}")
            return None
    
    def _load_csv_observations(self, file_path: Path, target_date: date) -> Optional[Dict[str, Any]]:
        """加载CSV格式观测数据"""
        try:
            df = pd.read_csv(file_path)
            
            # 查找日期列
            date_columns = [col for col in df.columns if 'date' in col.lower()]
            if not date_columns:
                # 假设第一列是日期
                date_columns = [df.columns[0]]
            
            date_col = date_columns[0]
            
            # 转换日期格式
            df[date_col] = pd.to_datetime(df[date_col]).dt.date
            
            # 筛选目标日期的数据
            target_data = df[df[date_col] == target_date]
            
            if target_data.empty:
                return None
            
            # 提取观测变量
            observations = {}
            for col in df.columns:
                if col != date_col and not target_data[col].isna().all():
                    value = target_data[col].iloc[0]
                    if pd.notna(value):
                        observations[col.lower()] = float(value)
            
            return observations
            
        except Exception as e:
            self.logger.error(f"Failed to load CSV observations: {e}")
            return None
    
    def _load_json_observations(self, file_path: Path, target_date: date) -> Optional[Dict[str, Any]]:
        """加载JSON格式观测数据"""
        try:
            import json
            
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            # 支持多种JSON结构
            if isinstance(data, dict):
                # 检查是否有日期键
                date_str = target_date.isoformat()
                if date_str in data:
                    return data[date_str]
                
                # 检查是否直接包含观测数据
                if 'date' in data and data['date'] == date_str:
                    obs_data = {k: v for k, v in data.items() if k != 'date'}
                    return obs_data
            
            elif isinstance(data, list):
                # 查找匹配日期的记录
                for record in data:
                    if isinstance(record, dict) and 'date' in record:
                        if record['date'] == target_date.isoformat():
                            obs_data = {k: v for k, v in record.items() if k != 'date'}
                            return obs_data
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to load JSON observations: {e}")
            return None
    
    def _load_txt_observations(self, file_path: Path, target_date: date) -> Optional[Dict[str, Any]]:
        """加载文本格式观测数据"""
        try:
            observations = {}
            
            with open(file_path, 'r') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                # 尝试解析键值对
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip().lower()
                    try:
                        observations[key] = float(value.strip())
                    except ValueError:
                        continue
                elif '\t' in line or ',' in line:
                    # 尝试解析表格格式
                    parts = line.replace('\t', ',').split(',')
                    if len(parts) >= 2:
                        try:
                            key = parts[0].strip().lower()
                            value = float(parts[1].strip())
                            observations[key] = value
                        except ValueError:
                            continue
            
            return observations if observations else None
            
        except Exception as e:
            self.logger.error(f"Failed to load TXT observations: {e}")
            return None

class ObservationQualityController:
    """观测数据质量控制器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 质量控制参数
        self.quality_thresholds = config.get('quality_thresholds', {
            'lai': {'min': 0, 'max': 15},
            'biomass': {'min': 0, 'max': 50000},
            'soil_moisture': {'min': 0, 'max': 1},
            'root_depth': {'min': 0, 'max': 500},
            'groundwater_level': {'min': -1000, 'max': 0}
        })
        
        self.outlier_detection = config.get('enable_outlier_detection', True)
        self.outlier_threshold = config.get('outlier_threshold', 3.0)  # 3-sigma规则
    
    def validate_observations(self, observations: Dict[str, Any]) -> Dict[str, Any]:
        """验证观测数据"""
        validated_observations = {}
        
        for var_name, value in observations.items():
            if self._is_valid_observation(var_name, value):
                validated_observations[var_name] = value
            else:
                self.logger.warning(f"Invalid observation for {var_name}: {value}")
        
        return validated_observations
    
    def _is_valid_observation(self, var_name: str, value: Any) -> bool:
        """检查单个观测值是否有效"""
        try:
            # 基本类型检查
            if not isinstance(value, (int, float, np.number)):
                return False
            
            value = float(value)
            
            # 检查是否为NaN或无穷大
            if not np.isfinite(value):
                return False
            
            # 范围检查
            if var_name in self.quality_thresholds:
                thresholds = self.quality_thresholds[var_name]
                if value < thresholds['min'] or value > thresholds['max']:
                    return False
            
            return True
            
        except Exception:
            return False

class UncertaintyManager:
    """不确定性管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 不确定性参数
        self.default_observation_errors = config.get('observation_errors', {
            'lai': 0.5,
            'biomass': 500.0,
            'soil_moisture': 0.05,
            'root_depth': 10.0,
            'groundwater_level': 50.0
        })
    
    def estimate_observation_uncertainty(self, observations: Dict[str, Any]) -> Dict[str, float]:
        """估算观测不确定性"""
        uncertainties = {}
        
        for var_name, value in observations.items():
            if var_name in self.default_observation_errors:
                # 使用默认误差
                uncertainties[f'{var_name}_error'] = self.default_observation_errors[var_name]
            else:
                # 估算误差（简化方法）
                if isinstance(value, (int, float)):
                    uncertainties[f'{var_name}_error'] = abs(value) * 0.1  # 10%相对误差
        
        return uncertainties
    
    def propagate_uncertainty(self, prior_states: Dict[str, Any],
                            observations: Dict[str, Any],
                            posterior_states: Dict[str, Any]) -> Dict[str, Any]:
        """传播不确定性"""
        # 简化的不确定性传播
        uncertainty_info = {
            'prior_uncertainty': self._estimate_state_uncertainty(prior_states),
            'observation_uncertainty': self.estimate_observation_uncertainty(observations),
            'posterior_uncertainty': self._estimate_posterior_uncertainty(
                prior_states, observations, posterior_states
            )
        }
        
        return uncertainty_info
    
    def _estimate_state_uncertainty(self, states: Dict[str, Any]) -> Dict[str, float]:
        """估算状态不确定性"""
        uncertainties = {}
        
        for var_name, value in states.items():
            if isinstance(value, (int, float)):
                # 简化的状态不确定性估算
                uncertainties[f'{var_name}_uncertainty'] = abs(value) * 0.2  # 20%相对不确定性
            elif isinstance(value, np.ndarray):
                # 数组类型的不确定性
                uncertainties[f'{var_name}_uncertainty'] = np.std(value)
        
        return uncertainties
    
    def _estimate_posterior_uncertainty(self, prior_states: Dict[str, Any],
                                      observations: Dict[str, Any],
                                      posterior_states: Dict[str, Any]) -> Dict[str, float]:
        """估算后验不确定性"""
        # 简化的后验不确定性估算
        uncertainties = {}
        
        for var_name in posterior_states.keys():
            if var_name in prior_states and var_name in observations:
                # 使用简化的贝叶斯更新公式
                prior_var = (abs(prior_states[var_name]) * 0.2) ** 2
                obs_var = self.default_observation_errors.get(var_name, abs(observations[var_name]) * 0.1) ** 2
                
                # 后验方差
                posterior_var = 1 / (1/prior_var + 1/obs_var)
                uncertainties[f'{var_name}_posterior_uncertainty'] = np.sqrt(posterior_var)
        
        return uncertainties
```

## 可交付成果

### 主要交付物
1. **数据同化管理器**：AssimilationManager类
2. **观测数据处理模块**：ObservationDataLoader和ObservationQualityController类
3. **不确定性管理器**：UncertaintyManager类
4. **同化接口测试**：test_assimilation_interface.py
5. **观测数据示例**：sample_observations/目录

### 功能特性
- 完整的观测数据处理流程
- 灵活的数据同化算法接口
- 不确定性量化和传播
- 质量控制和验证机制

## 验收标准

### 功能验收
1. **数据加载**：能够正确加载多种格式的观测数据
2. **质量控制**：能够有效检测和过滤异常观测
3. **同化执行**：能够执行基本的数据同化算法
4. **结果存储**：能够正确保存同化结果和元数据

### 性能验收
1. **处理速度**：观测数据处理时间小于1秒
2. **内存使用**：大量观测数据处理时内存使用合理
3. **准确性**：同化结果符合预期

### 质量验收
1. **代码覆盖率**：单元测试覆盖率达到85%以上
2. **文档完整性**：所有接口有完整的文档字符串
3. **扩展性**：支持新的同化算法和观测类型

## 依赖关系
- **前置依赖**：任务01-06（完整的基础系统）
- **后续任务**：任务08（测试和验证）、任务09（文档和示例）


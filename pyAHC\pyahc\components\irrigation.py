# mypy: disable-error-code="call-overload, misc, override"
# - model_string 上引发了 override 错误，因为这些方法不共享相同的签名。这不是一个优先修复的问题。
"""AHC 模拟的灌溉设置。


- Pydantic 验证框架
- 外部参数配置
- 移除硬编码默认事件
- 灌溉事件时间验证
- 灌溉参数合理性验证

类：
    FixedIrrigationOriginal: 原始固定灌溉设置。
    ScheduledIrrigation: 灌溉调度设置。
    FixedIrrigation: 重构后的固定灌溉设置。

函数：
    irg_from_csv: 从 CSV 文件加载灌溉文件。
"""

from pathlib import Path as _Path
from typing import Optional as _Optional, List as _List, Tuple as _Tuple, Dict as _Dict, Any as _Any, Literal as _Literal, Union as _Union

from pydantic import (
    Field as _Field,
    PrivateAttr as _PrivateAttr,
    model_validator as _model_validator,
)

from pyahc.components.tables import IRRIGEVENTS
from pyahc.core.basemodel import PyAHCBaseModel as _PyAHCBaseModel
from pyahc.core.configurable import ConfigurableComponent as _ConfigurableComponent
from pyahc.core.defaults import FNAME_IN as _FNAME_IN
from pyahc.core.fields import (
    DayMonth as _DayMonth,
    String as _String,
    Table as _Table,
    PositiveFloat as _PositiveFloat,
    ConcentrationFloat as _ConcentrationFloat,
)
from pyahc.core.valueranges import YEARRANGE as _YEARRANGE
from pyahc.utils.mixins import (
    FileMixin as _FileMixin,
    SerializableMixin as _SerializableMixin,
    YAMLValidatorMixin as _YAMLValidatorMixin,
)

__all__ = ["FixedIrrigationOriginal", "ScheduledIrrigation", "FixedIrrigation", "IRRIGEVENTS"]


class FixedIrrigationOriginal(
    _PyAHCBaseModel, _SerializableMixin, _FileMixin, _YAMLValidatorMixin
):
    """ .swp 文件中的固定灌溉设置（原始版本）。

    属性：
        swirfix (Literal[0, 1]): 固定灌溉应用开关
        swirgfil (Literal[0, 1]): 独立文件固定灌溉应用开关
        irrigevents (Optional[Table]):
        irgfil (Optional[str]):

        # 新增可配置参数
        default_temp (Optional[float]): 默认灌溉水温度，默认为 15.8°C
        default_fwet (Optional[float]): 默认湿润分数，默认为 1.0
        default_conc (Optional[float]): 默认溶质浓度，默认为 0.5 mg/cm3
        default_irtype (Optional[int]): 默认灌溉类型，默认为 1 (地表灌溉)
        use_default_events (Optional[bool]): 是否使用默认灌溉事件，默认为 True
        default_events (Optional[list]): 默认灌溉事件列表
    """

    _extension = _PrivateAttr(default="irg")

    swirfix: _Literal[0, 1] | None = None
    swirgfil: _Literal[0, 1] | None = None
    irgfil: _String = _Field(default=_FNAME_IN, frozen=True)
    irrigevents: _Table | None = None

    # 新增可配置参数
    default_temp: float | None = _Field(default=15.8, ge=0.0, le=50.0)
    default_fwet: float | None = _Field(default=1.0, ge=0.0, le=1.0)
    default_conc: float | None = _Field(default=0.5, ge=0.0, le=100.0)
    default_irtype: int | None = _Field(default=1, ge=0, le=1)
    use_default_events: bool | None = _Field(default=True)
    default_events: list | None = _Field(default_factory=lambda: [
        {"day": 1, "month": 6, "depth": 10, "temp": 15.0, "irtype": 1, "fwet": 1.0, "conc": 0.1}
    ])

    def model_string(self, **kwargs) -> str:
        """覆盖 model_string 以处理可选文件生成。

        如果 swirgfil 设置为 1，则返回完整部分；否则，irrigevents 将从字符串中排除并保存到单独的 .irg 文件中。
        """
        if self.swirgfil == 1:
            return super().model_string(exclude={"irrigevents"}, **kwargs)
        else:
            return super().model_string()

    def irg(self, **kwargs):
        """生成 .irg 文件内容，严格按照 AHC V201 格式。

        Args:
            **kwargs: 可选参数，用于覆盖默认值
        """
        content = []

        # 从kwargs或实例属性获取参数值
        filename = kwargs.get('filename', self.irgfil or "irrigation")
        default_temp = kwargs.get('default_temp', self.default_temp or 15.8)
        default_fwet = kwargs.get('default_fwet', self.default_fwet or 1.0)
        default_conc = kwargs.get('default_conc', self.default_conc or 0.5)
        default_irtype = kwargs.get('default_irtype', self.default_irtype or 1)
        use_default_events = kwargs.get('use_default_events', self.use_default_events if self.use_default_events is not None else True)
        default_events = kwargs.get('default_events', self.default_events or [
            {"day": 1, "month": 6, "depth": 1, "temp": 15.0, "irtype": 1, "fwet": 1.0, "conc": 0.1}
        ])

        # 文件头部注释
        content.append("********************************************************************************")
        content.append(f"* Filename: {filename}.irg")
        content.append("* Contents: AHC 2.0 - Fixed irrigations   ")
        content.append("* concent_i: concentration i for the ith specifies ")
        content.append("* Irri_type: sprinkling (=0) or surface (=1) application")
        content.append("********************************************************************************")
        content.append("")
        content.append("* List for each irrigation application (max 50 records):")
        content.append("* day month  depth   temp  Irri_type   Fwet   concent_1 concent_2 concent_3...concent_8 ")
        content.append("*             mm       C                         mg/cm3   mg/cm3    mg/cm3 ...  mg/cm3")

        # 如果有灌溉事件数据，则添加数据行
        if self.irrigevents is not None and not self.irrigevents.empty:
            for _, row in self.irrigevents.iterrows():
                # 提取日期信息
                irdate = row['IRDATE']
                day = irdate.day
                month = irdate.month

                # 提取其他信息，使用可配置的默认值
                depth = row.get('IRDEPTH', 0.0)
                temp = row.get('IRTEMP', default_temp)
                irtype = row.get('IRTYPE', default_irtype)
                fwet = row.get('IRFWET', default_fwet)
                conc = row.get('IRCONC', default_conc)

                # 格式化数据行
                line = f"  {day:2d}    {month}     {depth:.0f}   {temp}    {irtype}     {fwet}   {conc} "
                content.append(line)
        elif use_default_events and default_events:
            # 使用可配置的默认灌溉数据
            for event in default_events:
                day = event.get('day', 1)
                month = event.get('month', 1)
                depth = event.get('depth', 0.0)
                temp = event.get('temp', default_temp)
                irtype = event.get('irtype', default_irtype)
                fwet = event.get('fwet', default_fwet)
                conc = event.get('conc', default_conc)

                line = f"  {day:2d}    {month}     {depth:.0f}   {temp}    {irtype}     {fwet}   {conc} "
                content.append(line)

        # 文件结尾
        content.append("* End of file ******************************************************************")
        content.append("              ")
        content.append("* End of file ******************************************************************")
        content.append("******************")

        return "\n".join(content)

    def write_irg(self, path: _Path, **kwargs):
        """将灌溉数据写入 .irg 文件。

        此方法仅在 swirgfil 属性设置为 1 时可用。

        参数：
            path (Path): .irg 文件将保存到的目录路径。
            **kwargs: 传递给irg方法的可选参数
        """
        if self.swirgfil != 1:
            msg = "Irrigation data are not set to be written to a .irg file."
            raise ValueError(msg)

        self.save_file(string=self.irg(**kwargs), fname=self.irgfil, path=path)


class ScheduledIrrigation(_PyAHCBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """ .crp 文件中的灌溉调度设置。

    属性：
        schedule (Literal[0, 1]): 应用灌溉调度开关
        startirr (str): 指定灌溉调度开始的日期和月份
        endirr (str): 指定灌溉调度结束的日期和月份
        cirrs (float): 灌溉水的溶质浓度
        isuas (int): 灌溉方法类型开关

            * 0 - 喷灌
            * 1 - 地表灌溉

        tcs (int): 选择以下时间标准选项之一

            * 1 - 实际/潜在蒸腾比
            * 2 - 易利用水耗尽
            * 3 - 总利用水耗尽
            * 4 - 绝对水量耗尽
            * 6 - 固定每周灌溉
            * 7 - 压头
            * 8 - 含水量

        phFieldCapacity (float): 田间持水量的土壤水压头
        irgthreshold (Optional[float]): 每周灌溉的阈值
        dcrit (Optional[float]): 传感器深度
        swcirrthres (Optional[bool]): 过度灌溉开关
        cirrthres (Optional[float]): 发生过度灌溉的阈值盐度浓度
        perirrsurp (Optional[float]): 通常计划的灌溉深度的过度灌溉
        tcsfix (Optional[int]): 灌溉应用之间最小时间间隔开关
        irgdayfix (Optional[int]): 灌溉应用之间最小天数
        phormc (Optional[int]): 使用压头或含水量的开关

            * 0 - 压头
            * 1 - 含水量

        dvs_tc1 (Optional[Table]):
        dvs_tc2 (Optional[Table]):
        dvs_tc3 (Optional[Table]):
        dvs_tc4 (Optional[Table]):
        dvs_tc5 (Optional[Table]):
    """

    schedule: _Literal[0, 1] | None = None
    startirr: _DayMonth | None = None
    endirr: _DayMonth | None = None
    cirrs: float | None = _Field(default=None, ge=0.0, le=100.0)
    isuas: _Literal[0, 1] | None = None
    tcs: _Literal[1, 2, 3, 4, 6, 7, 8] | None = None

    phfieldcapacity: float | None = _Field(default=None, ge=-1000.0, le=0.0)
    irgthreshold: float | None = _Field(default=None, ge=0.0, le=20.0)
    dcrit: float | None = _Field(default=None, ge=-100.0, le=0.0)
    swcirrthres: _Literal[0, 1] | None = None
    cirrthres: float | None = _Field(default=None, ge=0.0, le=100.0)
    perirrsurp: float | None = _Field(default=None, ge=0.0, le=100.0)
    tcsfix: _Literal[0, 1] | None = None
    irgdayfix: int | None = _Field(default=None, **_YEARRANGE)
    dcs: _Literal[0, 1] | None = None
    dcslim: _Literal[0, 1] | None = None
    irgdepmin: float | None = _Field(default=None, ge=0.0, le=100.0)
    irgdepmax: float | None = _Field(default=None, ge=0.0, le=1.0e7)
    tc1tb: _Table | None = None
    tc2tb: _Table | None = None
    tc3tb: _Table | None = None
    tc4tb: _Table | None = None
    tc7tb: _Table | None = None
    tc8tb: _Table | None = None
    dc1tb: _Table | None = None
    dc2tb: _Table | None = None


class FixedIrrigation(_ConfigurableComponent, _YAMLValidatorMixin, _FileMixin):
    """重构后的固定灌溉组件 - 支持外部参数配置，移除硬编码默认事件

    该类实现了完整的灌溉参数验证，包括：
    - 灌溉事件时间验证
    - 灌溉量合理性验证
    - 灌溉参数一致性验证
    - 移除复杂的硬编码默认事件

    支持三种灌溉类型：
    1. 固定灌溉 (irrigation_type=1)
    2. 调度灌溉 (irrigation_type=2)
    3. 自动灌溉 (irrigation_type=3)
    """

    _extension = _PrivateAttr(default="irg")

    # 灌溉类型参数（外部化配置）
    irrigation_type: _Optional[_Literal[1, 2, 3]] = _Field(default=None, description="灌溉类型选择")

    # 灌溉事件数据（外部化配置）
    irrigation_events: _Optional[_List[_Union[_Tuple[int, int, float], _Tuple[int, int, float, float]]]] = _Field(
        default=None, description="灌溉事件数据 [(日, 月, 灌溉量) 或 (日, 月, 灌溉量, 温度)]"
    )

    # 灌溉水质参数（外部化配置）
    irrigation_concentration: _Optional[_ConcentrationFloat] = _Field(
        default=None, description="灌溉水浓度"
    )

    # 灌溉效率参数
    irrigation_efficiency: _Optional[float] = _Field(
        default=None, ge=0.0, le=1.0, description="灌溉效率"
    )

    # 灌溉水温度参数（外部化配置）
    irrigation_temperature: _Optional[_PositiveFloat] = _Field(
        default=None, description="灌溉水温度"
    )

    # 湿润分数参数（外部化配置）
    irrigation_wetfraction: _Optional[float] = _Field(
        default=None, ge=0.0, le=1.0, description="湿润分数"
    )

    # 灌溉方法参数（外部化配置）
    irrigation_method: _Optional[_Literal[0, 1]] = _Field(
        default=None, description="灌溉方法：0=喷灌，1=地表灌溉"
    )

    # 兼容性参数（保持向后兼容）
    swirfix: _Literal[0, 1] | None = _Field(default=None, description="固定灌溉应用开关")
    swirgfil: _Literal[0, 1] | None = _Field(default=None, description="独立文件固定灌溉应用开关")
    irgfil: _String = _Field(default=_FNAME_IN, frozen=True, description="灌溉文件名")
    irrigevents: _Table | None = _Field(default=None, description="灌溉事件表")

    # 兼容性默认参数（外部化配置）
    default_temp: float | None = _Field(default=None, ge=0.0, le=50.0, description="默认灌溉水温度")
    default_fwet: float | None = _Field(default=None, ge=0.0, le=1.0, description="默认湿润分数")
    default_conc: float | None = _Field(default=None, ge=0.0, le=100.0, description="默认溶质浓度")
    default_irtype: int | None = _Field(default=None, ge=0, le=1, description="默认灌溉类型")
    use_default_events: bool | None = _Field(default=None, description="是否使用默认灌溉事件")
    default_events: list | None = _Field(default=None, description="默认灌溉事件列表")

    @_model_validator(mode="after")
    def validate_irrigation_parameters(self):
        """验证灌溉参数"""
        if not self._validation:
            return self

        # 如果没有设置 irrigation_type，跳过验证（向后兼容）
        if self.irrigation_type is None:
            return self

        # 验证灌溉事件数据
        if self.irrigation_events:
            for day, month, amount in self.irrigation_events:
                if not (1 <= day <= 31 and 1 <= month <= 12):
                    raise ValueError(f"Invalid irrigation date: {day}/{month}")
                if amount <= 0:
                    raise ValueError(f"Irrigation amount must be > 0, got {amount}")

        return self

    @_model_validator(mode="after")
    def validate_irrigation_consistency(self):
        """验证灌溉参数一致性"""
        if not self._validation:
            return self

        # 验证灌溉效率与灌溉量的一致性
        if self.irrigation_efficiency is not None and self.irrigation_events:
            for day, month, amount in self.irrigation_events:
                effective_amount = amount * self.irrigation_efficiency
                if effective_amount > amount:
                    raise ValueError(
                        f"Effective irrigation amount ({effective_amount}) cannot exceed applied amount ({amount})"
                    )

        # 验证温度合理性
        if self.irrigation_temperature is not None:
            if self.irrigation_temperature > 50.0:
                raise ValueError(f"Irrigation temperature seems too high: {self.irrigation_temperature}°C")
            if self.irrigation_temperature < 0.0:
                raise ValueError(f"Irrigation temperature cannot be negative: {self.irrigation_temperature}°C")

        # 验证浓度合理性
        if self.irrigation_concentration is not None:
            if self.irrigation_concentration > 100.0:
                raise ValueError(f"Irrigation concentration seems too high: {self.irrigation_concentration} mg/cm³")

        return self

    @_model_validator(mode="after")
    def apply_minimal_defaults(self):
        """应用最小默认值，移除复杂的硬编码默认事件"""
        if not self._validation:
            return self

        # 如果没有提供灌溉事件，使用最小默认值而不是复杂的默认事件
        if not self.irrigation_events and not self.irrigevents:
            # 使用最小灌溉事件：1月1日，0.1mm（几乎无灌溉）
            self.irrigation_events = [(1, 1, 0.1)]

        return self

    @classmethod
    def from_irrigation_type(cls, irrigation_type: int, config_overrides: _Optional[_Dict[str, _Any]] = None) -> "FixedIrrigation":
        """根据灌溉类型创建实例"""
        # 灌溉类型默认配置
        type_configs = {
            1: {  # 固定灌溉
                'irrigation_type': 1,
                'irrigation_events': [
                    (15, 5, 50.0),  # 5月15日，50mm
                    (30, 6, 40.0),  # 6月30日，40mm
                    (15, 7, 60.0),  # 7月15日，60mm
                ],
                'irrigation_efficiency': 0.85,
                'irrigation_temperature': 20.0,
                'irrigation_concentration': 0.0,
                'irrigation_method': 1
            },
            2: {  # 调度灌溉
                'irrigation_type': 2,
                'irrigation_events': [
                    (1, 6, 30.0),   # 6月1日，30mm
                    (15, 7, 45.0),  # 7月15日，45mm
                ],
                'irrigation_efficiency': 0.90,
                'irrigation_temperature': 18.0,
                'irrigation_concentration': 0.1
            },
            3: {  # 自动灌溉
                'irrigation_type': 3,
                'irrigation_events': [
                    (10, 5, 25.0),  # 5月10日，25mm
                    (20, 6, 35.0),  # 6月20日，35mm
                    (10, 8, 40.0),  # 8月10日，40mm
                ],
                'irrigation_efficiency': 0.95,
                'irrigation_temperature': 22.0,
                'irrigation_concentration': 0.05
            }
        }

        if irrigation_type not in type_configs:
            raise ValueError(f"Unsupported irrigation type: {irrigation_type}. Supported types: {list(type_configs.keys())}")

        base_config = type_configs[irrigation_type].copy()

        if config_overrides:
            base_config.update(config_overrides)

        return cls(**base_config)

    @classmethod
    def from_config(cls, config: _Dict[str, _Any]) -> "FixedIrrigation":
        """从外部配置创建实例"""
        return cls(**config)

    def model_string(self, **kwargs) -> str:
        """覆盖 model_string 以处理可选文件生成（保持向后兼容）"""
        if self.swirgfil == 1:
            return super().model_string(exclude={"irrigevents"}, **kwargs)
        else:
            return super().model_string()

    def irg(self, **kwargs):
        """生成 .irg 文件内容（保持向后兼容，但使用外部化参数）"""
        content = []

        # 从kwargs或实例属性获取参数值（优先使用新的外部化参数）
        filename = kwargs.get('filename', self.irgfil or "irrigation")
        default_temp = kwargs.get('default_temp',
                                 self.irrigation_temperature or
                                 self.default_temp or 15.8)
        default_fwet = kwargs.get('default_fwet',
                                 self.irrigation_wetfraction or
                                 self.default_fwet or 1.0)
        default_conc = kwargs.get('default_conc',
                                 self.irrigation_concentration or
                                 self.default_conc or 0.5)
        default_irtype = kwargs.get('default_irtype',
                                   self.irrigation_method or
                                   self.default_irtype or 1)

        # 文件头部注释
        content.append("********************************************************************************")
        content.append(f"* Filename: {filename}.irg")
        content.append("* Contents: AHC 2.0 - Fixed irrigations (Refactored)")
        content.append("* concent_i: concentration i for the ith specifies ")
        content.append("* Irri_type: sprinkling (=0) or surface (=1) application")
        content.append("********************************************************************************")
        content.append("")
        content.append("* List for each irrigation application (max 50 records):")
        content.append("* day month  depth   temp  Irri_type   Fwet   concent_1 concent_2 concent_3...concent_8 ")
        content.append("*             mm       C                         mg/cm3   mg/cm3    mg/cm3 ...  mg/cm3")

        # 处理灌溉事件数据
        events_added = False

        # 优先使用新的外部化灌溉事件
        if self.irrigation_events:
            for event in self.irrigation_events:
                if len(event) == 3:
                    # 格式：(day, month, amount)
                    day, month, amount = event
                    temp = default_temp
                elif len(event) == 4:
                    # 格式：(day, month, amount, temp)
                    day, month, amount, temp = event
                else:
                    # 默认格式
                    day, month, amount = event[0], event[1], event[2]
                    temp = default_temp

                line = f"  {day:2d}    {month}     {amount:.0f}   {temp}    {default_irtype}     {default_fwet}   {default_conc} "
                content.append(line)
                events_added = True

        # 如果没有新的事件，回退到原有的irrigevents表
        elif self.irrigevents is not None and not self.irrigevents.empty:
            for _, row in self.irrigevents.iterrows():
                irdate = row['IRDATE']
                day = irdate.day
                month = irdate.month
                depth = row.get('IRDEPTH', 0.0)
                temp = row.get('IRTEMP', default_temp)
                irtype = row.get('IRTYPE', default_irtype)
                fwet = row.get('IRFWET', default_fwet)
                conc = row.get('IRCONC', default_conc)

                line = f"  {day:2d}    {month}     {depth:.0f}   {temp}    {irtype}     {fwet}   {conc} "
                content.append(line)
                events_added = True

        # 如果仍然没有事件，使用最小默认事件（而不是复杂的默认事件）
        if not events_added:
            # 最小默认事件：1月1日，0.1mm
            line = f"   1    1       0.1   {default_temp}    {default_irtype}     {default_fwet}   {default_conc} "
            content.append(line)

        # 文件结尾
        content.append("* End of file ******************************************************************")
        content.append("              ")
        content.append("* End of file ******************************************************************")
        content.append("******************")

        return "\n".join(content)

    def write_irg(self, path: _Path, **kwargs):
        """将灌溉数据写入 .irg 文件（保持向后兼容）"""
        if self.swirgfil != 1:
            msg = "Irrigation data are not set to be written to a .irg file."
            raise ValueError(msg)

        self.save_file(string=self.irg(**kwargs), fname=self.irgfil, path=path)

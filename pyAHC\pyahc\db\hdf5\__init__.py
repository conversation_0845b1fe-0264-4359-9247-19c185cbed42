"""pyAHC HDF5数据管理模块

提供基于HDF5的项目数据存储和管理功能，支持：
- 项目级别的数据组织
- 日循环模拟的状态传递
- 状态变量的提取和注入
- 数据同化的基础架构
"""

from .manager import ProjectHDF5Manager
from .state_extractor import StateExtractor
from .state_injector import StateInjector
from .utils import get_variable_units, validate_date_format
from .exceptions import (
    HDF5Error,
    ProjectNotFoundError,
    DataNotFoundError,
    InvalidDataFormatError
)

__version__ = "0.1.0"
__all__ = [
    "ProjectHDF5Manager",
    "StateExtractor", 
    "StateInjector",
    "get_variable_units",
    "validate_date_format",
    "HDF5Error",
    "ProjectNotFoundError",
    "DataNotFoundError",
    "InvalidDataFormatError"
]

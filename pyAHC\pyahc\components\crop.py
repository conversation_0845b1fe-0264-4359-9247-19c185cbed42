# mypy: disable-error-code="call-overload, misc, type-arg"
# 这里的 type-arg 会导致将实际类型传递给别名 Subsection 时出现问题。
# 但这有助于类型提示，因此必须保持这种方式。
"""AHC 模型的作物设置和作物文件。

与 .dra 或 .swp 文件类似，.crp 文件是 AHC 模型的配置文件。
此模块中的类表示 .crp 文件的不同部分。主类是 `CropFile` 类，
它包含作物模拟的设置。

AHC 有三种作物模拟模式，用户在 .swp 文件中的 CROPROTATION 表中定义：

    * 1 - 简单作物设置 - 使用 CropDevelopmentSettingsFixed
    * 2 - 详细的 WOFOST 通用设置 - 使用 CropDevelopmentSettingsWOFOST
    * 3 - 动态草地生长模型 - 使用 CropDevelopmentSettingsGrass

对于每种选择，.crp 文件将有所不同。因此，此模块中定义了多个类来处理这些不同的设置。

类：
    CropFile: .crp 文件的类。
    CropDevelopmentSettingsWOFOST: WOFOST 中作物发育设置的类。
    CropDevelopmentSettingsFixed: 固定作物发育设置的类。
    CropDevelopmentSettingsGrass: 草地作物发育设置的类。
    OxygenStress: 氧胁迫设置的类。
    DroughtStress: 干旱胁迫设置的类。
    SaltStress: 盐胁迫设置的类。
    CompensateRWUStress: 补偿根系吸水胁迫设置的类。
    Interception: 截留设置的类。
    CO2Correction: CO2 校正设置的类。
    ScheduledIrrigation: 计划灌溉设置的类。
    Preparation: 准备设置的类。
"""

from typing import (
    Any as _Any,
    Literal as _Literal,
    Optional as _Optional,
    Dict as _Dict,
    List as _List,
)

# 常量定义
MAX_ROOTING_DEPTH = 300  # 3米，最大根深度阈值
MAX_CANOPY_HEIGHT = 500  # 5米，最大冠层高度阈值
MAX_LEAF_AREA_INDEX = 10  # 最大叶面积指数阈值

from pydantic import (
    Field as _Field,
    PrivateAttr as _PrivateAttr,
    model_validator as _model_validator,
)

from pyahc.components.irrigation import ScheduledIrrigation as _ScheduledIrrigation
from pyahc.components.tables import (
    AMAXTB,
    CFTB,
    CROPROTATION,
    DMGRZTB,
    DMMOWDELAY,
    DMMOWTB,
    DTSMTB,
    FLTB,
    FOTB,
    FRTB,
    FSTB,
    GCTB,
    KYTB,
    LSDATB,
    LSDBTB,
    MRFTB,
    RDCTB,
    RDRRTB,
    RDRSTB,
    RDTB,
    RFSETB,
    RLWTB,
    SLATB,
    TMNFTB,
    TMPFTB,
    WRTB,
)
from pyahc.core.basemodel import PyAHCBaseModel as _PyAHCBaseModel
from pyahc.core.fields import (
    Arrays as _Arrays,
    Decimal2f as _Decimal2f,
    IntList as _IntList,
    Subsection as _Subsection,
    Table as _Table,
    PositiveFloat as _PositiveFloat,
    UnitFloat as _UnitFloat,
    TemperatureFloat as _TemperatureFloat,
)
from pyahc.core.valueranges import (
    UNITRANGE as _UNITRANGE,
    YEARRANGE as _YEARRANGE,
)
from pyahc.db.cropdb import CropVariety as _CropVariety
from pyahc.utils.mixins import (
    FileMixin as _FileMixin,
    SerializableMixin as _SerializableMixin,
    WOFOSTUpdateMixin as _WOFOSTUpdateMixin,
    YAMLValidatorMixin as _YAMLValidatorMixin,
)

__all__ = [
    "CropDevelopmentSettingsWOFOST",
    "CropDevelopmentSettingsFixed",
    "CropDevelopmentSettingsGrass",
    "OxygenStress",
    "DroughtStress",
    "SaltStress",
    "CompensateRWUStress",
    "Interception",
    "CO2Correction",
    "Preparation",
    "CropFile",
    "Crop",
    "RDTB",
    "RDCTB",
    "GCTB",
    "CFTB",
    "KYTB",
    "MRFTB",
    "WRTB",
    "CROPROTATION",
    "DTSMTB",
    "SLATB",
    "AMAXTB",
    "TMPFTB",
    "TMNFTB",
    "RFSETB",
    "FRTB",
    "FLTB",
    "FSTB",
    "FOTB",
    "RDRRTB",
    "RDRSTB",
    "DMGRZTB",
    "LSDATB",
    "LSDBTB",
    "RLWTB",
    "DMMOWTB",
    "DMMOWDELAY",
]


class _CropDevelopmentSettings(
    _PyAHCBaseModel, _SerializableMixin, _YAMLValidatorMixin, _WOFOSTUpdateMixin
):
    """作物发育设置。

    !!! 注意：

        CropDevelopmentSettings 是不同作物发育设置（类型 1、2 和 3）的基类。
        作物参数可以从 WOFOST 数据库中读取。由于 WOFOST 和 AHC 模板之间某些相同参数的名称不同，
        因此使用 alias 参数在序列化时重命名参数以兼容 AHC 的 .crp 文件。
        目前它适用于 TSUM1 和 TSUM2。

    属性：
        wofost_variety (CropVariety): 作物品种设置。
        swcf (Literal[1, 2]): 选择作物系数或作物高度

            * 1 - 作物系数
            * 2 - 作物高度

        _table_dvs_cf (Optional[_Table]): 作物系数随发育阶段变化的表格
        _table_dvs_ch (Optional[_Table]): 作物高度随发育阶段变化的表格
        albedo (Optional[float]): 作物反射系数
        rsc (Optional[float]): 最小冠层阻力
        rsw (Optional[float]): 截留水分的冠层阻力
        tsumea (float): 从出苗到开花期的温度总和
        tsumam (float): 从开花到成熟期的温度总和
        tbase (Optional[float]): 温度总和的起始值
        kdif (float): 漫射可见光的消光系数
        kdir (float): 直射可见光的消光系数
        swrd (Optional[Literal[1, 2, 3]]): 根系生长发育开关

            * 1 - 根系生长取决于发育阶段
            * 2 - 根系生长取决于最大日增量
            * 3 - 根系生长取决于可用根系生物量

        rdtb (Optional _Arrays]): 根深随发育阶段变化的数组
        rdi (float): 初始根深
        rri (float): 根深最大日增量
        rdc (float): 特定作物的最大根深
        swdmi2rd (Optional[Literal[0, 1]]): 根深计算开关

            * 0 - 根深增加与根系同化物的可用性有关
            * 1 - 根深增加与相对干物质增加有关

        rlwtb (Optional _Arrays]): 根深随根重变化的数组
        wrtmax (float): 最大根重
        swrdc (Literal[0, 1]): 相对根系密度计算开关
        rdctb  _Arrays): 根系密度随相对根深变化的数组
    """

    # 在模型配置中添加允许额外属性
    # model_config = _ConfigDict(
    #     extra="allow"
    # )

    wofost_variety: _Any | None = _Field(default=None, exclude=True)

    swcf: _Literal[1, 2] | None = None
    cftb: _Table | None = None
    albedo: _Decimal2f | None = _Field(default=None, **_UNITRANGE)
    rsc: _Decimal2f | None = _Field(default=None, ge=0.0, le=1.0e6)
    rsw: _Decimal2f | None = _Field(default=None, ge=0.0, le=1.0e6)
    tsum1: _Decimal2f | None = _Field(alias="tsumea", default=None, ge=0.0, le=1.0e4)
    tsum2: _Decimal2f | None = _Field(alias="tsumam", default=None, ge=0.0, le=1.0e4)
    tbase: _Decimal2f | None = _Field(default=None, ge=-10.0, le=30.0)
    kdif: _Decimal2f | None = _Field(default=None, ge=0.0, le=2.0)
    kdir: _Decimal2f | None = _Field(default=None, ge=0.0, le=2.0)
    swrd: _Literal[1, 2, 3] | None = None
    rdtb: _Arrays | None = None
    rdi: _Decimal2f | None = _Field(default=None, ge=0.0, le=1000.0)
    rri: _Decimal2f | None = _Field(default=None, ge=0.0, le=100.0)
    rdc: _Decimal2f | None = _Field(default=None, ge=0.0, le=1000.0)
    swdmi2rd: _Literal[0, 1] | None = None
    rlwtb: _Arrays | None = None
    wrtmax: _Decimal2f | None = _Field(default=None, ge=0.0, le=1.0e5)
    swrdc: _Literal[0, 1] | None = None
    rdctb: _Arrays | None = None


class CropDevelopmentSettingsWOFOST(_CropDevelopmentSettings):
    """WOFOST 模型中定义的附加设置。

    属性：
        idsl (Literal[0, 1, 2]): 作物发育开关。
        dtsmtb  _Arrays): 温度总和随日平均温度变化的列表。
        dlo (Optional[float]): 作物发育的最佳日长。
        dlc (Optional[float]): 最小日长。
        vernsat (Optional[float]): 饱和春化需求。
        vernbase (Optional[float]): 基础春化需求。
        verndvs (Optional[float]): 春化作用停止的关键发育阶段。
        verntb (Optional _Arrays]): 春化速率随平均气温变化的表格。
        tdwi (float): 初始作物总干重。
        laiem (float): 出苗时的叶面积指数。
        rgrlai (float): 叶面积指数的最大相对增长率。
        spa (float): 特定荚面积。
        ssa (float): 特定茎面积。
        span (float): 最佳条件下叶片的寿命。
        slatb  _Arrays): 特定叶面积随作物发育阶段变化的列表。
        eff (float): 真实叶片的光能利用效率。
        amaxtb  _Arrays): 最大 CO2 同化率随发育阶段变化的列表。
        tmpftb  _Arrays): AMAX 随日平均温度变化的折减系数列表。
        tmnftb  _Arrays): AMAX 随日最低温度变化的折减系数列表。
        cvo (float): 转化为贮藏器官的效率。
        cvl (float): 转化为叶片的效率。
        cvr (float): 转化为根的效率。
        cvs (float): 转化为茎的效率。
        q10 (float): 呼吸速率随温度升高的增加量。
        rml (float): 叶片的维持呼吸速率。
        rmo (float): 贮藏器官的维持呼吸速率。
        rmr (float): 根的维持呼吸速率。
        rms (float): 茎的维持呼吸速率。
        rfsetb  _Arrays): 衰老折减系数随发育阶段变化的列表。
        frtb  _Arrays): 总干物质增加分配到根的比例随发育阶段变化的列表。
        fltb  _Arrays): 地上总干物质增加分配到叶的比例随发育阶段变化的列表。
        fstb  _Arrays): 地上总干物质增加分配到茎的比例随发育阶段变化的列表。
        fotb  _Arrays): 地上总干物质增加分配到贮藏器官的比例随发育阶段变化的列表。
        perdl (float): 因水分胁迫导致叶片的最大相对死亡率。
        rdrrtb  _Arrays): 根的相对死亡率随发育阶段变化的列表。
        rdrstb  _Arrays): 茎的相对死亡率随发育阶段变化的列表。
    """

    idsl: _Literal[0, 1, 2] | None = None
    dtsmtb: _Arrays | None = None
    dlo: float | None = _Field(default=None, ge=0.0, le=24.0)
    dlc: float | None = _Field(default=None, ge=0.0, le=24.0)
    vernsat: float | None = _Field(default=None, ge=0.0, le=100.0)
    vernbase: float | None = _Field(default=None, ge=0.0, le=100.0)
    verndvs: float | None = _Field(default=None, ge=0.0, le=0.3)
    verntb: _Arrays | None = None
    tdwi: float | None = _Field(default=None, ge=0.0, le=10_000)
    laiem: float | None = _Field(default=None, ge=0.0, le=10)
    rgrlai: float | None = _Field(default=None, **_UNITRANGE)
    spa: float | None = _Field(**_UNITRANGE, default=None)
    ssa: float | None = _Field(default=None, **_UNITRANGE)
    span: float | None = _Field(default=None, **_YEARRANGE)
    slatb: _Arrays | None = None
    eff: float | None = _Field(default=None, ge=0.0, le=10.0)
    amaxtb: _Arrays | None = None
    tmpftb: _Arrays | None = None
    tmnftb: _Arrays | None = None
    cvo: float | None = _Field(default=None, **_UNITRANGE)
    cvl: float | None = _Field(default=None, **_UNITRANGE)
    cvr: float | None = _Field(default=None, **_UNITRANGE)
    cvs: float | None = _Field(default=None, **_UNITRANGE)
    q10: float | None = _Field(default=None, ge=0.0, le=5.0)
    rml: float | None = _Field(default=None, **_UNITRANGE)
    rmo: float | None | None = _Field(**_UNITRANGE, default=None)
    rmr: float | None = _Field(default=None, **_UNITRANGE)
    rms: float | None = _Field(default=None, **_UNITRANGE)
    rfsetb: _Arrays | None = None
    frtb: _Arrays | None = None
    fltb: _Arrays | None = None
    fstb: _Arrays | None = None
    fotb: _Arrays | None = None
    perdl: float | None = _Field(default=None, ge=0.0, le=3.0)
    rdrrtb: _Arrays | None = None
    rdrstb: _Arrays | None = None


class CropDevelopmentSettingsFixed(_CropDevelopmentSettings):
    """固定作物发育设置（附加到 CropDevelopmentSettings）。

    属性：
        idev (Literal[1, 2]): 作物生长期持续时间

            * 1 - 持续时间固定
            * 2 - 持续时间可变

        lcc (Optional[int]): 作物生长期持续时间
        swgc (Literal[1, 2]): 选择叶面积指数或土壤覆盖率

            * 1 - 叶面积指数 (LAI)
            * 2 - 土壤覆盖率 (SCF)

        gctb  _Arrays): 土壤覆盖率随发育阶段变化的数组
    """

    idev: _Literal[1, 2] | None = None
    lcc: int | None = _Field(default=None, **_YEARRANGE)
    swgc: _Literal[1, 2] | None = None
    gctb: _Arrays | None = None
    kytb: _Arrays | None = None


class CropDevelopmentSettingsGrass(CropDevelopmentSettingsWOFOST):
    """草地生长特有的作物发育设置。

    属性：
        swtsum (Literal[0, 1, 2]): 选择气温总和或特定深度的土壤温度

            * 0 - 草地生长开始无延迟
            * 1 - 草地生长开始基于气温总和 > 200 摄氏度
            * 2 - 草地生长开始基于特定深度的土壤温度

        tsumtemp (Optional[float]): 特定茎面积 [0..1 公顷/千克, R]
        tsumdepth (Optional[float]): 最佳条件下叶片的寿命 [0..366 天, R]
        tsumtime (Optional[float]): 叶片老化下限温度 [-10..30 摄氏度, R]
    """

    swtsum: _Literal[0, 1, 2] | None = None
    tsumtemp: float | None = None
    tsumdepth: float | None = None
    tsumtime: float | None = None


class OxygenStress(_PyAHCBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """用于 .crp 文件的氧胁迫设置。

    属性：
        swoxygen (Literal[0, 1, 2]): 氧胁迫开关

            * 0 - 无氧胁迫
            * 1 - 根据 Feddes 等人 (1978) 的氧胁迫
            * 2 - 根据 Bartholomeus 等人 (2008) 的氧胁迫

        swoxygentype (Optional[Literal[1, 2]]): 用于计算氧胁迫的物理过程或繁殖功能开关

            * 1 - 物理过程
            * 2 - 繁殖功能

        swwrtnonox (Literal[0, 1]): 检查根区有氧条件以停止根（区）发育的开关
        aeratecrit (Optional[float]): 氧胁迫情况下停止根系延伸的阈值；0.0 表示最大氧胁迫
        hlim1 (Optional[float]): 较高压力水头下不吸水
        hlim2u (Optional[float]): 顶部土壤层最佳吸水开始的 H 值
        hlim2l (Optional[float]): 底部土壤层最佳吸水开始的 H 值
        q10_microbial (Optional[float]): 温度升高 10 摄氏度时微生物呼吸的相对增加量
        specific_resp_humus (Optional[float]): 25 摄氏度时腐殖质的呼吸速率
        srl (Optional[float]): 特定根长
        swrootradius (Optional[Literal[1, 2]]): 根半径计算开关

            * 1 - 计算根半径
            * 2 - 根半径在输入文件中给出

        dry_mat_cont_roots (Optional[float]): 根的干物质含量
        air_filled_root_por (Optional[float]): 根的充气孔隙度
        spec_weight_root_tissue (Optional[float]): 非充气根组织的比重
        var_a (Optional[float]): 根半径的方差
        root_radiuso2 (Optional[float]): 氧胁迫模块的根半径
        q10_root (Optional[float]): 温度升高 10 摄氏度时根呼吸的相对增加量
        f_senes (Optional[float]): 衰老折减系数，用于维持呼吸
        c_mroot (Optional[float]): 根的维持系数
        _table_max_resp_factor (Optional[_Table]): 根总呼吸/维持呼吸比率随发育阶段变化的表格
        _table_dvs_w_root_ss (Optional[_Table]): 根在土壤表面的干重随发育阶段变化的列表

    TODO: 找到一种方法来验证当 croptype=1 且 swoxygen=2 时所需的参数（目前我无法访问 croptype 参数）
    >> 在最后，当所有参数都可用时，将其移动到 Model 类验证中
    """

    swoxygen: _Literal[0, 1, 2] | None = None
    swwrtnonox: _Literal[0, 1] | None = None
    swoxygentype: _Literal[1, 2] | None = None
    aeratecrit: float | None = _Field(default=None, ge=0.0001, le=1.0)
    hlim1: float | None = _Field(default=None, ge=-100.0, le=100.0)
    hlim2u: float | None = _Field(default=None, ge=-1000.0, le=100.0)
    hlim2l: float | None = _Field(default=None, ge=-1000.0, le=100.0)
    q10_microbial: float | None = _Field(default=None, ge=1.0, le=4.0)
    specific_resp_humus: float | None = _Field(default=None, **_UNITRANGE)
    srl: float | None = _Field(default=None, ge=0.0, le=1.0e10)
    swrootradius: _Literal[1, 2] | None = None
    dry_mat_cont_roots: float | None = _Field(default=None, **_UNITRANGE)
    air_filled_root_por: float | None = _Field(default=None, **_UNITRANGE)
    spec_weight_root_tissue: float | None = _Field(default=None, ge=0.0, le=1.0e5)
    var_a: float | None = _Field(default=None, **_UNITRANGE)
    root_radiuso2: float | None = _Field(default=None, ge=1.0e-6, le=0.1)
    q10_root: float | None = _Field(default=None, ge=1.0, le=4.0)
    f_senes: float | None = _Field(default=None, **_UNITRANGE)
    c_mroot: float | None = _Field(default=None, **_UNITRANGE)
    mrftb: _Arrays | None = None
    wrtb: _Arrays | None = None


class DroughtStress(_PyAHCBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """用于 .crp 文件的干旱胁迫设置。

    属性：
        swdrought (Literal[1, 2]): 干旱胁迫开关

            * 1 - 根据 Feddes 等人 (1978) 的干旱胁迫
            * 2 - 根据 De Jong van Lier 等人 (2008) 的干旱胁迫

        swjarvis (Optional[Literal[0, 1, 2, 3, 4]]): _已弃用_ 用于减少吸水量的 Jarvis 模型开关
        alphcrit: Optional[float] = _已弃用_ 根系吸水补偿的临界胁迫指数 (Jarvis, 1989) [0.2..1 -, R]
        hlim3h (Optional[float]): 高 Tpot 时吸水减少开始的压力水头
        hlim3l (Optional[float]): 低 Tpot 时吸水减少开始的压力水头
        hlim4 (Optional[float]): 较低土壤水压力水头下不吸水
        adcrh (Optional[float]): 高大气需求水平，对应 HLIM3H
        adcrl (Optional[float]): 低大气需求水平，对应 HLIM3L
        wiltpoint (Optional[float]): 叶片中的最小压力水头
        kstem (Optional[float]): 叶片和根木质部之间的水力传导率
        rxylem (Optional[float]): 木质部半径
        rootradius (Optional[float]): 根半径
        kroot (Optional[float]): 根组织径向水力传导率
        rootcoefa (Optional[float]): 定义根之间发生平均土壤含水量的相对距离
        swhydrlift (Optional[Literal[0, 1]]): 根系水力提升可能性开关
        rooteff (Optional[float]): 根系效率因子
        stephr (Optional[float]): 迭代循环中 hroot 和 hxylem 值之间的步长
        criterhr (Optional[float]): 迭代之间 Hroot 的最大差异；收敛准则
        taccur (Optional[float]): 模拟和计算的潜在蒸腾速率之间的最大绝对差异
    """

    swdrought: _Literal[1, 2] | None = None
    swjarvis: _Literal[0, 1, 2, 3, 4] | None = None
    alphcrit: float | None = _Field(default=None, ge=0.2, le=1.0)
    hlim3h: float | None = _Field(default=None, ge=-1.0e4, le=100.0)
    hlim3l: float | None = _Field(default=None, ge=-1.0e4, le=100.0)
    hlim4: float | None = _Field(default=None, ge=-1.6e4, le=100.0)
    adcrh: float | None = _Field(default=None, ge=0.0, le=5.0)
    adcrl: float | None = _Field(default=None, ge=0.0, le=5.0)
    wiltpoint: float | None = _Field(default=None, ge=-1.0e8, le=-1.0e2)
    kstem: float | None = _Field(default=None, ge=1.0e-10, le=10.0)
    rxylem: float | None = _Field(default=None, ge=1.0e-4, le=1.0)
    rootradius: float | None = _Field(default=None, ge=1.0e-4, le=1.0)
    kroot: float | None = _Field(default=None, ge=1.0e-10, le=1.0e10)
    rootcoefa: float | None = _Field(default=None, **_UNITRANGE)
    swhydrlift: _Literal[0, 1] | None = None
    rooteff: float | None = _Field(default=None, **_UNITRANGE)
    stephr: float | None = _Field(default=None, ge=0.0, le=10.0)
    criterhr: float | None = _Field(default=None, ge=0.0, le=10.0)
    taccur: float | None = _Field(default=None, ge=1.0e-5, le=1.0e-2)


class SaltStress(_PyAHCBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """Salt stress settings for .crp file.

    Attributes:
        swsalinity (Literal[0, 1, 2]): Switch for salt stress

            * 0 - No salt stress
            * 1 - Maas and Hoffman reduction function
            * 2 - Use osmotic head

        saltmax (Optional[float]): Threshold salt concentration in soil water
        saltslope (Optional[float]): Decline of root water uptake above threshold
        salthead (Optional[float]): Conversion factor salt concentration (mg/cm3) into osmotic head (cm)
    """

    swsalinity: _Literal[0, 1, 2] | None = None
    saltmax: float | None = _Field(default=None, ge=0.0, le=100.0)
    saltslope: float | None = _Field(default=None, **_UNITRANGE)
    salthead: float | None = _Field(default=None, ge=0.0, le=1000.0)


class CompensateRWUStress(_PyAHCBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """用于 .crp 文件的根系吸水胁迫补偿设置。

    属性：
        swcompensate (Literal[0, 1, 2]): 根系吸水胁迫补偿开关

            * 0 - 无补偿
            * 1 - 根据 Jarvis (1989) 的补偿
            * 2 - 根据 Walsum (2019) 的补偿

        swstressor (Optional[Literal[1, 2, 3, 4, 5]]): 胁迫因子开关

            * 1 - 所有胁迫因子补偿
            * 2 - 干旱胁迫补偿
            * 3 - 氧胁迫补偿
            * 4 - 盐胁迫补偿
            * 5 - 霜冻胁迫补偿

        alphacrit (Optional[float]): 根系吸水补偿的临界胁迫指数
        dcritrtz (Optional[float]): 根区厚度阈值，超过该阈值后发生补偿
    """

    swcompensate: _Literal[0, 1, 2] | None = None
    swstressor: _Literal[1, 2, 3, 4, 5] | None = None
    alphacrit: float | None = _Field(default=None, ge=0.2, le=1.0)
    dcritrtz: float | None = _Field(default=None, ge=0.02, le=100.0)


class Interception(_PyAHCBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """用于 .crp 文件的截留设置。

    属性：
        swinter (Literal[0, 1, 2]): 降雨截留方法开关

            * 0 - 无截留
            * 1 - 农作物（Von Hoyningen-Hune 和 Braden）
            * 2 - 树木和森林（Gash）

        cofab (Optional[float]): 截留系数，对应最大截留量
        _table_intertb (Optional[_Table]): 包含以下列的表格，作为时间 T 的函数：

            * PFREE - 自由穿透系数
            * PSTEM - 茎流系数
            * SCANOPY - 冠层蓄水量系数
            * AVPREC = 平均降雨强度
            * AVEVAP = 湿冠层降雨期间的平均蒸发强度
    """

    swinter: _Literal[0, 1, 2] | None = None
    cofab: float | None = _Field(default=None, **_UNITRANGE)
    intertb: _Table | None = None


class CO2Correction(
    _PyAHCBaseModel, _SerializableMixin, _YAMLValidatorMixin, _WOFOSTUpdateMixin
):
    """WOFOST 类型 .crp 文件的 CO2 校正设置。

    属性：
        swco2 (Literal[0, 1]): CO2 影响引起的同化校正开关

            * 0 - 无 CO2 同化校正
            * 1 - CO2 同化校正

        atmofil (Optional[str]): atmosphere.co2 的备用文件名
        co2amaxtb (Optional _Arrays]): 光合作用随大气 CO2 浓度变化的校正
        co2efftb (Optional _Arrays]): 辐射利用效率随大气 CO2 浓度变化的校正
        co2tratb (Optional _Arrays]): 蒸腾作用随大气 CO2 浓度变化的校正
    """

    _validation: bool = _PrivateAttr(default=False)
    wofost_variety: _CropVariety | None = _Field(default=None, exclude=True)

    swco2: _Literal[0, 1] | None = None
    atmofil: str | None = None
    co2amaxtb: _Arrays | None = None
    co2efftb: _Arrays | None = None
    co2tratb: _Arrays | None = None


class Preparation(_PyAHCBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """用于 .crp 文件的准备、播种和发芽设置。

    属性：
        swprep (Literal[0, 1]): 准备开关
        swsow (Literal[0, 1]): 播种开关
        swgerm (Literal[0, 1, 2]): 发芽开关

            * 0 - 无发芽
            * 1 - 温度总和发芽
            * 2 - 温度总和和水势发芽

        swharv (Literal[0, 1]): 收获开关

            * 0 - 收获时间取决于生长期结束 (CROPEND)
            * 1 - 收获时间取决于发育阶段 (DVSEND)

        dvsend (Optional[float]): 收获时的发育阶段
        zprep (Optional[float]): 用于监测作物可操作性的 Z 水平
        hprep (Optional[float]): 准备期间的最大压力水头
        maxprepdelay (Optional[int]): 从生长期开始的最大准备延迟
        zsow (Optional[float]): 用于监测作物可操作性的 Z 水平
        hsow (Optional[float]): 播种期间的最大压力水头
        ztempsow (Optional[float]): 用于监测播种温度的 Z 水平
        tempsow (Optional[float]): 播种所需的土壤温度
        maxsowdelay (Optional[int]): 从生长期开始的最大播种延迟
        tsumemeopt (Optional[float]): 作物出苗所需的温度总和
        tbasem (Optional[float]): 最低温度，用于发芽轨迹
        teffmx (Optional[float]): 最高温度，用于发芽轨迹
        hdrygerm (Optional[float]): 干旱发芽轨迹的根区压力水头
        hwetgerm (Optional[float]): 湿润发芽轨迹的根区压力水头
        zgerm (Optional[float]): 用于监测平均压力水头的 Z 水平
        agerm (Optional[float]): A 系数 Eq. 24/25 Feddes & Van Wijk
    """

    swprep: _Literal[0, 1] | None = _Field(default=None)
    swsow: _Literal[0, 1] | None = None
    swgerm: _Literal[0, 1, 2] | None = None
    swharv: _Literal[0, 1] | None = None
    dvsend: float | None = _Field(default=None, ge=0.0, le=3.0)
    zprep: float | None = _Field(default=None, ge=-100.0, le=0.0)
    hprep: float | None = _Field(default=None, ge=-200.0, le=0.0)
    maxprepdelay: int | None = _Field(default=None, ge=1, le=366)
    zsow: float | None = _Field(default=None, ge=-100.0, le=0.0)
    hsow: float | None = _Field(default=None, ge=-200.0, le=0.0)
    ztempsow: float | None = _Field(default=None, ge=-100.0, le=0.0)
    tempsow: float | None = _Field(default=None, ge=0.0, le=30.0)
    maxsowdelay: int | None = _Field(default=None, ge=1, le=366)
    tsumemeopt: float | None = _Field(default=None, ge=0.0, le=1000.0)
    tbasem: float | None = _Field(default=None, ge=0.0, le=1000.0)
    teffmx: float | None = _Field(default=None, ge=0.0, le=1000.0)
    hdrygerm: float | None = _Field(default=None, ge=-1000.0, le=1000.0)
    hwetgerm: float | None = _Field(default=None, ge=-100.0, le=1000.0)
    zgerm: float | None = _Field(default=None, ge=-100.0, le=1000.0)
    agerm: float | None = _Field(default=None, ge=0.0, le=1000.0)


class GrasslandManagement(_PyAHCBaseModel, _SerializableMixin, _YAMLValidatorMixin):
    """动态草地生长模块的特定设置。

    属性：
        seqgrazmow (_IntList): 日历年内不同实践的时期序列。可用选项：

            * 1 - 放牧
            * 2 - 割草
            * 3 - 剪毛放牧

        swharvest (Literal[1, 2]): 收获时间开关，用于割草或放牧

            * 1 - 使用干物质阈值
            * 2 - 使用固定日期

        dateharvest Optional[(_DateList)]: 收获日期（最多 999 个）
        swdmgrz Optional[(Literal[1, 2])]: 用于触发放牧收获的干物质阈值开关

            * 1 - 使用固定阈值
            * 2 - 使用灵活阈值

        dmgrazing Optional[ _Arrays)]: 牛进入田地的最小干物质含量 [0..1d6 千克干物质/公顷, R]
        dmgrztb Optional[(int)]: 地上干物质阈值列表 [0..1d6 千克干物质/公顷, R]，用于触发放牧，作为天数 [1..366 天, R] 的函数
        maxdaygrz Optional[(int)]: 收获后的最大生长期 [1..366 -, I]
        swlossgrz Optional[(Literal[0, 1])]: 放牧期间因压力水头不足造成的损失开关

            * 0 - 无损失
            * 1 - 因踩踏造成的损失

        tagprest Optional[(float)]: 放牧后地上最小干物质含量 [0..1d6 千克干物质/公顷, R]
        dewrest Optional[(float)]: 剪毛事件后地上剩余产量 [0..1d6 千克干物质/公顷, R]
        _table_lsda (Optional[_Table]): 每个放牧期的实际牲畜密度
        _table_lsdb (Optional[_Table]): 牲畜密度、放牧天数和干物质吸收之间的关系
        swdmmow Optional[(int)]: 用于触发割草收获的干物质阈值开关

            * 1 - 使用固定阈值
            * 2 - 使用灵活阈值

        dmharvest Optional[(float)]: 触发割草的地上干物质阈值 [0..1d6 千克干物质/公顷, R]
        daylastharvest Optional[(int)]: 割草可能发生的最后日历日 [1..366 -, I]
        dmlastharvest Optional[(float)]: 最后日期割草的最小地上干物质 [0..1d6 千克干物质/公顷, R]
        dmmowtb Optional[(int)]: 干物质割草阈值
        maxdaymow Optional[(int)]: 收获后的最大生长期 [1..366 -, I]
        swlossmow Optional[(int)]: 割草期间因压力水头不足造成的损失开关

            * 0 - 无损失
            * 1 - 因踩踏造成的损失

        mowrest Optional[(float)]: 割草事件后地上剩余产量 [0..1d6 千克干物质/公顷, R]
        _table_dmmowdelay Optional[(Optional[_Table])]: 干物质收获 [0..1d6 千克/公顷, R] 与割草后再生长延迟天数 [0..366 天, I] 之间的关系
        swpotrelmf (int): 潜在产量计算开关

            * 1 - 理论潜在产量
            * 2 - 可达产量

        relmf (float): 相对管理因子，用于将理论潜在产量降低到可达产量 [0..1 -, R]
    """

    seqgrazmow: _IntList | None = None
    swharvest: _Literal[1, 2] | None = None
    dateharvest: _Arrays | None = None
    swdmgrz: _Literal[1, 2] | None = None
    dmgrazing: _Decimal2f | None = None
    dmgrztb: _Arrays | None = None
    maxdaygrz: int | None = None
    swlossgrz: _Literal[0, 1] | None = None
    tagprest: _Decimal2f | None = None
    dewrest: _Decimal2f | None = None
    lsda: _Table | None = None
    lsdb: _Table | None = None
    swdmmow: int | None = None
    dmharvest: _Decimal2f | None = None
    daylastharvest: int | None = None
    dmlastharvest: _Decimal2f | None = None
    dmmowtb: _Arrays | None = None
    maxdaymow: int | None = None
    swlossmow: int | None = None
    mowrest: _Decimal2f | None = None
    dmmowdelay: _Table | None = None
    swpotrelmf: int | None = None
    relmf: _Decimal2f | None = None


class CropFile(_PyAHCBaseModel, _FileMixin, _SerializableMixin):
    """ .crp 文件的主要类。

    此类收集作物文件的所有设置。目前属性的类型设置为 _Any，因为验证尚未实现。

    属性：
        name (str): 作物名称
        path (Optional[str]): .crp 文件的路径
        prep (Optional[Preparation]): 准备设置
        cropdev_settings (Optional[CropDevelopmentSettings]): 作物发育设置
        oxygenstress (Optional[OxygenStress]): 氧胁迫设置
        droughtstress (Optional[DroughtStress]): 干旱胁迫设置
        saltstress (Optional[SaltStress]): 盐胁迫设置
        compensaterwu (Optional[CompensateRWUStress]): 根系吸水胁迫补偿设置
        interception (Optional[Interception]): 截留设置
        scheduledirrigation (Optional[ScheduledIrrigation]): 计划灌溉设置
        grassland_management (Optional[GrasslandManagement]): 草地管理设置

        # 新增可配置参数
        crop_name (Optional[str]): 作物名称，默认为 "Maize"
        crop_type (Optional[int]): 作物模型类型，默认为 2 (EPIC)
        capfil (Optional[str]): 灌溉参数文件名，默认为空
        sowing_day (Optional[int]): 播种日，默认为 2
        sowing_month (Optional[int]): 播种月，默认为 5
        end_day (Optional[int]): 作物生长结束日，默认为 21
        end_month (Optional[int]): 作物生长结束月，默认为 9
        start_sch_day (Optional[int]): 灌溉调度开始日，默认为 8
        start_sch_month (Optional[int]): 灌溉调度开始月，默认为 10
        swdorm (Optional[int]): 休眠期开关，默认为 0
        dormancy_start_day (Optional[int]): 休眠期开始日，默认为 29
        dormancy_start_month (Optional[int]): 休眠期开始月，默认为 12
        dormancy_start_year (Optional[int]): 休眠期开始年，默认为 2010
        dormancy_end_day (Optional[int]): 休眠期结束日，默认为 25
        dormancy_end_month (Optional[int]): 休眠期结束月，默认为 2
        dormancy_end_year (Optional[int]): 休眠期结束年，默认为 2011
        swmulch (Optional[int]): 覆盖开关，默认为 1
        mulch_start_day (Optional[int]): 覆盖开始日，默认为 4
        mulch_start_month (Optional[int]): 覆盖开始月，默认为 5
        mulch_start_year (Optional[int]): 覆盖开始年，默认为 2013
        mulch_end_day (Optional[int]): 覆盖结束日，默认为 25
        mulch_end_month (Optional[int]): 覆盖结束月，默认为 9
        mulch_end_year (Optional[int]): 覆盖结束年，默认为 2013
        fmat (Optional[float]): 覆盖材料因子，默认为 1.0
        mfrac (Optional[float]): 覆盖分数，默认为 0.5
    """

    _extension: bool = _PrivateAttr(default="crp")

    name: str = _Field(exclude=True)
    path: str | None = None
    prep: _Subsection[Preparation] | None = None
    cropdev_settings: (
        _Subsection[
            CropDevelopmentSettingsFixed
            | CropDevelopmentSettingsWOFOST
            | CropDevelopmentSettingsGrass
        ]
        | None
    ) = None
    oxygenstress: _Subsection[OxygenStress] | None = None
    droughtstress: _Subsection[DroughtStress] | None = None
    saltstress: _Subsection[SaltStress] | None = SaltStress(swsalinity=0)
    compensaterwu: _Subsection[CompensateRWUStress] | None = CompensateRWUStress(
        swcompensate=0
    )
    interception: _Subsection[Interception] | None = None
    scheduledirrigation: _Subsection[_ScheduledIrrigation] | None = (
        _ScheduledIrrigation(schedule=0)
    )
    grasslandmanagement: _Subsection[GrasslandManagement] | None = None
    co2correction: _Subsection[CO2Correction] | None = None

    # 新增可配置参数
    crop_name: str | None = _Field(default="Maize")
    crop_type: int | None = _Field(default=2, ge=1, le=3)
    capfil: str | None = _Field(default="")
    sowing_day: int | None = _Field(default=2, ge=1, le=31)
    sowing_month: int | None = _Field(default=5, ge=1, le=12)
    end_day: int | None = _Field(default=21, ge=1, le=31)
    end_month: int | None = _Field(default=9, ge=1, le=12)
    start_sch_day: int | None = _Field(default=8, ge=1, le=31)
    start_sch_month: int | None = _Field(default=10, ge=1, le=12)
    swdorm: int | None = _Field(default=0, ge=0, le=1)
    dormancy_start_day: int | None = _Field(default=29, ge=1, le=31)
    dormancy_start_month: int | None = _Field(default=12, ge=1, le=12)
    dormancy_start_year: int | None = _Field(default=2010, ge=1900, le=2100)
    dormancy_end_day: int | None = _Field(default=25, ge=1, le=31)
    dormancy_end_month: int | None = _Field(default=2, ge=1, le=12)
    dormancy_end_year: int | None = _Field(default=2011, ge=1900, le=2100)
    swmulch: int | None = _Field(default=1, ge=0, le=1)
    mulch_start_day: int | None = _Field(default=4, ge=1, le=31)
    mulch_start_month: int | None = _Field(default=5, ge=1, le=12)
    mulch_start_year: int | None = _Field(default=2013, ge=1900, le=2100)
    mulch_end_day: int | None = _Field(default=25, ge=1, le=31)
    mulch_end_month: int | None = _Field(default=9, ge=1, le=12)
    mulch_end_year: int | None = _Field(default=2013, ge=1900, le=2100)
    fmat: float | None = _Field(default=1.0, ge=0.5, le=1.0)
    mfrac: float | None = _Field(default=0.5, ge=0.0, le=1.0)

    def _get_parameter_value(self, param_name: str, kwargs: _Dict[str, _Any], default_value: _Any = None) -> _Any:
        """获取参数值的辅助方法，优先从kwargs获取，然后从实例属性获取，最后使用默认值"""
        return kwargs.get(param_name, getattr(self, param_name) or default_value)

    def _get_parameter_with_none_check(self, param_name: str, kwargs: _Dict[str, _Any], default_value: _Any = None) -> _Any:
        """获取参数值的辅助方法，支持None值检查"""
        attr_value = getattr(self, param_name)
        return kwargs.get(param_name, attr_value if attr_value is not None else default_value)

    def model_string(self, **kwargs) -> str:
        """生成 .CAL 文件格式的字符串。

        覆盖默认的 SerializableMixin.model_string 方法，
        生成符合 AHC V201 要求的作物日历文件格式。

        Args:
            **kwargs: 可选参数，用于覆盖默认值
        """
        lines = []

        # 使用辅助方法获取参数值
        crop_name = self._get_parameter_value('crop_name', kwargs, "Maize")
        crop_type = self._get_parameter_value('crop_type', kwargs, 2)
        capfil = self._get_parameter_value('capfil', kwargs, "")
        sowing_day = self._get_parameter_value('sowing_day', kwargs, 2)
        sowing_month = self._get_parameter_value('sowing_month', kwargs, 5)
        end_day = self._get_parameter_value('end_day', kwargs, 21)
        end_month = self._get_parameter_value('end_month', kwargs, 9)
        start_sch_day = self._get_parameter_value('start_sch_day', kwargs, 8)
        start_sch_month = self._get_parameter_value('start_sch_month', kwargs, 10)
        swdorm = self._get_parameter_with_none_check('swdorm', kwargs, 0)
        dormancy_start_day = self._get_parameter_value('dormancy_start_day', kwargs, 29)
        dormancy_start_month = self._get_parameter_value('dormancy_start_month', kwargs, 12)
        dormancy_start_year = self._get_parameter_value('dormancy_start_year', kwargs, 2010)
        dormancy_end_day = self._get_parameter_value('dormancy_end_day', kwargs, 25)
        dormancy_end_month = self._get_parameter_value('dormancy_end_month', kwargs, 2)
        dormancy_end_year = self._get_parameter_value('dormancy_end_year', kwargs, 2011)
        swmulch = self._get_parameter_with_none_check('swmulch', kwargs, 1)
        mulch_start_day = self._get_parameter_value('mulch_start_day', kwargs, 4)
        mulch_start_month = self._get_parameter_value('mulch_start_month', kwargs, 5)
        mulch_start_year = self._get_parameter_value('mulch_start_year', kwargs, 2013)
        mulch_end_day = self._get_parameter_value('mulch_end_day', kwargs, 25)
        mulch_end_month = self._get_parameter_value('mulch_end_month', kwargs, 9)
        mulch_end_year = self._get_parameter_value('mulch_end_year', kwargs, 2013)
        fmat = self._get_parameter_with_none_check('fmat', kwargs, 1.0)
        mfrac = self._get_parameter_with_none_check('mfrac', kwargs, 0.5)

        # 文件头部
        lines.extend([
            "********************************************************************************",
            f"* Filename: {self.name}.CAL",
            "* Contents: AHC 1.0  - crop calendar   ",
            "********************************************************************************",
            "*c Rotation scheme ",
            "********************************************************************************",
            "",
            "********************************************************************************",
            "* Specify for each crop (maximum 3):",
            "*",
            "* col#1 CRPFIL = Crop data input file without .CRP extension, [A8]",
            "* col#2 Type   = Type of crop model: Simple=1, EPIC=2, WOFOST=3",
            "* col#3 CAPFIL = Input file with calculated irrication parameters",
            "*             without .CAP extension [A8]",
            "* col#4 IF Type=1-3, EMERGENCE = Emergence date of the crop;",
            "*       IF Type=4,   Sowing    = Sowing date of the crop",
            "* col#5 END_crop  = Forced end of crop growth",
            "* col#6 START_sch = Start of irrigation scheduling period",
            "*",
            "**********************************************************************************",
            "* Section 1：Planting and irrigation date",
            "* CRPFIL  Type        CAPFIL   Sowing    END_crop    START_sch  ",
            "*                              d1 m1     d2 m2       d3 m3  ",
            f"'{crop_name}'   {crop_type}  '{capfil}'  {sowing_day}  {sowing_month}     {end_day}  {end_month}     {start_sch_day}  {start_sch_month}",
            "* End of table",
            "*********************************************************************************",
            "",
            "*********************************************************************************",
            "* Section 2：Domancy period",
            f"  SWDORM = {swdorm} !   Switch, if considering the dormancy [Y=1, N=0] ",
            "* If SWDORM = 1 specify:  Start and end date",
            "* dd  mm  YY        dd  mm  YY  ",
            f"   {dormancy_start_day:2d}  {dormancy_start_month:2d}  {dormancy_start_year}        {dormancy_end_day:2d}  {dormancy_end_month:02d}  {dormancy_end_year}",
            "************************************************************************************************",
            "",
            "************************************************************************************************",
            "* Section 3：Mulching",
            f"  SWMULCH = {swmulch} !   Switch, mulch effect [Y=1, N=0]",
            "* If SWMULCH=1 then specify: Start and end date, fmat (mulch material factor) [0.5-1.0, R] and",
            "*                           mfrac (mulch fraction) [0-1.0,R]",
            "* dd  mm  YY     dd  mm  YY     fmat  mfrac",
            f" {mulch_start_day}    {mulch_start_month}    {mulch_start_year}  {mulch_end_day}   {mulch_end_month:02d}   {mulch_end_year}  {fmat}  {mfrac}   ",
            "*********************************************************************************",
            "* End of file *******************************************************************",
            "***",
            "*"
        ])

        return "\n".join(lines)

    @property
    def crp(self) -> str:
        """Return the model string of the .crp file."""
        return self.model_string()


class Crop(_PyAHCBaseModel, _SerializableMixin, _FileMixin, _YAMLValidatorMixin):
    """重构后的作物组件 - 支持外部参数配置和验证。

    Attributes:
        swcrop (int): Switch for crop:

            * 0 - Bare soil.
            * 1 - Simulate crop.

        rds (Optional[float]): Rooting depth of the crop [cm].
        croprotation (Optional[_Table]): _Table with crop rotation data.
        cropfiles (Optional[List[CropFile]]): List of crop files.

        # 新增作物参数（外部化配置）
        crop_type (Optional[int]): 作物类型选择 (1=一年生, 2=多年生, 3=草地)
        crop_name (Optional[str]): 作物名称
        emergence_date (Optional[List[int]]): 出苗日期 [日, 月]
        harvest_date (Optional[List[int]]): 收获日期 [日, 月]
        max_rooting_depth (Optional[float]): 最大根深度 [cm]
        base_temperature (Optional[float]): 基础温度 [°C]
        optimum_temperature (Optional[float]): 最适温度 [°C]
        maximum_temperature (Optional[float]): 最高温度 [°C]
        growth_coefficient (Optional[float]): 生长系数
        yield_factor (Optional[float]): 产量因子
        water_stress_factor (Optional[float]): 水分胁迫因子
        root_distribution (Optional[List[float]]): 根系分布
        root_growth_rate (Optional[float]): 根系生长速率
        leaf_area_index (Optional[float]): 叶面积指数
        canopy_height (Optional[float]): 冠层高度 [cm]

    Methods:
        write_crop: Write the crop files.
        validate_crop_parameters: 验证作物参数
        validate_crop_type_parameters: 验证作物类型相关参数
        validate_growth_parameters: 验证生长参数的合理性
        from_crop_database: 从作物数据库创建实例
        from_config: 从外部配置创建实例
    """

    # 原有参数（保持向后兼容）
    swcrop: _Literal[0, 1, None] = None
    rds: float | None = _Field(default=None, ge=1, le=5000)
    croprotation: _Table | None = None
    cropfiles: dict[str, CropFile] = _Field(default_factory=dict, exclude=True)

    # 新增作物参数（外部化配置）
    crop_type: _Optional[_Literal[1, 2, 3]] = _Field(default=None, description="作物类型选择")
    crop_name: _Optional[str] = _Field(default=None, description="作物名称")

    # 生长参数（移除硬编码）
    emergence_date: _Optional[_List[int]] = _Field(default=None, description="出苗日期 [日, 月]")
    harvest_date: _Optional[_List[int]] = _Field(default=None, description="收获日期 [日, 月]")
    max_rooting_depth: _Optional[_PositiveFloat] = _Field(default=None, description="最大根深度")

    # 生理参数（外部化配置）
    base_temperature: _Optional[_TemperatureFloat] = _Field(default=None, description="基础温度")
    optimum_temperature: _Optional[_TemperatureFloat] = _Field(default=None, description="最适温度")
    maximum_temperature: _Optional[_TemperatureFloat] = _Field(default=None, description="最高温度")

    # 生长系数（外部化配置）
    growth_coefficient: _Optional[_PositiveFloat] = _Field(default=None, description="生长系数")
    yield_factor: _Optional[_UnitFloat] = _Field(default=None, description="产量因子")
    water_stress_factor: _Optional[_UnitFloat] = _Field(default=None, description="水分胁迫因子")

    # 根系参数（外部化配置）
    root_distribution: _Optional[_List[float]] = _Field(default=None, description="根系分布")
    root_growth_rate: _Optional[_PositiveFloat] = _Field(default=None, description="根系生长速率")

    # 叶面积参数（外部化配置）
    leaf_area_index: _Optional[_PositiveFloat] = _Field(default=None, description="叶面积指数")
    canopy_height: _Optional[_PositiveFloat] = _Field(default=None, description="冠层高度")

    @_model_validator(mode="after")
    def validate_crop_parameters(self):
        """验证作物参数"""
        if not self._validation:
            return self

        # 验证温度参数的合理性
        if all(temp is not None for temp in [self.base_temperature, self.optimum_temperature, self.maximum_temperature]):
            if not (self.base_temperature < self.optimum_temperature < self.maximum_temperature):
                raise ValueError("Temperature parameters must satisfy: base < optimum < maximum")

        # 验证日期参数
        if self.emergence_date and len(self.emergence_date) != 2:
            raise ValueError("Emergence date must have 2 elements [day, month]")

        if self.harvest_date and len(self.harvest_date) != 2:
            raise ValueError("Harvest date must have 2 elements [day, month]")

        # 验证日期顺序（如果都提供）
        if self.emergence_date and self.harvest_date:
            emergence_day_of_year = self.emergence_date[1] * 30 + self.emergence_date[0]
            harvest_day_of_year = self.harvest_date[1] * 30 + self.harvest_date[0]

            if emergence_day_of_year >= harvest_day_of_year:
                raise ValueError("Emergence date must be before harvest date")

        return self

    @_model_validator(mode="after")
    def validate_crop_type_parameters(self):
        """验证作物类型相关参数"""
        if not self._validation:
            return self

        if self.crop_type == 1:  # 一年生作物
            required = ['emergence_date', 'harvest_date']
            missing = [p for p in required if getattr(self, p) is None]
            if missing:
                raise ValueError(f"crop_type=1 (annual crop) requires: {missing}")

        if self.crop_type == 2:  # 多年生作物
            if self.max_rooting_depth is None:
                raise ValueError("crop_type=2 (perennial crop) requires max_rooting_depth")

        if self.crop_type == 3:  # 草地/牧草
            if self.leaf_area_index is None:
                raise ValueError("crop_type=3 (grassland) requires leaf_area_index")

        return self

    @_model_validator(mode="after")
    def validate_growth_parameters(self):
        """验证生长参数的合理性"""
        if not self._validation:
            return self

        # 验证根深度合理性
        if self.max_rooting_depth is not None:
            if self.max_rooting_depth > MAX_ROOTING_DEPTH:
                raise ValueError(f"Max rooting depth seems unreasonable: {self.max_rooting_depth} cm, max allowed: {MAX_ROOTING_DEPTH} cm")

        # 验证冠层高度合理性
        if self.canopy_height is not None:
            if self.canopy_height > MAX_CANOPY_HEIGHT:
                raise ValueError(f"Canopy height seems unreasonable: {self.canopy_height} cm, max allowed: {MAX_CANOPY_HEIGHT} cm")

        # 验证叶面积指数合理性
        if self.leaf_area_index is not None:
            if self.leaf_area_index > MAX_LEAF_AREA_INDEX:
                raise ValueError(f"Leaf area index seems unreasonable: {self.leaf_area_index}, max allowed: {MAX_LEAF_AREA_INDEX}")

        return self

    @classmethod
    def from_crop_database(cls, crop_name: str, config_overrides: _Optional[_Dict[str, _Any]] = None) -> "Crop":
        """从作物数据库创建实例"""
        # 作物数据库（简化版本）
        crop_database = {
            'corn': {
                'crop_type': 1,
                'crop_name': 'Corn',
                'emergence_date': [15, 5],  # 5月15日
                'harvest_date': [30, 9],    # 9月30日
                'max_rooting_depth': 120.0,
                'base_temperature': 8.0,
                'optimum_temperature': 25.0,
                'maximum_temperature': 35.0,
                'growth_coefficient': 1.2,
                'yield_factor': 0.8,
                'leaf_area_index': 4.0,
                'canopy_height': 250.0
            },
            'wheat': {
                'crop_type': 1,
                'crop_name': 'Wheat',
                'emergence_date': [1, 4],   # 4月1日
                'harvest_date': [15, 7],    # 7月15日
                'max_rooting_depth': 100.0,
                'base_temperature': 5.0,
                'optimum_temperature': 20.0,
                'maximum_temperature': 30.0,
                'growth_coefficient': 1.0,
                'yield_factor': 0.9,
                'leaf_area_index': 3.5,
                'canopy_height': 80.0
            },
            'grass': {
                'crop_type': 3,
                'crop_name': 'Grass',
                'max_rooting_depth': 50.0,
                'base_temperature': 5.0,
                'optimum_temperature': 18.0,
                'maximum_temperature': 25.0,
                'growth_coefficient': 0.8,
                'yield_factor': 0.7,
                'leaf_area_index': 2.5,
                'canopy_height': 30.0
            }
        }

        if crop_name.lower() not in crop_database:
            raise ValueError(f"Crop '{crop_name}' not found in database. Available crops: {list(crop_database.keys())}")

        base_params = crop_database[crop_name.lower()].copy()

        if config_overrides:
            base_params.update(config_overrides)

        return cls(**base_params)

    @classmethod
    def from_config(cls, config: _Dict[str, _Any]) -> "Crop":
        """从外部配置创建实例"""
        return cls(**config)

    def write_crop(self, path: str, ctr_file=None):
        """Write the crop files with dynamic naming based on CtrFile parameters.
        
        Args:
            path (str): Directory path to write the files
            ctr_file: CtrFile instance containing CALFIL parameter
        """
        if ctr_file and hasattr(ctr_file, 'calfil'):
            # 从CtrFile获取CALFIL参数
            calfil = ctr_file.calfil
            
            # 生成动态文件名：{calfil}.CAL
            dynamic_filename = f"{calfil}.CAL"
            
            # 如果cropfiles中有文件，使用动态文件名写入第一个文件
            if self.cropfiles:
                first_cropfile = next(iter(self.cropfiles.values()))
                # 直接写入.CAL文件，不使用CropFile的扩展名
                with open(f"{path}/{dynamic_filename}", "w", encoding="utf-8") as f:
                    f.write(first_cropfile.crp)
            
            # 为其他cropfiles保持原有的命名方式
            for name, cropfile in self.cropfiles.items():
                if name != dynamic_filename:  # 避免重复写入
                    cropfile.save_file(string=cropfile.crp, fname=name, path=path)
        else:
            # 如果没有CtrFile信息，使用原有的命名方式
            for name, cropfile in self.cropfiles.items():
                cropfile.save_file(string=cropfile.crp, fname=name, path=path)

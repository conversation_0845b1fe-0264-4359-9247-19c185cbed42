"""ProjectHDF5Manager使用示例

本示例演示如何使用ProjectHDF5Manager进行项目数据管理，
包括项目创建、日数据保存/加载、状态传递等功能。
"""

import numpy as np
import logging
from pathlib import Path
from datetime import date, datetime, timedelta
from typing import Dict, Any

import sys
sys.path.insert(0, '.')
from pyahc.db.hdf5 import ProjectHDF5Manager
# from pyahc.model.model import Model
# from pyahc.model.result import Result

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_sample_model() -> Model:
    """创建示例模型对象"""
    # 这里应该创建一个真实的Model对象
    # 为了示例，我们创建一个简化的模拟对象
    class MockModel:
        def __init__(self):
            self.name = "AHC_Model"
            self.version = "1.0"
            self.parameters = {
                'field_capacity': 0.35,
                'wilting_point': 0.15,
                'k_sat': 10.0
            }
    
    return MockModel()


def create_sample_result() -> Result:
    """创建示例结果对象"""
    # 这里应该创建一个真实的Result对象
    # 为了示例，我们创建一个简化的模拟对象
    class MockResult:
        def __init__(self):
            self.output = {
                'csv': None,  # 实际应该是DataFrame
                'log': "Model run completed successfully"
            }
            self.warning = []
    
    return MockResult()


def generate_sample_states(day_of_year: int) -> Dict[str, Any]:
    """生成示例状态变量（模拟季节变化）"""
    # 模拟土壤含水量剖面（4层）
    base_moisture = 0.25 + 0.1 * np.sin(day_of_year * 2 * np.pi / 365)
    soil_moisture = np.array([
        base_moisture + 0.05,
        base_moisture,
        base_moisture - 0.02,
        base_moisture - 0.05
    ])
    
    # 模拟作物LAI（叶面积指数）
    if day_of_year < 120:  # 播种前
        lai = 0.0
    elif day_of_year < 200:  # 生长期
        lai = 4.0 * (day_of_year - 120) / 80
    elif day_of_year < 280:  # 成熟期
        lai = 4.0 - 2.0 * (day_of_year - 200) / 80
    else:  # 收获后
        lai = 0.0
    
    # 模拟生物量
    if day_of_year < 120:
        biomass = 0.0
    elif day_of_year < 280:
        biomass = 8000.0 * (day_of_year - 120) / 160
    else:
        biomass = 8000.0
    
    return {
        'soil_moisture': soil_moisture,
        'lai': max(0.0, lai),
        'biomass': max(0.0, biomass),
        'root_depth': min(100.0, max(0.0, (day_of_year - 120) * 0.5)),
        'groundwater_level': 150.0 + 20.0 * np.sin(day_of_year * 2 * np.pi / 365)
    }


def example_basic_usage():
    """基本使用示例"""
    logger.info("=== 基本使用示例 ===")
    
    # 1. 创建HDF5管理器
    hdf5_path = Path("example_project.h5")
    
    with ProjectHDF5Manager(hdf5_path, mode='w') as manager:
        # 2. 创建项目
        project_name = "corn_field_001-2013"
        metadata = {
            'field_id': 'field_001',
            'crop_type': 'corn',
            'year': 2013,
            'latitude': 45.75,
            'longitude': -93.48,
            'altitude': 1039.3,
            'created_at': datetime.now(),
            'description': '玉米田块001的2013年模拟',
            'simulation_config': {
                'start_date': date(2013, 5, 1),
                'end_date': date(2013, 10, 31),
                'enable_assimilation': False,
                'time_step': 'daily'
            }
        }
        
        logger.info(f"创建项目: {project_name}")
        manager.create_project(project_name, metadata)
        
        # 3. 验证项目创建
        projects = manager.list_projects()
        logger.info(f"项目列表: {projects}")
        
        # 4. 获取项目元数据
        loaded_metadata = manager.get_project_metadata(project_name)
        logger.info(f"项目元数据: {loaded_metadata['field_id']}, {loaded_metadata['crop_type']}")
        
        # 5. 保存单日数据
        test_date = date(2013, 6, 15)
        model = create_sample_model()
        result = create_sample_result()
        states = generate_sample_states(test_date.timetuple().tm_yday)
        parameters = {
            'irrigation_amount': 25.0,
            'fertilizer_n': 150.0
        }
        
        logger.info(f"保存日数据: {test_date}")
        manager.save_daily_data(project_name, test_date, model, result, states, parameters)
        
        # 6. 加载日数据
        logger.info(f"加载日数据: {test_date}")
        loaded_data = manager.load_daily_data(project_name, test_date)
        
        logger.info(f"加载的状态变量: {list(loaded_data['states'].keys())}")
        logger.info(f"加载的参数变量: {list(loaded_data['parameters'].keys())}")
        logger.info(f"土壤含水量: {loaded_data['states']['soil_moisture']}")


def example_seasonal_simulation():
    """季节性模拟示例"""
    logger.info("=== 季节性模拟示例 ===")
    
    hdf5_path = Path("seasonal_simulation.h5")
    
    with ProjectHDF5Manager(hdf5_path, mode='w') as manager:
        project_name = "corn_seasonal_2013"
        metadata = {
            'field_id': 'field_002',
            'crop_type': 'corn',
            'year': 2013,
            'simulation_type': 'seasonal',
            'description': '完整季节模拟示例'
        }
        
        manager.create_project(project_name, metadata)
        
        # 模拟整个生长季节（5月1日到10月31日）
        start_date = date(2013, 5, 1)
        end_date = date(2013, 10, 31)
        current_date = start_date
        
        logger.info(f"开始季节模拟: {start_date} 到 {end_date}")
        
        day_count = 0
        while current_date <= end_date:
            # 创建当日的模型和结果
            model = create_sample_model()
            result = create_sample_result()
            
            # 生成当日状态变量
            day_of_year = current_date.timetuple().tm_yday
            states = generate_sample_states(day_of_year)
            
            # 模拟管理措施参数
            parameters = {}
            if current_date.month == 5 and current_date.day == 15:  # 播种
                parameters['seeding_rate'] = 75000  # 株/公顷
            elif current_date.month == 6 and current_date.day == 1:  # 施肥
                parameters['fertilizer_n'] = 150.0  # kg/ha
            elif current_date.month == 7 and current_date.day == 15:  # 灌溉
                parameters['irrigation_amount'] = 50.0  # mm
            
            # 保存日数据
            manager.save_daily_data(project_name, current_date, model, result, states, parameters)
            
            day_count += 1
            if day_count % 30 == 0:  # 每30天报告一次进度
                logger.info(f"已处理 {day_count} 天，当前日期: {current_date}")
            
            current_date += timedelta(days=1)
        
        logger.info(f"季节模拟完成，共处理 {day_count} 天")
        
        # 验证数据完整性
        test_dates = [date(2013, 6, 1), date(2013, 7, 15), date(2013, 9, 1)]
        for test_date in test_dates:
            try:
                data = manager.load_daily_data(project_name, test_date)
                lai = data['states']['lai']
                biomass = data['states']['biomass']
                logger.info(f"{test_date}: LAI={lai:.2f}, 生物量={biomass:.1f} kg/ha")
            except Exception as e:
                logger.error(f"加载 {test_date} 数据失败: {e}")


def example_multiple_projects():
    """多项目管理示例"""
    logger.info("=== 多项目管理示例 ===")
    
    hdf5_path = Path("multiple_projects.h5")
    
    with ProjectHDF5Manager(hdf5_path, mode='w') as manager:
        # 创建多个项目
        projects_config = [
            {'name': 'corn_field_001-2013', 'crop': 'corn', 'field': 'field_001'},
            {'name': 'soybean_field_002-2013', 'crop': 'soybean', 'field': 'field_002'},
            {'name': 'wheat_field_003-2013', 'crop': 'wheat', 'field': 'field_003'}
        ]
        
        for config in projects_config:
            metadata = {
                'field_id': config['field'],
                'crop_type': config['crop'],
                'year': 2013,
                'created_at': datetime.now()
            }
            
            logger.info(f"创建项目: {config['name']}")
            manager.create_project(config['name'], metadata)
            
            # 为每个项目保存一些示例数据
            test_date = date(2013, 7, 1)
            model = create_sample_model()
            result = create_sample_result()
            states = generate_sample_states(test_date.timetuple().tm_yday)
            
            manager.save_daily_data(config['name'], test_date, model, result, states)
        
        # 列出所有项目
        all_projects = manager.list_projects()
        logger.info(f"所有项目: {all_projects}")
        
        # 检查每个项目的元数据
        for project_name in all_projects:
            metadata = manager.get_project_metadata(project_name)
            logger.info(f"项目 {project_name}: {metadata['crop_type']} 在 {metadata['field_id']}")


def main():
    """主函数"""
    logger.info("ProjectHDF5Manager 使用示例开始")
    
    try:
        # 运行各种示例
        example_basic_usage()
        example_seasonal_simulation()
        example_multiple_projects()
        
        logger.info("所有示例运行完成")
        
    except Exception as e:
        logger.error(f"示例运行失败: {e}")
        raise
    
    finally:
        # 清理示例文件
        example_files = [
            "example_project.h5",
            "seasonal_simulation.h5", 
            "multiple_projects.h5"
        ]
        
        for file_path in example_files:
            path = Path(file_path)
            if path.exists():
                path.unlink()
                logger.info(f"清理文件: {file_path}")


if __name__ == "__main__":
    main()

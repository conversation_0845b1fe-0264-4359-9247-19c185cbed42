***********************************************************************************************
* Filename: huinong.DRB
* Contents: AHC 1.0 - Basic lateral drainage 
***********************************************************************************************

***********************************************************************************************
* Section 1: Method
*
  DRAMET = 2 ! Method to establish drainage fluxes
*              1 = Use table of drainage flux - groundwater level relation
*              2 = Use drainage formula of Hooghoudt or Ernst
*              3 = Use drainage/infiltration resistance, multi-level if needed
***********************************************************************************************

***********************************************************************************************
* Section 2: Table of drainage flux - groundwater level relation
*
* In case drainage fluxes should be distributed vertically in the saturated
* zone (SWDIVD = 1 in *.SWA), specify the dist. L between the drainage canals:
*
  LM1 =    ! Drain spacing, [1..1E5 m, R]
*
* Specify drainage flux [-10..10 cm/d, R] as function of groundwater level
* [-1.E5..100 cm, R]; start with the highest groundwaterlevel:
* GWL      Q        (maximum 25 records)
* End of table
***********************************************************************************************

***********************************************************************************************
* Section 3: Drainage formula of Hooghoudt or Ernst
*
  IPOS = 2  ! Position of the drain
*             1 = On top of impervious layer in a homogeneous profile
*             2 = Above impervious layer in a homogeneous profile
*             3 = At interface of a fine upper and a coarse lower layers
*             4 = In the lower, more coarse soil layer
*             5 = In the upper, more fine soil layer
*
* Of the next parameters, always specify BASEGW and KHTOP,
*                         KHBOT and ZINTF in case IPOS = 3, 4 or 5,
*                         KVTOP and KVBOT incase IPOS = 4 or 5,
*                         GEOFAC in case IPOS = 5
 BASEGW = -4500.00 ! Level of impermeable layer, [-1.E4..0 cm, R, neg. bleow soil surf.]
 KHTOP  = 580.00 ! Horizontal hydraulic conductivity top layer    [0..1000 cm/d, R]
 KHBOT  =  ! Horizontal hydraulic conductivity bottom layer [0..1000 cm/d, R]
 KVTOP  =  ! Vertical hydraulic conductivity top layer      [0..1000 cm/d, R]
 KVBOT  =  ! Vertical hydr. conductivity bottom layer       [0..1000 cm/d, R]
 GEOFAC =  ! Geometry factor Ernst, [0..100 -, R]
 ZINTF  =  ! Level of interface of fine and caorse soil layer [-1.E4..0 cm, R]
*
* Always specify:
 LM2    = 80.00 ! Drain spacing, [1..1.E5 m, R]
 WETPER = 810.00 ! Wet perimeter of the drain, [0..1000 cm, r]
 ZBOTDR = -120.00 ! Level of drain bottom, [-1000..0 cm, R, neg. below soil surface]
 ENTRES = 100.00 ! Drain entry resistance, [0..1000 d, R]
***********************************************************************************************

***********************************************************************************************
* Section 4: Drainage and infiltration resistance
*
 NRLEVS =1 ! Number of drainage levels [1..5, I]
***********************************************************************************************

***********************************************************************************************
* Section_4a: Drainage to level 1
*
 DRARES1 =  ! Drainage resistance [0..1.E5 d, R]
 INFRES1 =  ! Infiltration resistance [0..1.E5 d, R]
 SWALLO1 = 1 ! Switch, for allowance of drainage/infiltration
*                      1 = Drainage and infiltration are both allowed,
*                      2 = Drainage is not allowed,
*                      3 = Infiltration is not allowed.
*
* In case drainage fluxes should distributed vertically in the saturated zone
* zone (SWDIVD = 1 in *.SWA), specify the distance L between the drainage canals:
*
  L1 =  ! Drain spacing, [1..1E5 m, R]
*
  ZBOTDR1 =  ! Level of drain tube bottom [-1000..0 cm, R, neg. below the soil surface]
  SWDTYP1 = 1 ! Type of drainage medium:
*               1 = Drain tube
*               2 = Open Channel
*
* In case SWDTYP1 = 2, specify date [day month] and channel water level
* [-1.E5..100 cm, R], maximum 366 records:
* dd mm LEVEL1
* End of table
***********************************************************************************************
* Section_4b: Drainage to level 2
*
 DRARES2 =   ! Drainage resistance [0..1.E5 d, R]
 INFRES2 =   ! Infiltration resistance [0..1.E5 d, R]
 SWALLO2 = 1 ! Switch, for allowance of drainage/infiltration
*                      1 = Drainage and infiltration are both allowed,
*                      2 = Drainage is not allowed,
*                      3 = Infiltration is not allowed.
*
* In case drainage fluxes should distributed vertically in the saturated zone
* zone (SWDIVD = 1 in *.SWA), specify the distance L between the drainage canals:
*
  L2 =  ! Drain spacing, [1..1E5 m, R]
*
  ZBOTDR2 =  ! Level of drain tube bottom [-1000..0 cm, R, neg. below the soil surface]
  SWDTYP2 = 1 ! Type of drainage medium:
*               1 = Drain tube
*               2 = Open Channel
*
* In case SWDTYP2 = 2, specify date [day month] and channel water level
* [-1.E5..100 cm, R], maximum 366 records:
* dd mm LEVEL2
* End of table
***********************************************************************************************
* Section_4c: Drainage to level 3
*
 DRARES3 =  ! Drainage resistance [0..1.E5 d, R]
 INFRES3 =  ! Infiltration resistance [0..1.E5 d, R]
 SWALLO3 = 1 ! Switch, for allowance of drainage/infiltration
*                      1 = Drainage and infiltration are both allowed,
*                      2 = Drainage is not allowed,
*                      3 = Infiltration is not allowed.
*
* In case drainage fluxes should distributed vertically in the saturated zone
* zone (SWDIVD = 1 in *.SWA), specify the distance L between the drainage canals:
*
  L3 =  ! Drain spacing, [1..1E5 m, R]
*
  ZBOTDR3 =  ! Level of drain tube bottom [-1000..0 cm, R, neg. below the soil surface]
  SWDTYP3 = 1 ! Type of drainage medium:
*               1 = Drain tube
*               2 = Open Channel
*
* In case SWDTYP3 = 2, specify date [day month] and channel water level
* [-1.E5..100 cm, R], maximum 366 records:
* dd mm LEVEL3
* End of table
***********************************************************************************************
* Section_4d: Drainage to level 4
*
 DRARES4 =  ! Drainage resistance [0..1.E5 d, R]
 INFRES4 =  ! Infiltration resistance [0..1.E5 d, R]
 SWALLO4 = 1 ! Switch, for allowance of drainage/infiltration
*                      1 = Drainage and infiltration are both allowed,
*                      2 = Drainage is not allowed,
*                      3 = Infiltration is not allowed.
*
* In case drainage fluxes should distributed vertically in the saturated zone
* zone (SWDIVD = 1 in *.SWA), specify the distance L between the drainage canals:
*
  L4 =  ! Drain spacing, [1..1E5 m, R]
*
  ZBOTDR4 =  ! Level of drain tube bottom [-1000..0 cm, R, neg. below the soil surface]
  SWDTYP4 = 1 ! Type of drainage medium:
*               1 = Drain tube
*               2 = Open Channel
*
* In case SWDTYP4 = 2, specify date [day month] and channel water level
* [-1.E5..100 cm, R], maximum 366 records:
* dd mm LEVEL4
* End of table
***********************************************************************************************
* Section_4e: Drainage to level 4
*
 DRARES5 =  ! Drainage resistance [0..1.E5 d, R]
 INFRES5 =  ! Infiltration resistance [0..1.E5 d, R]
 SWALLO5 = 1 ! Switch, for allowance of drainage/infiltration
*                      1 = Drainage and infiltration are both allowed,
*                      2 = Drainage is not allowed,
*                      3 = Infiltration is not allowed.
*
* In case drainage fluxes should distributed vertically in the saturated zone
* zone (SWDIVD = 1 in *.SWA), specify the distance L between the drainage canals:
*
  L5 =  ! Drain spacing, [1..1E5 m, R]
*
  ZBOTDR5 =  ! Level of drain tube bottom [-1000..0 cm, R, neg. below the soil surface]
  SWDTYP5 = 1 ! Type of drainage medium:
*               1 = Drain tube
*               2 = Open Channel
*
* In case SWDTYP5 = 2, specify date [day month] and channel water level
* [-1.E5..100 cm, R], maximum 366 records:
* dd mm LEVEL5
* End of table
*
* End of file *********************************************************************************

  INFRES5 =          ! Infiltration resistance [0..1.E5 d, R]
  SWALLO5 =        1 ! Switch, for allowance of drainage/infiltration
*                 1 = Drainage and infiltration are both allowed,
*                 2 = Drainage is not allowed,
*                 3 = Infiltration is not allowed.
*
* In case drainage fluxes should distributed vertically in the saturated zone
* zone (SWDIVD = 1 in *.SWA), specify the distance L between the drainage canals:
*
  L5 =          ! Drain spacing, [1..1E5 m, R]
*
  ZBOTDR5 =          ! Level of drain tube bottom [-1000..0 cm, R, neg. below the soil surface]
  SWDTYP5 = 1 ! Type of drainage medium:
*               1 = Drain tube
*               2 = Open Channel
*
* In case SWDTYP5 = 2, specify date [day month] and channel water level 
* [-1.E5..100 cm, R], maximum 366 records:
* dd mm LEVEL5
* End of table
*
* End of file *********************************************************************************

{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# {title}\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["_Here you can document your model creation process. Good luck!_"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pyahc as ps\n\n", "metadata = ps.Metadata(\n", "{formatted_string}\n", ")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.8"}}, "nbformat": 4, "nbformat_minor": 4}